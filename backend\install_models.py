"""
script pour télécharger les modèles import nltk utilisés pour le scraping/parsing sur confluence
"""

import logging
import os

import nltk

if __name__ == "__main__":
    logging.basicConfig(level="INFO")
    logging.info("téléchargement de stopwords (NLTK_DATA: %s)...", os.environ.get("NLTK_DATA"))
    logging.debug("environnement: %s", os.environ.keys())
    nltk.download("stopwords")
    logging.info("téléchargement terminé.")
