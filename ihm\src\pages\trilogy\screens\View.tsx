import React from 'react'
import { Section, Text, Title, TitleLevels, View, Button } from '@bytel/trilogy-react-ts'
import { Justifiable, Alignable, VariantState } from '@bytel/trilogy-react-ts'

export const ViewScreen = (): JSX.Element => {
  return (
    <Section>
      <input type="checkbox" id={"checkbox_one"} />
      <View markup={"label"}>
        <Text>
    Click here to check the checkbox.</Text></View>
      <View markup={"span"}></View>
      <View markup={"div"} flexable justify={Justifiable.SPACE_BETWEEN} align={Alignable.ALIGNED_CENTER}>
        <Button variant={VariantState.TERTIARY}>CLick on me</Button>
        <Title level={TitleLevels.THREE}>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias culpa dolorum error et ex exercitationem,
          explicabo fugiat fugit, id impedit iste libero modi, molestiae nisi nobis quis sapiente ut voluptas?
        </Title>
      </View>
    </Section>
  )
}
