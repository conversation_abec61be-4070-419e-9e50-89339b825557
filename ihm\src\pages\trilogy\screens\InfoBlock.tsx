import React from 'react'
import {
  Section,
  Divider,
  Box,
  BoxContent,
  InfoBlock,
  InfoBlockHeader,
  InfoBlockContent,
  TextLevels,
  ButtonList,
  InfoBlockStatus,
  InfoBlockAction,
  Icon,
  IconName,
  IconColor,
  IconSize,
  Spacer,
  SpacerSize,
  Text,
  Button,
} from '@bytel/trilogy-react-ts'
import { TypographyAlign, TypographyBold, VariantState } from '@bytel/trilogy-react-ts'

export const InfoBlockScreen = (): JSX.Element => {
  return (
    <Section>
      <Box shadowless>
        <BoxContent>
          <InfoBlock>
            <InfoBlockHeader
              customIcon={<Icon name={IconName.UI_4G} color={IconColor.PRIMARY} size={IconSize.MEDIUM} />}
            >
              Infos Title
            </InfoBlockHeader>
            <InfoBlockContent>
              <Text level={TextLevels.TWO} typo={[TypographyBold.TEXT_WEIGHT_NORMAL, TypographyAlign.TEXT_CENTERED]}>
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. Autem enim ipsam iste maxime minus modi nihil,
                nisi officia perferendis porro, quam recusandae similique sint totam voluptate. Cupiditate, quos veniam!
                Quis!
              </Text>
            </InfoBlockContent>

            <ButtonList centered>
              <Button variant={VariantState.SECONDARY} onClick={() => console.log('toto')} fullwidth>
                CTA info
              </Button>
            </ButtonList>
          </InfoBlock>
        </BoxContent>
      </Box>
      <Spacer size={SpacerSize.LARGE} />
      <Divider />
      <Spacer size={SpacerSize.LARGE} />

      <Box shadowless>
        <BoxContent>
          <InfoBlock>
            <InfoBlockHeader status={InfoBlockStatus.SUCCESS}>Infos Title</InfoBlockHeader>
            <InfoBlockContent>
              <Text level={TextLevels.TWO} typo={[TypographyBold.TEXT_WEIGHT_NORMAL, TypographyAlign.TEXT_CENTERED]}>
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. Autem enim ipsam iste maxime minus modi nihil,
                nisi officia perferendis porro, quam recusandae similique sint totam voluptate. Cupiditate, quos veniam!
                Quis!
              </Text>
            </InfoBlockContent>

            <ButtonList centered>
              <Button variant={VariantState.SECONDARY} onClick={() => console.log('toto')} fullwidth>
                CTA info
              </Button>
            </ButtonList>
          </InfoBlock>
        </BoxContent>
      </Box>

      <Spacer size={SpacerSize.LARGE} />
      <Divider />
      <Spacer size={SpacerSize.LARGE} />

      <Box shadowless>
        <BoxContent>
          <InfoBlock>
            <InfoBlockHeader status={InfoBlockStatus.ERROR}>Infos Title</InfoBlockHeader>
            <InfoBlockContent>
              <Text level={TextLevels.TWO} typo={[TypographyBold.TEXT_WEIGHT_NORMAL, TypographyAlign.TEXT_CENTERED]}>
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. Autem enim ipsam iste maxime minus modi nihil,
                nisi officia perferendis porro, quam recusandae similique sint totam voluptate. Cupiditate, quos veniam!
                Quis!
              </Text>
            </InfoBlockContent>

            <ButtonList centered>
              <Button variant={VariantState.SECONDARY} onClick={() => console.log('toto')} fullwidth>
                CTA info
              </Button>
            </ButtonList>
          </InfoBlock>
        </BoxContent>
      </Box>

      <Spacer size={SpacerSize.LARGE} />
      <Divider />
      <Spacer size={SpacerSize.LARGE} />

      <Box shadowless>
        <BoxContent>
          <InfoBlock>
            <InfoBlockHeader status={InfoBlockStatus.SUCCESS}>Infos Title</InfoBlockHeader>
            <InfoBlockContent>
              <Text level={TextLevels.TWO} typo={[TypographyBold.TEXT_WEIGHT_NORMAL, TypographyAlign.TEXT_CENTERED]}>
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. Autem enim ipsam iste maxime minus modi nihil,
                nisi officia perferendis porro, quam recusandae similique sint totam voluptate. Cupiditate, quos veniam!
                Quis!
              </Text>
            </InfoBlockContent>

            <ButtonList centered>
              <Button variant={VariantState.SECONDARY} onClick={() => console.log('toto')} fullwidth>
                CTA info
              </Button>
            </ButtonList>
          </InfoBlock>
        </BoxContent>
      </Box>

      <Box shadowless>
        <BoxContent>
          <InfoBlock>
            <InfoBlockHeader
              status={InfoBlockStatus.WARNING}
              customIcon={<Icon name={IconName.UI_4G} color={IconColor.PRIMARY} size={IconSize.LARGE} />}
            >
              Infos Title
            </InfoBlockHeader>
            <InfoBlockContent>
              <Text level={TextLevels.TWO} typo={[TypographyBold.TEXT_WEIGHT_NORMAL, TypographyAlign.TEXT_CENTERED]}>
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. Autem enim ipsam iste maxime minus modi nihil,
                nisi officia perferendis porro, quam recusandae similique sint totam voluptate. Cupiditate, quos veniam!
                Quis!
              </Text>
            </InfoBlockContent>

            <ButtonList centered>
              <Button variant={VariantState.SECONDARY} onClick={() => console.log('toto')}>
                CTA info
              </Button>
            </ButtonList>
          </InfoBlock>
        </BoxContent>
      </Box>

      <InfoBlock boxed>
        <InfoBlockHeader status={InfoBlockStatus.WARNING}>Une erreur est survenue</InfoBlockHeader>
        <InfoBlockContent>
          <Text>La page à laquelle vous essayez accéder est momentanément indisponible</Text>
          <Text>Veuillez réessayer ultérieurement</Text>
        </InfoBlockContent>
        <InfoBlockAction>
          <Button variant={'PRIMARY'} onClick={() => alert('test')}>
            Button
          </Button>
        </InfoBlockAction>
      </InfoBlock>

      <Divider />

      <InfoBlock boxed>
        <InfoBlockHeader status={InfoBlockStatus.ERROR}>Une erreur est survenue</InfoBlockHeader>
        <InfoBlockContent>
          <Text>La page à laquelle vous essayez accéder est momentanément indisponible</Text>
          <Text>Veuillez réessayer ultérieurement</Text>
        </InfoBlockContent>
        <InfoBlockAction>
          <Button variant={'PRIMARY'} onClick={() => alert('test')}>
            Button
          </Button>
        </InfoBlockAction>
      </InfoBlock>

      <Divider />

      <InfoBlock boxed>
        <InfoBlockHeader status={InfoBlockStatus.SUCCESS}>Traitement effecté avec succès</InfoBlockHeader>
        <InfoBlockContent>
          <Text>Le message a été envoyé.</Text>
        </InfoBlockContent>
        <InfoBlockAction>
          <Button variant={'PRIMARY'} onClick={() => alert('test')}>
            Button
          </Button>
        </InfoBlockAction>
      </InfoBlock>
    </Section>
  )
}
