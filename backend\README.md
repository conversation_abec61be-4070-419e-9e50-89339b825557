# Dev Backend 

## introduction

Vous retrouverez dans ce dossier tout ce qui concerne le backend de l'application.

Pour plus de détails, vous pouvez vous référer à la documentation racine (voir readme à la racine du dépôt).

##  Structure

L'arborescence présentée est orientée `Domain Driven Design` avec une `architecture hexagonale` (*architecture en couche classique dans laquelle on fait apparaitre une couche métier*). 

Ce découpage permet ainsi de partir de la logique métier et au fur à mesure de l'adapter vers la techno cible que l'on va utiliser.

Ainsi, on peut facilement faire évoluer la logique métier ou la logique technique (manière de stocker/exposer les données) sans devoir tout retester.

Comme il s'agit d'une application directement lancée, on utilise un dossier `app` au lieu du traditionnel dossier `src` pour ranger les sources.

Ci-dessous une explication des différents modules.

### domains

ce module va contenir toute la couche métier, c'est à dire les différents domaines de l'application.

Pour chaque domaine, on doit retrouver les sous-modules suivants:

- `models` qui comprend les différents modèles de données du domaine (*l'objet métier lui-même mais aussi les Data Transfert Objects utilisés par les services*)

- `repository` les operations de persistances des modèles de données du domaine. il peut s'agir en général d'un simple CRUD (create, read, update, delete). Dans la couche domaine on va juste juste définir l'interface qui sera à implémenter pour les techno cible

- `services` qui comprend les services métier du domaine sans la moindre notion de comment celui est exposé par la suite (*api web, interface ligne de commande, protocol binaire développé pour l'occasion...*)

### repositories

ce module correspond à la couche d'adaption sur la manière de persister les données. C'est içi que l'on va implémenter les logique de lecture/écrite dans la technologie cible (postgresql, redis, aws S3...) avec les gestions de cache appropriées si nécessaire.

Pour l'instant on fait surtout du SQL, mais on est pas à l'abri d'avoir une persistance hybride avec du SQL et du S3 par la suite. 

### api

ce module correspond à la couche d'adaptation en services web des services du domaines que l'on va exposer. On va donc y retrouver toute la logique que doit implémenter une api web (*ex: authentification de l'appellant*)

### commons

ce module contient les éléments de logique qui sont ni une implémentation d'une techno spécifique, ni une logique domaine directe.

Typiquement, on y retrouve des éléments tels que
- la manière d'initialiser l'application avec sa configuration
- les classes d'erreurs réutilisables
- la gestion des logs

