import { BQuery } from "@bytel/query";
import { PersonneService } from "./personneService";
import { BackendService } from "./backendService";
import { WorkspaceService } from "./workspaceService";
import { RequirementService } from "./RequirementService";
import { GenerationService } from "./GenerationService";
import { AdminService } from "./adminService";
import { DocumentService } from "./documentService"; // <-- Ajout de l'import
import { FeedbackService } from "services/feedbackService"; // Import du service de feedback
import { UserService } from "services/userService";

export default class APIClient {
  personneService: PersonneService;
  backend: BackendService;
  workspaceService: WorkspaceService;
  requirementService: RequirementService;
  generationService: GenerationService;
  adminService: AdminService;
  documentService: DocumentService; // <-- Ajout de la propriété
  feedbackService: FeedbackService; // Déclaration du service de feedback
  userService: UserService; // Déclaration du service utilisateur

  constructor(bquery: BQuery) {
    this.personneService = new PersonneService(bquery);
    this.backend = new BackendService(bquery);
    this.workspaceService = new WorkspaceService(bquery);
    this.requirementService = new RequirementService(bquery);
    this.generationService = new GenerationService(bquery);
    this.adminService = new AdminService(bquery);
    this.documentService = new DocumentService(bquery); // <-- Initialisation du service
    this.feedbackService = new FeedbackService(bquery); // Initialisation du service de feedback
    this.userService = new UserService(bquery); // Initialisation du service utilisateur
  }
}
