import logging
import uuid

from fastapi import Depends, File, Response, UploadFile
from fastapi.routing import APIRouter

from app.api.security import ADMIN_AUTHENTIFICATION, CURRENT_USER, get_current_user
from app.common.bootstrap import APPLICATION
from app.common.exceptions import get_exception_responses
from app.domains.documents.models import DocumentInput, DocumentOutput, DocumentsOutput
from app.domains.users.service import UserAlreadyExistsException, UserCreateInput, UserNotFoundException, UserOutput, UsersOutput

router = APIRouter(prefix="/users", tags=["utilisateurs"])
logger = logging.getLogger(__name__)


@router.get("", response_model=UsersOutput, dependencies=[ADMIN_AUTHENTIFICATION])
def list_users():
    logger.info("Starting to list all users")
    try:
        result = APPLICATION.services.users.list_users()
        logger.info(f"Successfully listed {result.total} users")
        return result
    except Exception as e:
        logger.error(f"Error listing users: {str(e)}", exc_info=True)
        raise


@router.get("/search", responses=get_exception_responses(UserNotFoundException), response_model=UserOutput, dependencies=[ADMIN_AUTHENTIFICATION])
def search_user(login: str):
    logger.info(f"Starting search for user with login={login}")
    try:
        result = APPLICATION.services.users.get_user_by_login(login=login)
        logger.info(f"Successfully found user {login}")
        return result
    except UserNotFoundException:
        logger.warning(f"User not found: {login}")
        raise
    except Exception as e:
        logger.error(f"Error searching user {login}: {str(e)}", exc_info=True)
        raise


@router.post("/{login}/admin/make", status_code=204)
def make_admin(login: str, current_user: UserOutput = Depends(get_current_user)):
    logger.info(f"Starting to make user {login} admin")
    try:
        result = APPLICATION.services.users.make_admin(login, current_user.id)
        logger.info(f"Successfully made user {login} admin")
        return result
    except Exception as e:
        logger.error(f"Error making user {login} admin: {str(e)}", exc_info=True)
        raise


@router.post("/{login}/admin/remove", status_code=204)
async def remove_admin(login: str, current_user: UserOutput = Depends(get_current_user)):
    logger.info(f"Starting to remove admin privileges from user {login}")
    try:
        APPLICATION.services.users.remove_admin(login, current_user.id)
        logger.info(f"Successfully removed admin privileges from user {login}")
    except Exception as e:
        logger.error(f"Error removing admin privileges from user {login}: {str(e)}", exc_info=True)
        raise


@router.post("", response_model=UserOutput, responses=get_exception_responses(UserAlreadyExistsException), dependencies=[ADMIN_AUTHENTIFICATION])
def register_user(user: UserCreateInput):
    logger.info(f"Starting to register new user with login={user.login}")
    try:
        result = APPLICATION.services.users.register_user(user=user)
        logger.info(f"Successfully registered new user {user.login}")
        return result
    except UserAlreadyExistsException:
        logger.warning(f"User already exists: {user.login}")
        raise
    except Exception as e:
        logger.error(f"Error registering user {user.login}: {str(e)}", exc_info=True)
        raise


@router.get("/me", response_model=UserOutput)
async def me(current_user: UserOutput = CURRENT_USER) -> UserOutput:
    logger.info(f"Successfully processed user {current_user.id}")
    return current_user


@router.post("/upload", response_model=DocumentOutput)
async def upload_file(file: UploadFile = File(...), current_user: UserOutput = CURRENT_USER):
    logger.info(f"User {current_user.id} is uploading a file: {file.filename}")
    try:
        content = await file.read()
        document_input = DocumentInput(filename=file.filename, content=content, owner_id=current_user.id)
        result = APPLICATION.services.documents.save_document(document_input)
        logger.info(f"Successfully uploaded file '{file.filename}' for user {current_user.id}")
        return result
    except Exception as e:
        logger.error(f"Error uploading file for user {current_user.id}: {str(e)}", exc_info=True)
        raise


@router.get("/documents", response_model=DocumentsOutput)
async def list_user_documents(current_user: UserOutput = CURRENT_USER):
    logger.info(f"Retrieving documents for user {current_user.id}")
    try:
        result = APPLICATION.services.documents.get_user_documents(current_user.id)
        logger.info(f"Successfully retrieved {result.total} documents for user {current_user.id}")
        return result
    except Exception as e:
        logger.error(f"Error getting documents for user {current_user.id}: {str(e)}", exc_info=True)
        raise


@router.get("/documents/{document_id}/download")
async def download_document(document_id: uuid.UUID, current_user: UserOutput = CURRENT_USER):
    logger.info(f"User {current_user.id} is downloading document {document_id}")
    try:
        # Récupère le modèle de stockage pour avoir le contenu binaire
        document = APPLICATION.repos.documents.get(document_id)
        if not document:
            raise UserNotFoundException(f"Document {document_id} not found")
        filename = document.filename
        return Response(
            content=document.content, media_type="application/octet-stream", headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        logger.error(f"Error downloading document {document_id}: {str(e)}", exc_info=True)
        raise


@router.delete("/documents/{document_id}", status_code=204)
async def delete_document(document_id: uuid.UUID, current_user: UserOutput = CURRENT_USER):
    logger.info(f"User {current_user.id} is deleting document {document_id}")
    try:
        APPLICATION.services.documents.delete_document(document_id, current_user.id)
        logger.info(f"Successfully deleted document {document_id}")
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {str(e)}", exc_info=True)
        raise


@router.put("/documents/{document_id}", response_model=DocumentOutput)
async def update_document(document_id: uuid.UUID, file: UploadFile = File(...), current_user: UserOutput = CURRENT_USER):
    logger.info(f"User {current_user.id} is updating document {document_id}")
    content = await file.read()
    document_input = DocumentInput(filename=file.filename, content=content, owner_id=current_user.id)
    try:
        result = APPLICATION.services.documents.update_document(document_id, document_input, current_user.id)
        logger.info(f"Successfully updated document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error updating document {document_id}: {str(e)}", exc_info=True)
        raise
