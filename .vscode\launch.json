{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Dev local",
            "request": "launch",
            "type": "chrome",
            // "url": "http://localhost:5173",
            "runtimeArgs": [
                "http://localhost:8000",
                "http://localhost:5173"
            ],
            "preLaunchTask": "🎉 local env - start"
        },
        {
            "name": "Debug backend",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                // "run",
                // "app/main.py"
                "app.main:app",
                "--reload"
            ],
            "cwd": "${workspaceFolder}/backend",
            "jinja": true,
            "envFile": "${workspaceFolder}/.env",
        },
    ]
}