import * as React from "react";
import { getConfig, TypeConfig } from "../helpers/config";

export const ConfigContext = React.createContext<TypeConfig | null>(null);

export const ConfigProvider: React.FC<
  React.PropsWithChildren<{ mockConfig?: TypeConfig }>
> = ({ children, mockConfig }) => {
  const [CONFIG, setConfig] = React.useState<TypeConfig | undefined>(
    mockConfig,
  );

  React.useEffect(() => {
    if (!mockConfig) getConfig().then(setConfig);
  }, [mockConfig]);

  return CONFIG != null ? (
    <ConfigContext.Provider value={CONFIG}>{children}</ConfigContext.Provider>
  ) : (
    <></>
  );
};

export const useConfig = () => {
  return React.useContext(ConfigContext) as TypeConfig;
};
