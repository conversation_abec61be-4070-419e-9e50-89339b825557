# from app.common.exceptions import get_exception_responses
import logging
from uuid import UUID

from fastapi import APIRouter, Depends

from app.api.security import ADMIN_AUTHENTIFICATION, get_current_user
from app.common.bootstrap import APPLICATION
from app.domains.users.models import UserOutput
from app.domains.workspace.models import WorkspaceBase, WorkspaceCreateInput, WorkspaceOutput, WorkspaceStatusOutput

router = APIRouter(tags=["workspaces"])
logger = logging.getLogger(__name__)


@router.get("/me/workspaces")
def get_user_workspaces(current_user: UserOutput = Depends(get_current_user)):
    try:
        result = APPLICATION.services.workspaces.get_user_workspaces(current_user.id)
        logger.info(f"Successfully retrieved {len(result)} workspaces for user={current_user.id}")
        return result
    except Exception as e:
        logger.error(f"Error getting user workspaces: {str(e)}", exc_info=True)
        raise


@router.post("/workspaces", response_model=WorkspaceOutput)
def create_workspace(payload: WorkspaceBase, current_user: UserOutput = Depends(get_current_user)):
    # TODO, le WorkspaceCreateInput est a créer en récupérant le userid connecté
    logger.info(f"Creating new workspace: {payload.nom}")

    payload = WorkspaceCreateInput(**payload.model_dump(exclude={"owner_id"}), owner_id=current_user.id)

    logger.debug(f"Payload before creation: {payload}")
    try:
        result = APPLICATION.services.workspaces.create_workspace(payload)
        logger.info(f"Successfully created workspace with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Error creating workspace: {str(e)}", exc_info=True)
        raise


@router.get("/workspaces", dependencies=[ADMIN_AUTHENTIFICATION])
def list_workspaces(limit: int = 100) -> list[WorkspaceOutput]:
    logger.info(f"Listing workspaces with limit={limit}")
    try:
        result = APPLICATION.services.workspaces.get_workspaces(limit)
        logger.info(f"Retrieved {len(result)} workspaces")
        return result
    except Exception as e:
        logger.error(f"Error listing workspaces: {str(e)}", exc_info=True)
        raise


@router.get("/workspaces/{workspace_id}", response_model=WorkspaceOutput)
def get_workspace(workspace_id: UUID, current_user: UserOutput = Depends(get_current_user)) -> WorkspaceOutput:
    logger.info(f"Getting workspace with ID: {workspace_id}")
    try:
        result = APPLICATION.services.workspaces.get_workspace(workspace_id, current_user)
        logger.info(f"Successfully retrieved workspace: {workspace_id}")
        return result
    except Exception as e:
        logger.error(f"Error retrieving workspace {workspace_id}: {str(e)}", exc_info=True)
        raise


@router.get("/workspaces/{workspace_id}/isbusy", response_model=WorkspaceStatusOutput)
def get_workspace_busy(workspace_id: UUID) -> WorkspaceStatusOutput:
    logger.info(f"Checking workspace status for ID: {workspace_id}")
    try:
        result = APPLICATION.services.workspaces.get_workspace_status(workspace_id)
        logger.info(f"Successfully retrieved workspace status: {workspace_id}")
        return result
    except Exception as e:
        logger.error(f"Error retrieving workspace status {workspace_id}: {str(e)}", exc_info=True)
        raise
