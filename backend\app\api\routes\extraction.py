import logging

from fastapi import APIRouter

from app.api.security import USER_AUTHENTIFICATION
from app.common.bootstrap import APPLICATION
from app.common.exceptions import get_exception_responses
from app.domains.extraction.models import ExtractionDocumentationUnique
from app.externals.atlassian.confluence import EspaceConfluenceNonAutorise, PageIntrouvable

router = APIRouter(tags=["imports"])
logger = logging.getLogger(__name__)


@router.get(
    "/extraction/du",
    dependencies=[USER_AUTHENTIFICATION],
    response_model=ExtractionDocumentationUnique,
    responses=get_exception_responses(EspaceConfluenceNonAutorise, PageIntrouvable),
)
async def extraction_documentation_unique(pageId: str):
    """
    réalise une extraction depuis confluence des exigences contenues dans une documentation unique
    """
    return APPLICATION.services.extraction.extraire_exigence_documentation_unique(id_confluence=pageId)
