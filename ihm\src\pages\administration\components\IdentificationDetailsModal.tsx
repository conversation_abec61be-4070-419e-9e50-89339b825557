import React from "react";
import { Accordion,AccordionI<PERSON> ,AccordionHeader ,AccordionBody ,Button, Modal, Box, Text, Divider, <PERSON>dal<PERSON><PERSON>le, <PERSON>dal<PERSON>ooter, ModalMarkup,Title } from "@bytel/trilogy-react-ts";
import { DemandeIdentificationOutput } from "../../../types/identification";

interface IdentificationDetailsModalProps {
  identification: DemandeIdentificationOutput | null;
  isOpen: boolean;
  onClose: () => void;
  onCancelIdentification: (id: string) => void;
  formatDate: (dateString: string) => string;
  getStatusBadge: (status: string) => React.ReactNode;
  calculateDuration: (startDate: string, endDate: string)=> React.ReactNode;
}

const IdentificationDetailsModal: React.FC<IdentificationDetailsModalProps> = ({
  identification,
  isOpen,
  onClose,
  onCancelIdentification,
  formatDate,
  getStatusBadge,
  calculateDuration
}) => {
  if (!identification) return null;

  return (
    <Modal active={isOpen} triggerMarkup={ModalMarkup.BUTTON} closeIcon onClose={onClose} ctaContent="Fermer">
      <ModalTitle>Détails de l'identification</ModalTitle>
      <Divider />
      <div>
        {/* <Box className="mb-3">
          <Text >ID:</Text>
          <div>{identification.id || "N/A"}</div>
        </Box> */}
        <Box className="mb-3">    
          <table className="table">
            {/* <tr><td >ID:</td><td> {identification.id.slice(0, 8) + "..."  || "N/A"}</td></tr>  */}
            <tr><td >Date de demande :</td><td> {formatDate(identification.date_creation)}</td></tr> 
            <tr><td >Date fin demande :</td><td> {formatDate(identification.date_fin)}</td></tr>
            <tr><td >Dure de la demande :</td><td> 
              {identification.date_debut ? calculateDuration(identification.date_debut, identification.date_fin) : "N/A"}
              {!identification.date_fin && identification.date_debut && " (en cours)"}
            </td></tr>       
            <tr><td >Statut: </td><td> {getStatusBadge(identification.statut)}</td></tr>        
            <tr><td >Demandeur: </td><td> {identification.demandeur_name || identification.demandeur_id.slice(0, 8) + "..." || "N/A"}</td></tr> 
            <tr><td >Workspace: </td><td> {identification.workspace_name || identification.workspace_id.slice(0, 8) + "..." || "N/A"}</td></tr> 
            
            </table>{identification.stats && (
                        <>
            <Accordion>
              <AccordionItem id="ONE">
                      <AccordionHeader>
                        <Text >Stats :</Text>
                      </AccordionHeader>
                      <AccordionBody data-id="totooooo-test-id">
                        <table className="table">            
                          
                          <tr><td > Total Tokens: </td><td> {identification.stats.total_tokens || "N/A"}</td></tr>        
                          <tr><td > Prompt Tokens: </td><td> {identification.stats.prompt_tokens}</td></tr>        
                          <tr><td > Completion Tokens: </td><td> {identification.stats.completion_tokens}</td></tr> 
                          <tr><td > Reasoning Tokens: </td><td> {identification.stats.reasoning_tokens}</td></tr> 
                          <tr><td > Total Cost: </td><td> {identification.stats.total_cost} </td></tr>
                                                   
                        </table>
                      </AccordionBody>
              </AccordionItem>
        
            </Accordion>
          </>)} 
        </Box> 
        
        {identification.entrants && identification.entrants.exigences && (
          <Box className="mb-3">
            <Text >Exigences:</Text>
            <div className="mt-2">
              {identification.entrants.exigences.length > 0 ? (
                <ul className="list-group">
                  {identification.entrants.exigences.map((exigence, index) => (
                    <li key={index} className="list-group-item">
                      {exigence.id ? `${exigence.id}: ${exigence.texte || ""}` : JSON.stringify(exigence)}
                    </li>
                  ))}
                </ul>
              ) : (
                <span>Aucune exigence</span>
              )}
            </div>
          </Box>
        )}

        {identification.resultat && (
          <Box className="mb-3">
            <Text >Résultat:</Text>
            <pre
              style={{
                backgroundColor: "#f5f5f5",
                padding: "10px",
                borderRadius: "4px",
                maxHeight: "300px",
                overflowY: "auto",
                whiteSpace: "pre-wrap",
                fontSize: "13px",
              }}
            >
              {identification.resultat}
            </pre>
          </Box>
        )}
      </div>
      <ModalFooter className="modal-footer">
        <Button variant="SECONDARY" onClick={onClose}>
          Fermer
        </Button>
        {identification.statut === "EN_COURS" && (
          <Button
            variant="DANGER"
            onClick={() => {
              onClose();
              onCancelIdentification(identification.id);
            }}
          >
            Annuler cette identification
          </Button>
        )}
      </ModalFooter>
    </Modal>
  );
};

export default IdentificationDetailsModal;