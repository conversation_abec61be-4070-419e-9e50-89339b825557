import {
  Button,
  ButtonMarkup,
  InfoBlock,
  InfoBlockAction,
  InfoBlockContent,
  InfoBlockHeader,
  InfoBlockStatus,
  RowItem,
  Rows,
  Text,
  VariantState,
} from "@bytel/trilogy-react-ts";
import React from "react";
import { Link, useNavigate } from "react-router-dom";
import "./style.scss";

export const ErrorPage: React.FC<React.PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();

  return (
    <>
      <InfoBlock boxed>
        <InfoBlockHeader status={InfoBlockStatus.ERROR}>
          Une erreur est survenue
        </InfoBlockHeader>
        <InfoBlockContent>
          <Text>Veuillez réessayer ultérieurement</Text>
        </InfoBlockContent>
        <InfoBlockAction>
          <Rows>
            <RowItem>
              <Button
                variant={VariantState.PRIMARY}
                routerLink={Link}
                to="/"
                markup={ButtonMarkup.A}
              >
                {"Retour à l'accueil"}
              </Button>
            </RowItem>
            <RowItem>
              <Button
                variant={VariantState.PRIMARY}
                onClick={() => navigate(0)}
              >
                {"Rafraichir"}
              </Button>
            </RowItem>
          </Rows>
        </InfoBlockAction>
      </InfoBlock>
      {children}
    </>
  );
};

export default ErrorPage;
