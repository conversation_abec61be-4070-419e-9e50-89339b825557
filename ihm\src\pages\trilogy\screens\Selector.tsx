import * as React from 'react'
import { Section, AutoLayout, Selector, SelectorItem, SpacerSize } from '@bytel/trilogy-react-ts'
import { Alignable } from '@bytel/trilogy-react-ts'

export const SelectorView = (): JSX.Element => {
  return (
    <Section>
      <AutoLayout edgeSize={SpacerSize.MEDIUM} edges={['top', 'bottom']}>
        <Selector
          activeIndex={1}
          align={Alignable.ALIGNED_CENTER}
        >
          <SelectorItem>
            Selector One
          </SelectorItem>
          <SelectorItem>
            Selector Two
          </SelectorItem>
          <SelectorItem>
            Selector Three
          </SelectorItem>
        </Selector>
      </AutoLayout>
    </Section>
  )
}
