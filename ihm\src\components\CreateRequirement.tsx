import React from "react"
import { <PERSON>vider, Fab, Input, Textarea, Modal, Text, View, Select, SelectOption, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@bytel/trilogy-react-ts"


enum TypologieExigence { "langage naturel", "user story", "OpenAPI" }

type TypeExigence = keyof typeof TypologieExigence

const ChoixTypeExigence = ["langage naturel", "user story", "OpenAPI"]


export interface RequirementInput {
    ws_id: string;
    nom: string | undefined;
    description: string | undefined;
    type: TypeExigence | undefined;
    data: string | undefined;
}

interface CreateRequirementsProps {
    currentWorkspace: string
}


/**
 * Le formulaire affiché pour créer des exigences manuellement. WIP
 */
export const CreateRequirementsForm: React.FC<CreateRequirementsProps> = (props) => {

    const currentWorkspace = props.currentWorkspace

    const [newRequirement, setNewRequirement] = React.useState<RequirementInput>({
        ws_id: currentWorkspace,
        nom: undefined,
        description: undefined,
        type: undefined,
        data: undefined
    });


    return (
        <>
            <Text>{JSON.stringify(newRequirement)}</Text>
            <div id="createRequirementForm">
                <Input
                    placeholder="Nom de l'exigence"
                    type="text"
                    name="nom"
                    value={newRequirement.nom}
                    onChange={e => { setNewRequirement({ ...newRequirement, nom: e.inputValue }) }}
                />
                <Textarea
                    placeholder="Description"
                    value={newRequirement.description}
                    onChange={e => { setNewRequirement({ ...newRequirement, description: e.textareaValue }) }}
                />
                <Select label="Type d'exigence"
                    selected={newRequirement.type}
                    onChange={(e) => {
                        const value: string | undefined =
                            typeof e === 'string' || typeof e === 'number'
                                ? String(e)
                                : e.selectValue
                                    ? String(e.selectValue)
                                    : undefined
                        setNewRequirement({ ...newRequirement, type: value })
                    }}
                >
                    {ChoixTypeExigence.map((value) => (<SelectOption value={value}>{value}</SelectOption>))}
                </Select>
            </div>
        </>
    )
}

{/* <ModalTitle>Créer une nouvelle exigence</ModalTitle>
            <Divider />
            <div className="create-button-modal">
              <Input
                placeholder="Nom de l'exigence"
                type="text"
                name="nom"
                value={newRequirement.nom}
                onChange={handleInputChange}
              />
              <Textarea
                placeholder="Description"
                name="description"
                value={newRequirement.description}
                onChange={handleTextareaChange}
              />
              <Select
                label="Type"
                name="type"
                selected={newRequirement.type}
                onChange={handleSelectChange}
              >
                <SelectOption value="langage naturel">Langage naturel</SelectOption>
                <SelectOption value="user story">User Story</SelectOption>
                <SelectOption value="OpenAPI">OpenAPI</SelectOption>
              </Select>
              <Tabs activeIndex={activeTab}>
                <TabsItem active={activeTab === 0} onClick={() => handleTabChange(0)}>
                  <Title level={TitleLevels.THREE}>Saisie de texte</Title>
                </TabsItem>
                <TabsItem active={activeTab === 1} onClick={() => handleTabChange(1)}>
                  <Title level={TitleLevels.THREE}>Jointure de fichier</Title>
                </TabsItem>
              </Tabs>

              {activeTab === 0 && (
                <Textarea
                  placeholder="Mettez ici le contenu de votre exigence..."
                  name="data"
                  value={newRequirement.data}
                  onChange={handleTextareaChange}
                />
              )}
              {activeTab === 1 && (
                <div {...getRootProps()} className={`dropzone ${isDragActive ? 'active' : ''}`} style={{
                  border: '2px dashed #bbb', padding: '20px', borderRadius: '8px', textAlign: 'center', marginTop: '8px'
                }}>
                  <input {...getInputProps()} />
                  {fileName ? (
                    <Text>Fichier sélectionné : {fileName} ({(fileSize! / 1024).toFixed(2)} Ko)</Text>
                  ) : (
                    <Text>{isDragActive ? "Déposez le fichier ici..." : "Faites glisser un fichier ici, ou cliquez pour sélectionner un fichier"}</Text>
                  )}
                </div>
              )}
            </div>
            <ModalFooter className="modal-footer">
              <Button variant='SECONDARY' onClick={() => setIsModalActive(false)}>Annuler</Button>
              <Button variant='PRIMARY' onClick={handleCreateRequirement}>Créer</Button>
            </ModalFooter>
          </Modal> */}

export const CreateRequirementsFabModal: React.FC<CreateRequirementsProps> = (props) => {

    const [isOpen, setIsOpen] = React.useState<boolean>(false)

    const handleModalState = (wantedState: boolean) => {
        console.debug(`open state: ${isOpen}. wanted state: ${wantedState}`)
        setIsOpen(wantedState)
    }


    return (
        <div id="createRequirement">
            <Fab testId="createRequirementFab" iconName="tri-plus-circle" onClick={() => handleModalState(true)}>Ajouter une exigence</Fab>
            <Modal
                testId="createRequirementModal"
                active={isOpen}
                title={'Créer une nouvelle exigence'}
                onOpen={() => handleModalState(true)}
                disableHandlingClickOutside
                onClose={() => handleModalState(false)}
                closeIcon
                fullwidth
                className="is-large"
                footer
            >
                <View>
                    <Divider />
                    <CreateRequirementsForm currentWorkspace={props.currentWorkspace} />
                </View>
                 <ModalFooter className="modal-footer">
                    <Button variant='SECONDARY' onClick={() => handleModalState(false)}>Annuler</Button>
                    <Button variant='PRIMARY' onClick={() => alert("TODO")}>Créer</Button>
                </ModalFooter>
            </Modal>
        </div>
    )
}