import React from 'react'
import { Alert, Section, Spacer, TitleLevels, View, Title, Divider, AutoLayout } from '@bytel/trilogy-react-ts'
import { AlertState } from '@bytel/trilogy-react-ts'

export const AlertScreen = (): JSX.Element => {
  return (
    <Section>
      <View>
        {Object.values(AlertState).map((alert, index) => {
          return (
            <AutoLayout key={index}>
              <Title level={TitleLevels.TWO}>alert : {alert}</Title>
              <Spacer size={10} />
              <Alert
                display
                alert={alert}
                title={alert}
                description="Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.."
              />
              <Divider />
            </AutoLayout>
          )
        })}
      </View>
    </Section>
  )
}
