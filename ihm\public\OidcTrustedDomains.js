// BYTEL_KEEP_THIS_LINE v1.1.0 OidcTrustedDomains.js

// Add below trusted domains, access tokens will automatically injected to be send to
// trusted domain can also be a path like https://www.myapi.com/users,
// then all subroute like https://www.myapi.com/useers/1 will be authorized to send access_token to.

// Domains used by OIDC server must be also declared here
const domains = {
  oidcDomains: [
    /^https:\/\/[^/]+\.nbyt\.fr(:[^/]+)?\//,
    /^https:\/\/[^/]+\.(bpa|dolmen)\.bouyguestelecom\.fr\//,
    "https://oauth2.bouyguestelecom.fr/",
    "https://oauth2.sandbox.bouyguestelecom.fr/",
    "https://iam.bouyguestelecom.fr/",
    "https://iam.sandbox.bouyguestelecom.fr/",
    /^https?:\/\/localhost/
  ],
  accessTokenDomains: [
    /^https:\/\/[^/]+\.nbyt\.fr(:[^/]+)?\//,
    /^https:\/\/[^/]+\.(bpa|dolmen)\.bouyguestelecom\.fr\//,
    "https://api.bouyguestelecom.fr/",
    "https://api.sandbox.bouyguestelecom.fr/",
    "https://api.bouyguestelecom-entreprises.fr/",
    "https://api.sandbox.bouyguestelecom-entreprises.fr/",
    /^https?:\/\/localhost/
  ]
};
const trustedDomains = {
  default: domains,
  config_show_access_token: {
    ...domains,
    showAccessToken: true
  }
};
