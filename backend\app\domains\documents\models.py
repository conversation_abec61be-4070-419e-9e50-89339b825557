import uuid
from datetime import datetime

from pydantic import BaseModel
from sqlmodel import Field, SQLModel

# Modèle de base


class DocumentBase(BaseModel):
    """
    Le modèle de données pour les documents
    """

    filename: str


# Modèle de stockage (table)


class DocumentStorage(SQLModel, table=True):
    """
    Le modèle de données de la table documents
    """

    __tablename__ = "documents"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    filename: str = Field(index=True)
    content: bytes
    owner_id: uuid.UUID = Field(foreign_key="utilisateurs.id")
    date_upload: datetime = Field(default_factory=datetime.now)
    date_creation: datetime = Field(default_factory=datetime.now)
    date_maj: datetime | None = None


# DTOs (Data Transfer Objects)


class DocumentInput(BaseModel):
    """
    DTO pour l'ajout d'un document
    """

    filename: str
    content: bytes
    owner_id: uuid.UUID


class DocumentOutput(BaseModel):
    """
    Comment un document est vu depuis l'API
    """

    id: uuid.UUID
    filename: str
    owner_id: uuid.UUID
    date_upload: datetime
    date_creation: datetime | None
    date_maj: datetime | None
    size: int  # Ajouter la taille en bytes


class DocumentsOutput(BaseModel):
    """
    Comment les documents sont vus depuis l'API (liste)
    """

    total: int
    documents: list[DocumentOutput]


class DocumentFilter(BaseModel):
    """
    Le filtre de recherche des documents
    """

    owner_id: uuid.UUID | None = None
    filename: str | None = None
    document_ids: list[uuid.UUID] | None = None
