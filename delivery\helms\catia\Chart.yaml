apiVersion: v2
name: catia
# TODO:
# - voir si moyen d'alimenter depuis les tags gitlab ?
# - voir si la convention version du chart = version de l'application est utile ou non

version: 1.0.0-SNAPSHOT # version du chart
appVersion: "1.0.0-SNAPSHOT" # version de l'application
description: Déploiement kube pour CATIA

sources:
  - https://gitlab.int.nbyt.fr/outils-topsi/catia/catia

type: application

maintainers:
  - name: VACHETTE, Francis
    email: <EMAIL>

dependencies:
  - name: pull-secret-glouton
    version: 1.0.0
    repository: "https://glouton.int.nbyt.fr/artifactory/registry-helm"
  - name: webapp
    alias: ihm
    version: ~1
    repository: "https://glouton.int.nbyt.fr/artifactory/registry-helm"
  - name: webapp
    alias: apiweb
    version: ~1
    repository: "https://glouton.int.nbyt.fr/artifactory/registry-helm"
