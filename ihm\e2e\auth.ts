import { Page } from "@playwright/test";

export async function authenticate(page: Page, user: string = "helloworld-consultant") {
  await page.goto("/");

  await page.waitForURL(/https?:\/\/.+\/auth.*/);

  // Sésame
  // await page.getByPlaceholder("Login Windows").fill("testcdc");
  // await page.getByPlaceholder("Mot de passe Windows").fill("password");
  // await page.getByRole("button", { name: "Valider" }).click();

  // MIRO
  await page.getByLabel("Username").fill(user);
  await page.getByLabel("Password", { exact: true }).fill("password");
  await page.getByRole("button", { name: "Sign In" }).click();

  await page.waitForURL(/http:\/\/.+:5173\/(?!login).*/);
}
