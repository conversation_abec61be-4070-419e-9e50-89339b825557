import { render } from "@testing-library/react";
import React, { ReactElement } from "react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import { APIProvider } from "providers/api.tsx";
import { ConfigProvider } from "providers/config.tsx";
import { SnackbarContext, SnackbarProvider } from "providers/snackbar.tsx";
import { mockAPI, mockConfig, mockLogger, mockSnackbar } from "tests/init-mocks.tsx";
import { QueryClient, QueryClientProvider } from "react-query";
import { MockedOAuth2Provider } from "@bytel/react-oauth2";
import { LoggerProvider } from "providers/logger.tsx";

type OptionsMock =
  | {
      initialEntries?: string[];
    }
  | {
      params?: Record<string, string>;
    };
type OptionsMockProvider = {
  initialEntries?: string[];
} & {
  params?: Record<string, string>;
};

const MockedProviders: React.FC<React.PropsWithChildren<OptionsMockProvider>> = ({ children, initialEntries, params }) => {
  let MockedRouter: React.FC<React.PropsWithChildren> = ({ children }) => <MemoryRouter initialEntries={initialEntries}>{children}</MemoryRouter>;

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  if (params) {
    const entryWithParams = "/" + Object.values(params).join("/");
    const path =
      "/" +
      Object.keys(params)
        .map((x) => ":" + x)
        .join("/");
    MockedRouter = ({ children }) => (
      <MemoryRouter initialEntries={[entryWithParams]}>
        <Routes>
          <Route path={path} element={children} />
        </Routes>
      </MemoryRouter>
    );
  }
  return (
    <TestErrorBoundary>
      <MockedOAuth2Provider>
        <MockedRouter>
          <ConfigProvider mockConfig={mockConfig}>
            <LoggerProvider mockLogger={mockLogger}>
              <SnackbarProvider mockSnackbar={mockSnackbar} showStackTrace={true}>
                <APIProvider mockAPI={mockAPI}>
                  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
                </APIProvider>
              </SnackbarProvider>
            </LoggerProvider>
          </ConfigProvider>
        </MockedRouter>
      </MockedOAuth2Provider>
    </TestErrorBoundary>
  );
};

export class TestErrorBoundary extends React.Component<React.PropsWithChildren, { error: Error | null; errorInfo: React.ErrorInfo | null }> {
  static contextType = SnackbarContext;
  state: { error: Error | null; errorInfo: React.ErrorInfo | null } = {
    error: null,
    errorInfo: null,
  };

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ error, errorInfo });
  }

  render() {
    const { error } = this.state;
    if (error == null) {
      return <>{this.props.children}</>;
    }
    return (
      <>
        <div className="error-name">{error.name}</div>
        <div className="error-cause">{JSON.stringify(error.cause)}</div>
        <div className="error-stack">{error.stack}</div>
        <div className="error-message">{error.message}</div>
        {/*<div className='errorInfo'>{JSON.stringify(errorInfo)}</div>*/}
      </>
    );
  }
}

const customRender = (ui: ReactElement, options?: OptionsMock) =>
  render(ui, {
    wrapper: ({ children }) => <MockedProviders {...options}>{children}</MockedProviders>,
  });

export * from "@testing-library/react";
export { customRender };
