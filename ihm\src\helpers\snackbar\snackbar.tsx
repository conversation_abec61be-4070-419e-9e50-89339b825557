import { AlertStateValues, Notification } from "@bytel/trilogy-react-ts";
import React from "react";
import { useSnackbar } from "providers/snackbar";

export type Severity = "error" | "success" | "warning" | "info" | "hidden";

export const SnackBarHandler: React.FC<{ showStackTrace: boolean }> = ({ showStackTrace }) => {
  const { error, severity, message, hide, duration } = useSnackbar();

  if (severity == null || severity === "hidden") {
    return <></>;
  }
  return <Snackbar onClose={hide} open={message != null} severity={severity} message={message} stacktrace={showStackTrace ? error?.message : undefined} duration={duration} />;
};

const MAPPING_ERROR: { [k in Severity]: AlertStateValues } = {
  error: "ERROR",
  info: "INFO",
  warning: "WARNING",
  success: "SUCCESS",
  hidden: "INFO",
};

/**
 * Trilogy Snackbar
 * @param open Etat de la snackbar
 * @param onClose Fonction lors de la fermeture de la snackbar
 * @param severity Niveau d'alerte (defaut : info)
 * @param stacktrace Message de la stacktrace
 * @param message Message
 * @param anchorOrigin Position (defaut : bottom right)
 * @param duration Duration in ms before Notification is hidden, undefined for inifite (default: undefined)
 */
export const Snackbar: React.FC<{
  onClose: () => void;
  open?: boolean;
  severity?: Severity;
  message?: string;
  stacktrace?: string;
  duration: number | "infinite";
  anchorOrigin?: {
    horizontal?: "right" | "left";
    vertical?: "bottom" | "top";
  };
}> = ({
  open,
  onClose,
  severity = "info",
  stacktrace,
  message,
  anchorOrigin = {
    horizontal: "right",
    vertical: "bottom",
  },
  duration,
}) => {
  const style = React.useMemo<React.CSSProperties>(() => {
    const left = anchorOrigin.horizontal !== "left" ? "auto" : "24px";
    const right = anchorOrigin.horizontal !== "left" ? "24px" : "auto";
    const bottom = anchorOrigin.vertical !== "top" ? "50px" : undefined;
    const top = anchorOrigin.vertical !== "top" ? undefined : "24px";
    return {
      left,
      right,
      bottom,
      top,
      display: "flex",
      position: "absolute",
      zIndex: 100000,
    };
  }, [anchorOrigin.vertical, anchorOrigin.horizontal]);

  React.useEffect(() => {
    if (open && duration !== "infinite") {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [message, stacktrace, duration, open, onClose]);

  return open ? (
    <div className={"snackbar"} style={style}>
      <Notification info alert={MAPPING_ERROR[severity]} closable={onClose} title={message} description={stacktrace} />
    </div>
  ) : (
    <></>
  );
};
