import React from 'react'
import {
  Box,
  Container,
  Divider,
  Icon,
  IconColor,
  IconName,
  IconPosition,
  IconSize,
  IconStatus,
  IconStatusPosition,
  Link,
  Section,
  Text,
  Title,
  TitleLevels,
  View,
} from '@bytel/trilogy-react-ts'
import { Alignable } from '@bytel/trilogy-react-ts'

export const IconScreen = (): JSX.Element => {
  return (
    <Section>
      <Icon size={IconSize.HUGE} name={IconName.USER_COMMENT} />

      <Icon size={IconSize.HUGE} name={IconName.USER_HEART} />

      <Icon size={IconSize.HUGE} name={IconName.USER_LEGAL} />

      <Icon size={IconSize.HUGE} name={IconName.USER_LIGHTBULB} />

      <Icon size={IconSize.HUGE} name={IconName.CHAT_IA} />

      <Icon size={IconSize.HUGE} name={IconName.INSTALLER} />

      <Icon size={IconSize.LARGE} name={IconName.TWITTER} />

      <Icon size={IconSize.MEDIUM} name={IconName.TWITTER} />

      <Icon size={IconSize.SMALL} name={IconName.TWITTER} />

      <Icon size={IconSize.HUGE} name={IconName.USBKEY} />

      <Icon size={IconSize.HUGE} name={IconName.FILE_EURO} />

      <Icon size={IconSize.HUGE} name={IconName.PICTO_MABB} />

      <Icon name={IconName.WASTE_REDUCTION} color={IconColor.GREY} size={IconSize.SMALL} />

      <Icon size={IconSize.MEDIUM} name={IconName.ARROW_BOTTOM} />

      <Icon size={IconSize.MEDIUM} name={IconName.ARROW_RIGHT} />

      <Icon size={IconSize.MEDIUM} name={IconName.ARROW_LEFT} />

      <Icon size={IconSize.MEDIUM} name={IconName.ARROW_HIGH} />

      <Icon size={IconSize.MEDIUM} name={IconName.ARROW_DOWN} />

      <Icon size={IconSize.MEDIUM} name={IconName.BEETY} />

      <Icon size={IconSize.MEDIUM} name={IconName.CHECK_CIRCLE} />

      <Icon size={IconSize.MEDIUM} name={IconName.CHECK_DOUBLE} />

      <Icon size={IconSize.MEDIUM} name={IconName.CHECK} />

      <Icon size={IconSize.MEDIUM} name={IconName.TIMES} />

      <Icon size={IconSize.MEDIUM} name={IconName.TURKEY} />

      <Icon size={IconSize.MEDIUM} name={IconName.USBKEY} />

      <Text className={'has-text-primary'}>
        toto
        <Icon name={IconName.ACCOMODATION} />
      </Text>

      <Icon
        align={Alignable.ALIGNED_START}
        size={IconSize.MEDIUM}
        name={IconName.UI_4G}
        content={'Super texte'}
        position={IconPosition.UP}
      />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.BEETY} />

      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.SCAN_QR} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.ESIM_CHECK} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.ESIM_UNAVAILABLE} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.ESIM} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.LINES_SPEED} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.BOX_RADIO} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PHONE_MISSED} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PHONE_BLOCKED} />

      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.UI_4G} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.UI_4G} stretched={true} />
      <Icon align={Alignable.ALIGNED_START} name={IconName.UI_4G} />
      <Icon align={Alignable.ALIGNED_START} name={IconName.UI_4G} stretched={true} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.SMALL} name={IconName.UI_4G} stretched={true} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.MEDIUM} name={IconName.UI_4G} stretched={true} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.LARGE} name={IconName.UI_4G} stretched={true} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.SMALLER} name={IconName.UI_4G} stretched={true} />

      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PICTO_PAYMENT_VISA} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PICTO_PAYMENT_PAYPAL} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PICTO_PAYMENT_MASTERCARD} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PICTO_FACILITI} />
      <Icon align={Alignable.ALIGNED_START} size={IconSize.HUGE} name={IconName.PICTO_PAYMENT_AMERICANEXPRESS} />

      <Icon align={Alignable.ALIGNED_START} color={IconColor.WARNING} size={IconSize.LARGE} name={IconName.STAR} />
      <Icon align={Alignable.ALIGNED_CENTER} color={IconColor.WARNING} size={IconSize.LARGE} name={IconName.STAR} />
      <Icon align={Alignable.ALIGNED_END} color={IconColor.WARNING} size={IconSize.LARGE} name={IconName.STAR} />

      <Icon align={Alignable.ALIGNED_START} size={IconSize.SMALLER} name={IconName.UI_4G_5G} />
      <Icon align={Alignable.ALIGNED_CENTER} size={IconSize.SMALL} name={IconName.HOME} />
      <Icon align={Alignable.ALIGNED_CENTER} size={IconSize.MEDIUM} name={IconName.ARROW_RIGHT} />
      <Icon align={Alignable.ALIGNED_CENTER} size={IconSize.LARGE} name={IconName.FILE_PDF} />
      <Icon align={Alignable.ALIGNED_CENTER} size={IconSize.HUGE} name={IconName.STAR} />

      <Icon align={Alignable.ALIGNED_END} size={IconSize.LARGE} name={IconName.ARROW_RIGHT} />

      <Icon size={IconSize.LARGE} name={IconName.UI_4G} />

      <Box background='ERROR'>
        <Icon color={IconColor.WHITE} size={IconSize.LARGE} name={IconName.UI_4G} />
      </Box>

      <Icon color={IconColor.ERROR} circled size={IconSize.LARGE} name={IconName.UI_4G} />

      <Icon circled size={IconSize.LARGE} name={IconName.UI_4G} />

      <Title level={TitleLevels.THREE}>Icone avec texte, position et alignement vertical</Title>
      <Divider />

      <Container>
        <View>
          <Text>Icon UP</Text>
          <Icon content='Input + text' size={IconSize.LARGE} position={IconPosition.UP} name={IconName.UI_4G} />
        </View>
        <Divider />
        <View>
          <Text>Icon DOWN</Text>
          <Icon content='Input + text' size={IconSize.LARGE} position={IconPosition.DOWN} name={IconName.UI_4G} />
        </View>
        <Divider />
        <View>
          <Text>Icon LEFT</Text>
          <Icon content='Input + text' size={IconSize.LARGE} position={IconPosition.LEFT} name={IconName.UI_4G} />
        </View>
        <Divider />
        <View>
          <Text>Icon RIGHT</Text>
          <Icon content='Input + text' size={IconSize.LARGE} position={IconPosition.RIGHT} name={IconName.UI_4G} />
        </View>
        <Divider />
        <View>
          <Text>Aligné au centre</Text>
          <Icon
            content='Donec dui tellus, fermentum ut purus ac, commodo vestibulum enim. Fusce ut tortor in leo dictum maximus. Sed aliquam ante consequat turpis faucibus, id ultricies est vulputate. Nullam semper justo nulla, sed pulvinar urna tincidunt sagittis. Phasellus vitae erat massa. Fusce quis ultrices magna, eget varius nibh. Proin purus dui, lobortis vel diam sit amet, interdum vestibulum nunc.'
            size={IconSize.MEDIUM}
            verticalAlign={Alignable.ALIGNED_CENTER}
            name={IconName.UI_4G}
          />
        </View>
        <Divider />
        <View>
          <Text>Aligné en haut</Text>
          <Icon
            content='Donec dui tellus, fermentum ut purus ac, commodo vestibulum enim. Fusce ut tortor in leo dictum maximus. Sed aliquam ante consequat turpis faucibus, id ultricies est vulputate. Nullam semper justo nulla, sed pulvinar urna tincidunt sagittis. Phasellus vitae erat massa. Fusce quis ultrices magna, eget varius nibh. Proin purus dui, lobortis vel diam sit amet, interdum vestibulum nunc.'
            size={IconSize.MEDIUM}
            verticalAlign={Alignable.ALIGNED_START}
            name={IconName.UI_4G}
          />
        </View>
        <Divider />
        <View>
          <Text>Aligné en bas</Text>
          <Icon
            content='Donec dui tellus, fermentum ut purus ac, commodo vestibulum enim. Fusce ut tortor in leo dictum maximus. Sed aliquam ante consequat turpis faucibus, id ultricies est vulputate. Nullam semper justo nulla, sed pulvinar urna tincidunt sagittis. Phasellus vitae erat massa. Fusce quis ultrices magna, eget varius nibh. Proin purus dui, lobortis vel diam sit amet, interdum vestibulum nunc.'
            size={IconSize.MEDIUM}
            verticalAlign={Alignable.ALIGNED_END}
            name={IconName.UI_4G}
          />
        </View>

        <View>
          <Icon
            content={<Link href={'https://google.fr'}>My super link with href</Link>}
            size={IconSize.LARGE}
            position={IconPosition.LEFT}
            name={IconName.UI_4G}
          />
        </View>
      </Container>

      <Title level={TitleLevels.THREE}>Icone avec status</Title>
      <Divider />

      <Icon
        statusPosition={IconStatusPosition.TOP}
        status={IconStatus.SUCCESS}
        size={IconSize.MEDIUM}
        name={IconName.FILE_EURO}
      />

      <Icon
        statusPosition={IconStatusPosition.BOTTOM}
        status={IconStatus.SUCCESS}
        size={IconSize.MEDIUM}
        name={IconName.FILE_EURO}
      />
    </Section>
  )
}
