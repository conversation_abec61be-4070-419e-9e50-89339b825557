---
hide_table_of_contents: true
tags:
  - ADR:ouvert
  - framework
  - langage 
---

import {Annoter} from '@site/components'

## Contexte et problématique rencontré

La solution actuellement mise en place lors du POC s'appuie énormément sur l'écosystème python qui est au coeur des développement IA.

Les framework suivants ont étés utilisés:

- [streamlit](https://streamlit.io/) pour l'IHM qui est facilement remplacable 
- [langchain et langgraph](https://www.langchain.com/) pour les chaines de traitement en back

Les langages supportés officiellement côté Bytel sont `Java`, `Typescript` et `C#` qui ont leur framework interne dédié à disposition. 
De ce fait, python n'est pas officiellement supporté dans la stack technique, il existe des [paquets commun en python](https://gitlab.int.nbyt.fr/COMMUN/PYTHON) mais rien qui se rapproche d'un socle applicatif comme summer.

L'application étant avant tout dédié à l'utilisation d'outil via IA, il y a une crainte de ne pas pouvoir suivre les évolutions permanente du secteur de l'IA générative. 
En effet, langchain est actuellement le framework ayant le plus d'évolution et d'intégration en étant également particulièrement documentés.

A l'inverse, on voit des initiatives similaires apparaitre dans les communauté `Java` et `C#`, mais qui [sont loin d'avoir la vélocité de la communauté python](https://github.com/spring-projects/spring-ai/pull/2349). 
Il faut d'ailleurs noter que ces initiatives **manques de documentation** ce qui ne simplifie pas leur prise en main.

Quelques exemples:

- [langchain4j](https://github.com/langchain4j/langchain4j)
- [langgraph4j](https://github.com/bsorrentino/langgraph4j)
- [spring-ai](https://spring.io/projects/spring-ai)
- [Semantic Kernel](https://github.com/microsoft/semantic-kernel/)
- [Langchain .NET](https://github.com/tryAGI/LangChain)

Il y a donc un fossé à combler et différentes manière de s'y prendre.


:::note

le sujet ne concerne que les traitements en back puisque pour la partie IHM, il n'y a rien de spécifique à l'IA et donc la stack techno React/Typescript sera utilisée.

:::

## Solutions possibles


### Rester sur du python {#1}

On estime qu'en l'état actuel des innovations IA et de leur prominence dans l'écosystème python, 
il serait préférable de rester sur du python pour rapidement intégrer des nouveautés au fil de l'eau.

Cette situation pouvant durant un certain temps, cela signifie qu'il faut à l'inverse mettre en place les éléments nécessaire pour la stack Bytel.
On peut penser notamment aux logs vers prismes pour lesquels il n'y a pas de paquets commun bien que des ST tels [NSF](https://gitlab.int.nbyt.fr/NSF/nsf-back/-/tree/main/app/src/main/objets_unix) 
soit <Annoter details="note de Francis: ce code me fait mal aux yeux tellement il est dupliqué de partout, sans parler des données en dur">« développé »</Annoter> en python.

On peut également noter que côté [data ?](https://gitlab.int.nbyt.fr/BRAIN/), python est majoritairement utilisé mais aucune idée du socle commun si il existe

Ci-dessous un stack technique identifié pour l'application si elle est developpée en python (sans compter les développement spécifique Bytel)

| Framework | Description |
|-|-|
| [Pydantic](https://docs.pydantic.dev/latest/) | LA libraire de validation de données. Elle est utilisé partout et permet de faire de la sérialisation d'objet à la volée | 
| [Pydanc Settings](https://docs.pydantic.dev/latest/concepts/pydantic_settings/) | Extension de pydantic dédié à la configuration applicative avec gestion des secrets |
| [FastAPI](https://fastapi.tiangolo.com/) | framework alternatif à Flask qui offre beaucoup plus d'option et est utilisés dans énormément d'application existante | 
| [Langgraph/Langchain](https://github.com/langchain-ai/langgraph) | framework dédié à l'assemble de chaine de traitement autour de l'IA générative |
| [SQL Model](https://sqlmodel.tiangolo.com/)<br/>ou<br/>[SQLAlchemy](https://www.sqlalchemy.org/) | solution pour de l'Object-Relationnal Mapping.<br/>SQL Model est directement intégré avec Pydantic et fait le lien avec SQLAlchemy |
| [Alembic](https://alembic.sqlalchemy.org/en/latest/) | outil de migration de base de donnée | 
| [Apache AIRFLOW](https://airflow.apache.org/docs/apache-airflow/stable/index.html) | Orchestration de worfklow pour faire de l'ETL |



<table>
<thead>
<tr>
<th scope="col" style={{width:"50%"}}>Avantages</th>
<th scope="col" style={{width:"50%"}}>Inconvénients</th>
</tr>
</thead>
<tbody>
<tr>
<td>

- Garantie de pouvoir intégrer les dernières nouveautées 
- l'équipe a de l'expérience sur le langage
- les frameworks utilisés sont bien documentés

</td>
<td>

- Il va être nécessaire de mettre en place les éléments manquants de la stack Bytel en python
- A long terme, va demander de faire un portage vers une stack officiellement supportée

</td>
</tr>
</tbody>
</table>


### Basculer sur la stack Bytel Java {#2}

Au lieu de rester sur la base actuelle, on bascule sur du Java avec utilisation de SUMMER pour mettre en place le backend.
 
Pour la partie IA, il faudra choisir parmi:

- [langchain4j](https://github.com/langchain4j/langchain4j)
- [langgraph4j](https://github.com/bsorrentino/langgraph4j)
- [spring-ai](https://spring.io/projects/spring-ai)

Sachant que tout manque devra être implémenté/complété.

On peut noter que côté [TATIN](https://gitlab.int.nbyt.fr/TATIN/tatin/-/blob/main/back/libs/usecase/usecase-ai-generation/pom.xml), ces librairies ne sont pas utilisées au profit d'un développement d'un socle basé sur google-vertex-ai

<table>
<thead>
<tr>
<th scope="col" style={{width:"50%"}}>Avantages</th>
<th scope="col" style={{width:"50%"}}>Inconvénients</th>
</tr>
</thead>
<tbody>
<tr>
<td>

- Utilisation d'une stack technique entièrement craft compliant
- Occasion de contribuer à l'Open Source (boost l'image de marque)

</td>
<td>

- Occasion de contribuer à l'Open Source (devoir attendre des éternités pour que sa pull-request soit mergée)
- Devoir passer du temps développer/intégrer régulièrement les nouveautées réalisées par la communauté python qui itère beaucoup plus vite
- peu d'expérience de l'équipe

</td>
</tr>
</tbody>
</table>


## Solution choisie

:::warning

A DETERMINER EN SEANCE AVEC LES EQUIPES CRAFT

:::
