---
title: Contribuer au wiki

toc_max_heading_level: 5
---


## Introduction

Vous avez besoin d'écrire du contenu pour le wiki, mais vous ne savez pas comment vous y prendre ? Vous êtes au bon endroit !

Vous allez retrouver ci-dessous, différents exemples de syntaxes pour vous aider à rédiger du contenu pour le wiki.

Il y a deux types de syntaxes que vous pouvez utiliser pour rédiger du contenu pour le wiki : 

- le **markdown** (.md)
- le **markdown + jsx** (.mdx) qui permet d'ajouter des composants react

Pour des contenu simple, le markdown peut suffire. Cependant, le jsx vous apportera beaucoup plus de flexibilité pour la mise en forme de vos contenus et vous évitera de trop vous répéter.

Si vous avez besoin de voir des notions plus en détail, vous pouvez regarder la [documentation de docusaurus](https://docusaurus.io/docs/markdown-features).

## Syntaxe markdown (.md)

Les mise en forme les plus communes sont disponibles dans [à ce lien](https://commonmark.org/help/). 

Nous allons proposer ici des exemples plus complexes ou spécifique à docusaurus.


### Tableau

| Colonne A | Colonne B |
|-|-|
| valeur a | valeur b |
| valeur a | valeur b |

### Import d'une image

![Image](@site/static/logo.png)

### Bloc de code

```js
  console.log("Markdown features including the code block are available");
```

```jsx
function HelloCodeTitle(props) {
  return <h1>Hello, {props.name}</h1>;
}
```

```gherkin title="Scenario Gherkin"
  Scenario: Trying to login without credentials
      Given I am on the store homepage
        And I follow "Login"
       When I press "Login"
       Then I should be on login page
       # And I should see "Invalid credentials"
```
### Toggle

<details>
  <summary>Click me!</summary>

  This is the detailed content


  You can use Markdown here including **bold** and _italic_ text, and [inline link](https://docusaurus.io)
  <details>
    <summary>Nested toggle! Some surprise inside...</summary>

    😲😲😲😲😲
  </details>
</details>


### Diagrammes

Les diagrammes sont généré via la [syntaxe mermaid](https://mermaid.js.org/intro/syntax-reference.html)

:::warning

il y a un [bug](https://github.com/facebook/docusaurus/issues/8357) quand on rends les diagrammes mermaid en parallèle 
qui fait disparaitre les flèches. Pour éviter cela, il faut utiliser du Lazy Loading quand c'est possible (ex: \<Tabs lazy\>)

:::

```mermaid
---
config:
  look: handDrawn
  theme: neutral
---

graph TD;
    A-->B;
    A-->C;
    B-->D;
    C-->D;
```
<details>
<summary>diagramme de séquence</summary>
```mermaid
sequenceDiagram
    Alice-)John: See you later!
    John-->>Alice: Great!
    Alice-->>John: Hello John, how are you?

```
</details>

<details>
<summary>diagrammes C4</summary>


<Tabs lazy>
<TabItem value="c4-system-context" label="Contexte Sytème">

```mermaid
    C4Context
      title System Context diagram for Internet Banking System
      Enterprise_Boundary(b0, "BankBoundary0") {
        Person(customerA, "Banking Customer A", "A customer of the bank, with personal bank accounts.")
        Person(customerB, "Banking Customer B")
        Person_Ext(customerC, "Banking Customer C", "desc")

        Person(customerD, "Banking Customer D", "A customer of the bank, <br/> with personal bank accounts.")

        System(SystemAA, "Internet Banking System", "Allows customers to view information about their bank accounts, and make payments.")

        Enterprise_Boundary(b1, "BankBoundary") {

          SystemDb_Ext(SystemE, "Mainframe Banking System", "Stores all of the core banking information about customers, accounts, transactions, etc.")

          System_Boundary(b2, "BankBoundary2") {
            System(SystemA, "Banking System A")
            System(SystemB, "Banking System B", "A system of the bank, with personal bank accounts. next line.")
          }

          System_Ext(SystemC, "E-mail system", "The internal Microsoft Exchange e-mail system.")
          SystemDb(SystemD, "Banking System D Database", "A system of the bank, with personal bank accounts.")

          Boundary(b3, "BankBoundary3", "boundary") {
            SystemQueue(SystemF, "Banking System F Queue", "A system of the bank.")
            SystemQueue_Ext(SystemG, "Banking System G Queue", "A system of the bank, with personal bank accounts.")
          }
        }
      }

      BiRel(customerA, SystemAA, "Uses")
      BiRel(SystemAA, SystemE, "Uses")
      Rel(SystemAA, SystemC, "Sends e-mails", "SMTP")
      Rel(SystemC, customerA, "Sends e-mails to")

      UpdateElementStyle(customerA, $fontColor="red", $bgColor="grey", $borderColor="red")
      UpdateRelStyle(customerA, SystemAA, $textColor="blue", $lineColor="blue", $offsetX="5")
      UpdateRelStyle(SystemAA, SystemE, $textColor="blue", $lineColor="blue", $offsetY="-10")
      UpdateRelStyle(SystemAA, SystemC, $textColor="blue", $lineColor="blue", $offsetY="-40", $offsetX="-50")
      UpdateRelStyle(SystemC, customerA, $textColor="red", $lineColor="red", $offsetX="-50", $offsetY="20")

      UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="1")


```

</TabItem>
<TabItem value="c4-container" label="Conteneur">

```mermaid
    C4Container
    title Container diagram for Internet Banking System

    System_Ext(email_system, "E-Mail System", "The internal Microsoft Exchange system", $tags="v1.0")
    Person(customer, Customer, "A customer of the bank, with personal bank accounts", $tags="v1.0")

    Container_Boundary(c1, "Internet Banking") {
        Container(spa, "Single-Page App", "JavaScript, Angular", "Provides all the Internet banking functionality to customers via their web browser")
        Container_Ext(mobile_app, "Mobile App", "C#, Xamarin", "Provides a limited subset of the Internet banking functionality to customers via their mobile device")
        Container(web_app, "Web Application", "Java, Spring MVC", "Delivers the static content and the Internet banking SPA")
        ContainerDb(database, "Database", "SQL Database", "Stores user registration information, hashed auth credentials, access logs, etc.")
        ContainerDb_Ext(backend_api, "API Application", "Java, Docker Container", "Provides Internet banking functionality via API")

    }

    System_Ext(banking_system, "Mainframe Banking System", "Stores all of the core banking information about customers, accounts, transactions, etc.")

    Rel(customer, web_app, "Uses", "HTTPS")
    UpdateRelStyle(customer, web_app, $offsetY="60", $offsetX="90")
    Rel(customer, spa, "Uses", "HTTPS")
    UpdateRelStyle(customer, spa, $offsetY="-40")
    Rel(customer, mobile_app, "Uses")
    UpdateRelStyle(customer, mobile_app, $offsetY="-30")

    Rel(web_app, spa, "Delivers")
    UpdateRelStyle(web_app, spa, $offsetX="130")
    Rel(spa, backend_api, "Uses", "async, JSON/HTTPS")
    Rel(mobile_app, backend_api, "Uses", "async, JSON/HTTPS")
    Rel_Back(database, backend_api, "Reads from and writes to", "sync, JDBC")

    Rel(email_system, customer, "Sends e-mails to")
    UpdateRelStyle(email_system, customer, $offsetX="-45")
    Rel(backend_api, email_system, "Sends e-mails using", "sync, SMTP")
    UpdateRelStyle(backend_api, email_system, $offsetY="-60")
    Rel(backend_api, banking_system, "Uses", "sync/async, XML/HTTPS")
    UpdateRelStyle(backend_api, banking_system, $offsetY="-50", $offsetX="-140")


```

</TabItem>
<TabItem value="c4-component" label="Composant">

```mermaid
    C4Component
    title Component diagram for Internet Banking System - API Application

    Container(spa, "Single Page Application", "javascript and angular", "Provides all the internet banking functionality to customers via their web browser.")
    Container(ma, "Mobile App", "Xamarin", "Provides a limited subset to the internet banking functionality to customers via their mobile mobile device.")
    ContainerDb(db, "Database", "Relational Database Schema", "Stores user registration information, hashed authentication credentials, access logs, etc.")
    System_Ext(mbs, "Mainframe Banking System", "Stores all of the core banking information about customers, accounts, transactions, etc.")

    Container_Boundary(api, "API Application") {
        Component(sign, "Sign In Controller", "MVC Rest Controller", "Allows users to sign in to the internet banking system")
        Component(accounts, "Accounts Summary Controller", "MVC Rest Controller", "Provides customers with a summary of their bank accounts")
        Component(security, "Security Component", "Spring Bean", "Provides functionality related to singing in, changing passwords, etc.")
        Component(mbsfacade, "Mainframe Banking System Facade", "Spring Bean", "A facade onto the mainframe banking system.")

        Rel(sign, security, "Uses")
        Rel(accounts, mbsfacade, "Uses")
        Rel(security, db, "Read & write to", "JDBC")
        Rel(mbsfacade, mbs, "Uses", "XML/HTTPS")
    }

    Rel_Back(spa, sign, "Uses", "JSON/HTTPS")
    Rel(spa, accounts, "Uses", "JSON/HTTPS")

    Rel(ma, sign, "Uses", "JSON/HTTPS")
    Rel(ma, accounts, "Uses", "JSON/HTTPS")

    UpdateRelStyle(spa, sign, $offsetY="-40")
    UpdateRelStyle(spa, accounts, $offsetX="40", $offsetY="40")

    UpdateRelStyle(ma, sign, $offsetX="-90", $offsetY="40")
    UpdateRelStyle(ma, accounts, $offsetY="-40")

        UpdateRelStyle(sign, security, $offsetX="-160", $offsetY="10")
        UpdateRelStyle(accounts, mbsfacade, $offsetX="140", $offsetY="10")
        UpdateRelStyle(security, db, $offsetY="-40")
        UpdateRelStyle(mbsfacade, mbs, $offsetY="-40")


```

</TabItem>

<TabItem value="c4-dynamic" label="Dynamique">

Correspond à un séquencement. Bref, une autre vision du diagramme de séquence UML


```mermaid
    C4Dynamic
    title Dynamic diagram for Internet Banking System - API Application

    ContainerDb(c4, "Database", "Relational Database Schema", "Stores user registration information, hashed authentication credentials, access logs, etc.")
    Container(c1, "Single-Page Application", "JavaScript and Angular", "Provides all of the Internet banking functionality to customers via their web browser.")
    Container_Boundary(b, "API Application") {
      Component(c3, "Security Component", "Spring Bean", "Provides functionality Related to signing in, changing passwords, etc.")
      Component(c2, "Sign In Controller", "Spring MVC Rest Controller", "Allows users to sign in to the Internet Banking System.")
    }
    Rel(c1, c2, "Submits credentials to", "JSON/HTTPS")
    Rel(c2, c3, "Calls isAuthenticated() on")
    Rel(c3, c4, "select * from users where username = ?", "JDBC")

    UpdateRelStyle(c1, c2, $textColor="red", $offsetY="-40")
    UpdateRelStyle(c2, c3, $textColor="red", $offsetX="-40", $offsetY="60")
    UpdateRelStyle(c3, c4, $textColor="red", $offsetY="-40", $offsetX="10")


```

</TabItem>

<TabItem value="c4-deploiement" label="Deploiement">

```mermaid
    C4Deployment
    title Deployment Diagram for Internet Banking System - Live

    Deployment_Node(mob, "Customer's mobile device", "Apple IOS or Android"){
        Container(mobile, "Mobile App", "Xamarin", "Provides a limited subset of the Internet Banking functionality to customers via their mobile device.")
    }

    Deployment_Node(comp, "Customer's computer", "Microsoft Windows or Apple macOS"){
        Deployment_Node(browser, "Web Browser", "Google Chrome, Mozilla Firefox,<br/> Apple Safari or Microsoft Edge"){
            Container(spa, "Single Page Application", "JavaScript and Angular", "Provides all of the Internet Banking functionality to customers via their web browser.")
        }
    }

    Deployment_Node(plc, "Big Bank plc", "Big Bank plc data center"){
        Deployment_Node(dn, "bigbank-api*** x8", "Ubuntu 16.04 LTS"){
            Deployment_Node(apache, "Apache Tomcat", "Apache Tomcat 8.x"){
                Container(api, "API Application", "Java and Spring MVC", "Provides Internet Banking functionality via a JSON/HTTPS API.")
            }
        }
        Deployment_Node(bb2, "bigbank-web*** x4", "Ubuntu 16.04 LTS"){
            Deployment_Node(apache2, "Apache Tomcat", "Apache Tomcat 8.x"){
                Container(web, "Web Application", "Java and Spring MVC", "Delivers the static content and the Internet Banking single page application.")
            }
        }
        Deployment_Node(bigbankdb01, "bigbank-db01", "Ubuntu 16.04 LTS"){
            Deployment_Node(oracle, "Oracle - Primary", "Oracle 12c"){
                ContainerDb(db, "Database", "Relational Database Schema", "Stores user registration information, hashed authentication credentials, access logs, etc.")
            }
        }
        Deployment_Node(bigbankdb02, "bigbank-db02", "Ubuntu 16.04 LTS") {
            Deployment_Node(oracle2, "Oracle - Secondary", "Oracle 12c") {
                ContainerDb(db2, "Database", "Relational Database Schema", "Stores user registration information, hashed authentication credentials, access logs, etc.")
            }
        }
    }

    Rel(mobile, api, "Makes API calls to", "json/HTTPS")
    Rel(spa, api, "Makes API calls to", "json/HTTPS")
    Rel_U(web, spa, "Delivers to the customer's web browser")
    Rel(api, db, "Reads from and writes to", "JDBC")
    Rel(api, db2, "Reads from and writes to", "JDBC")
    Rel_R(db, db2, "Replicates data to")

    UpdateRelStyle(spa, api, $offsetY="-40")
    UpdateRelStyle(web, spa, $offsetY="-40")
    UpdateRelStyle(api, db, $offsetY="-20", $offsetX="5")
    UpdateRelStyle(api, db2, $offsetX="-40", $offsetY="-20")
    UpdateRelStyle(db, db2, $offsetY="-10")


```

</TabItem>

</Tabs>

</details>

<details>
<summary>architecture (beta)</summary>

```mermaid
architecture-beta
    group api(cloud)[API]

    service db(database)[Database] in api
    service disk1(disk)[Storage] in api
    service disk2(disk)[Storage] in api
    service server(server)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db

```

Voir avec les équipes bytel-docs pour ajouter des packs d'icones [issue github potentiellement utile](https://github.com/facebook/docusaurus/discussions/10508)

```mermaid
architecture-beta
    group api(logos:aws-lambda)[API]

    service db(logos:aws-aurora)[Database] in api
    service disk1(logos:aws-glacier)[Storage] in api
    service disk2(logos:aws-s3)[Storage] in api
    service server(logos:aws-ec2)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db

```

</details>

### Admonitions

Ne fais pas partie de la syntaxe markdown standard. Il s'agit d'un ajout spécifique à docusaurus

:::note

Some **content** with _Markdown_ `syntax`. Check [this `api`](#).

:::

:::tip

Some **content** with _Markdown_ `syntax`. Check [this `api`](#).

:::

:::info

Some **content** with _Markdown_ `syntax`. Check [this `api`](#).

:::

:::warning

Some **content** with _Markdown_ `syntax`. Check [this `api`](#).

:::

:::danger

Some **content** with _Markdown_ `syntax`. Check [this `api`](#).

:::


## Syntaxe MarkDown + JSX (.mdx)

### Importer des composants

Pour utiliser un composant, il vous faudra l'importer. Ci-dessous les import réalisés sur cette page.

```js title="import des composants de la page"
// composant standard issus de docusaurus
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import CodeBlock from '@theme/CodeBlock';

// composant custom
import {Annoter} from '@site/components'

// utilisation raw-loader pour récupérer des fichiers liés au code source
import MyComponentSource from '!!raw-loader!@site/../backend/main.tsx';
import MyBackSource from '!!raw-loader!@site/../backend/app/main.py';
import MyCucumberSpecification from '!!raw-loader!@site/../backend/tests/exemple.feature';
import MyOtherSpec from '!!raw-loader!@site/../backend/tests/prismjs.feature';
```


import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import TOCInline from '@theme/TOCInline';
import CodeBlock from '@theme/CodeBlock';
import {Annoter} from '@site/components'
import MyComponentSource from '!!raw-loader!@site/../ihm/src/main.tsx';
import MyBackSource from '!!raw-loader!@site/../backend/app/main.py';
import MyCucumberSpecification from '!!raw-loader!@site/../backend/tests/exemple.feature';
import MyOtherSpec from '!!raw-loader!@site/../backend/tests/prismjs.feature';

### Tabs

Autre alternative au toggle, les tabs permettent également de regrouper du contenu.

<Tabs>
<TabItem value="js" label="JavaScript">

```js
function helloWorld() {
  console.log('Hello, world!');
}
```

</TabItem>
<TabItem value="py" label="Python">

```py
def hello_world():
  print("Hello, world!")
```

</TabItem>
<TabItem value="java" label="Java">

```java
class HelloWorld {
  public static void main(String args[]) {
    System.out.println("Hello, World");
  }
}
```

</TabItem>
</Tabs>



### Définir des variables

La puissance du mdx réside dans le fait que vous pouvez définir des variables que vous pourrez réutiliser un peu partout.

Pratique si besoin de mettre à jour automatiquement la documentation par la suite.


```js
export const now = new Date()

il est {now.getHours()}h{now.getMinutes()}.
Plus précisément, la date actuelle correspond au  {now.toLocaleString()}
```

export const now = new Date()

il est {now.getHours()}h.
Plus précisément, la date actuelle correspond au  {now.toLocaleString()}



### Afficher du code source

Il est possible d'afficher directement des sources du dépôt git, ce qui permet de faire le lien avec le code ✨

Pour cela, on utilise le plugin `raw-loader` pour récupérer le contenu du fichier et CodeBlock pour en afficher le contenu mis en forme.

```jsx
// utilisation raw-loader pour récupérer des fichiers liés au code source
import MyComponentSource from '!!raw-loader!@site/../ihm/src/main.tsx';
import MyBackSource from '!!raw-loader!@site/../backend/app/main.py';
import MyCucumberSpecification from '!!raw-loader!@site/../backend/tests/exemple.feature';
```

<CodeBlock language="jsx" title="main.tsx">{MyComponentSource}</CodeBlock>
<CodeBlock language="python" title="main.py">{MyBackSource}</CodeBlock>

<CodeBlock language="Gherkin" title="exemple.feature">{MyCucumberSpecification}</CodeBlock>


### Annoter

import AnnoterSource from '!!raw-loader!@site/components.js';

<Annoter details={<CodeBlock language="jsx">{AnnoterSource}</CodeBlock>}>Annoter</Annoter> est composant custom qui a rajouté pour permettre d'accéder à du contexte supplémentaire sans surcharger la page.

On peut le voir comme une alternative aux toggles

```jsx
export const STBL = <Annoter details="Spécifications Techniques des Besoins Logiciel">STBL</Annoter>

Mon texte peut contenir des définitions tels que {STBL} que ...
```

export const STBL = <Annoter details="Spécifications Techniques des Besoins Logiciel">STBL</Annoter>

Mon texte peut contenir des définitions tels que {STBL} que je vais vouloir expliquer sans en faire des caisses visuellement.
