import React from 'react'
import { Columns, ColumnsItem, Select, SelectOption, Tabs, TabsItem, Divider, View, Text } from '@bytel/trilogy-react-ts'
import { TypographyAlign, TrilogyColor } from '@bytel/trilogy-react-ts'

export const ToolbarScreen = (): JSX.Element => {
  return (
    <View color={'WHITE'}>
      <Columns marginless className={'is-aligned-center'}>
        <ColumnsItem size={1}>
          <Text typo={TypographyAlign.TEXT_CENTERED}>Ligne :</Text>
        </ColumnsItem>
        <ColumnsItem size={3}>
          <Select
            label='Choisir une option'
            onFocus={(e) => console.log('OUVERT', e)}
            onBlur={(e) => console.log('FERMÉ', e)}
            onChange={(e) => console.log(e)}
            iconName='tri-advisor'
          >
            <SelectOption iconName='tri-advisor' id='id_one' value='opt_one' label='option1'>
              option 1
            </SelectOption>
            <SelectOption iconName='tri-advisor' id='id_two' value='opt_two' label='option2'>
              option 2
            </SelectOption>
            <SelectOption iconName='tri-advisor' disabled id='id_three' value='opt_three' label='option3'>
              option 3
            </SelectOption>
          </Select>
        </ColumnsItem>
        <ColumnsItem>
          <Tabs shadowless>
            <TabsItem>Tab 1</TabsItem>
            <TabsItem>Tab 2</TabsItem>
            <TabsItem>Tab 3</TabsItem>
          </Tabs>
        </ColumnsItem>
      </Columns>
      <Divider color={TrilogyColor.GREY_LIGHT} marginless />
    </View>
  )
}
