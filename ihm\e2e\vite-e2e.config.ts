/// <reference types="vitest" />
import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react-swc";
import viteTsconfigPaths from "vite-tsconfig-paths";
import { DynamicPublicDirectory } from "vite-multiple-assets";

const publicDir = process.env.CI ? "e2e/public-ci" : "e2e/public-local";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), viteTsconfigPaths(), DynamicPublicDirectory(["public", publicDir])],
  publicDir,
  server: {
    host: "0.0.0.0",
    proxy: {
      "/api": {
        target: "http://localhost:3000",
      },
    },
  },
});
