import logging

from app.common.exceptions import InvalidContentException, NotFoundException
from app.domains.extraction.models import ExtractionDocumentationUnique
from app.domains.extraction.repositories import DocumentsUniquesRepositoryInterface
from app.externals.atlassian.scraping import PageDocumentUnique, exigences_pages_du
from atlassian import Confluence

LOGGER = logging.getLogger(__name__)


class EspaceConfluenceNonAutorise(InvalidContentException):
    message = "la page demandée ne fait pas partie des espaces confluence autorisés"


class PageIntrouvable(NotFoundException):
    message = "la page confluence demandée est introuvable ou inaccessible"


class ConfluenceDocumentsUniques(DocumentsUniquesRepositoryInterface):
    """
    implementation de l'extraction des documentations uniques stockés dans confluence.
    TODO: voir si besoin d'intégrer une gestion de cache si on commence à avoir des abus ou des sujets de perf.
    """

    ESPACES_CONFLUENCES: list[str] = ["LUE"]

    def __init__(self, confluence_client: Confluence, official_url: str):
        LOGGER.debug("initialisation client confluence")
        self.confluence = confluence_client
        self.official_url = official_url
        LOGGER.debug("validation de l'accès du token aux espaces confluences nécessaires à l'extraction des DU (%s)", self.ESPACES_CONFLUENCES)
        try:
            self._valider_token()
        except:
            LOGGER.error("token invalide pour la récupération de documentations unique depuis confluence")
            raise
        LOGGER.info("initialisation client confluence réussie")

    def _valider_token(self):
        for espace in self.ESPACES_CONFLUENCES:
            item = self.confluence.get_space(space_key=espace)
            LOGGER.info("espace confluence %s accessible", item.get("name"))

    def extraire_exigences(self, id_document_unique) -> ExtractionDocumentationUnique:
        """
        réalise l'extraction brute de l'ensemble des pages liées à la documentation unique fournies.
        une fois cette extraction brute réalisée, un scraping/parsing est réalisé pour obtenir les exigences
        """
        LOGGER.info("lancement d'une extraction d'exigence pour l'identifiant de page confluence %s", id_document_unique)
        pages_documentation_unique: list[PageDocumentUnique] = []
        main_page = self._extraction_page(id_document_unique)
        pages_documentation_unique.append(main_page)
        LOGGER.info("récupération des pages de la documentation unique %s (%s)", main_page.title, main_page.id)
        pages_du = self._extraction_recursive(main_page)
        pages_documentation_unique.extend(pages_du)
        LOGGER.info("récupération des pages de la documentation unique terminée (%i pages)", len(pages_documentation_unique))
        exigences_du, erreurs_du = exigences_pages_du(pages_documentation_unique)
        extraction = ExtractionDocumentationUnique(
            id=main_page.id,
            nom=main_page.title,
            url=main_page.url,
            pages_extraites=len(pages_documentation_unique),
            exigences=exigences_du,
            erreurs=erreurs_du,
        )
        return extraction

    def _extraction_recursive(
        self, page_parent: PageDocumentUnique, extracted_pages: list | None = None, pages_to_check: dict[str, bool] | None = None
    ) -> list[PageDocumentUnique]:
        """
        réalise de manière récursive une extraction des pages confluence à partir des pages parents fournies en entrées
        """
        if extracted_pages is None:
            extracted_pages = []
        if pages_to_check is None:
            pages_to_check = {}

        LOGGER.info("recherche récursive de page à partir de la page parent (%s - %s)", page_parent.id, page_parent.title)
        childrens = self.confluence.get_child_pages(page_id=page_parent.id)
        current_extraction = []
        for child in childrens:
            page_doc = self._extraction_page(child["id"])
            current_extraction.append(page_doc)
            pages_to_check[page_doc.id] = True

        extracted_pages.extend(current_extraction)
        pages_to_check[page_parent.id] = False
        LOGGER.info("%i pages enfants extraites.", len(current_extraction))

        if len(current_extraction) > 0:
            LOGGER.info("de nouvelles page ont étés découvertes, lancement de l'extraction recursive sur ces pages")
            for page in current_extraction:
                self._extraction_recursive(page_parent=page, extracted_pages=extracted_pages, pages_to_check=pages_to_check)

        LOGGER.debug("etat extraction: %s", pages_to_check)
        return extracted_pages

    def _extraction_page(self, page_id: str) -> PageDocumentUnique:
        """
        réalise l'extraction et conversion d'une page confluence en page document unique
        """
        try:
            page_confluence = self.confluence.get_page_by_id(page_id, expand="body.storage,space")
        except Exception as error:
            LOGGER.error(error)
            raise PageIntrouvable(identifier=page_id)
        if page_confluence["space"]["key"] not in self.ESPACES_CONFLUENCES:
            raise EspaceConfluenceNonAutorise(error_reason=f": espace {page_confluence['space']['key']} non autorisé.")
        url = f"{self.official_url}{page_confluence['_links']['tinyui']}"
        return PageDocumentUnique(id=page_id, content=page_confluence["body"]["storage"]["value"], title=page_confluence["title"], url=url)
