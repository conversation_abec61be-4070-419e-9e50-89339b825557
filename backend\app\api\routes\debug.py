import logging
from datetime import datetime
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, File, Response, UploadFile, status

from app.api.security import ADMIN_AUTHENTIFICATION, USER_AUTHENTIFICATION
from app.common.bootstrap import APPLICATION
from app.common.exceptions import BaseIdentifiedException
from app.domains.bouchons.models import BouchonsApiFiltre, BouchonsApiInput, BouchonsApiOutput
from app.domains.documents.models import DocumentFilter, DocumentInput, DocumentOutput, DocumentsOutput
from app.domains.exigences.models import ExigenceCreateInput, ExigenceOutput
from app.domains.feedbacks.models import FeedbackCreate, FeedbackInput, FeedbackOutput
from app.domains.identification.models import DemandeIdentificationFiltre, DemandeIdentificationInput, DemandeIdentificationOutput
from app.domains.testcases.models import TestCaseFilter, TestCaseInput, TestCaseOutput
from app.domains.users.models import User<PERSON><PERSON><PERSON>nput, User<PERSON>utput, <PERSON>Output, UserStorage
from app.domains.workspace.models import Workspace<PERSON>reateInput, WorkspaceOutput

router = APIRouter(tags=["debug"], prefix="/debug")
logger = logging.getLogger(__name__)


async def get_local_env_user() -> UserOutput:
    """
    Retourne toujours le premier utilisateur trouvé dans la persistance utilisée (memory, database..)
    ou le mock par défaut si il n'y a encore aucun utilisateur enregistré
    """
    DEFAULT_DEBUG_LOGIN = "catia-user"

    logger.info("LOCAL ENV: récupération du premier user disponible")
    try:
        return APPLICATION.services.users.get_user_by_login(DEFAULT_DEBUG_LOGIN)
    except:
        logger.info("pas de user encore créé, création du user par defaut à la volée.")
        created = APPLICATION.repos.users.add(
            UserStorage(id=UUID("00000000-0000-0000-0000-000000000000"), login=DEFAULT_DEBUG_LOGIN, admin=True, date_inscription=datetime.now())
        )
        return created


@router.get("/public")
def debug_public_endpoint():
    logger.info("message INFO")
    logger.debug("message DEBUG")
    logger.warning("message WARNING")
    logger.error("message ERROR")
    logger.critical("message CRITICAL")
    return {"message": "Ce endpoint est public"}


@router.get("/expected")
def debug_raise_handled_error():
    raise BaseIdentifiedException(identifier="test-erreur-0")


@router.get("/unexpected")
def debug_raise_unhandled_error():
    raise ValueError("I don't work and panic !")


@router.get("/extraction/du")
async def debug_extraction_documentation_unique(pageId: str = "379004168"):
    return APPLICATION.services.extraction.extraire_exigence_documentation_unique(id_confluence=pageId)


@router.get("/private", dependencies=[USER_AUTHENTIFICATION])
def debug_private_endpoint():
    return {"message": "Ce endpoint est privé"}


@router.get("/admin", dependencies=[ADMIN_AUTHENTIFICATION])
def debug_admin_endpoint():
    return {"message": "ce endpoint est réservé aux administrateurs"}


@router.get("/users/{id}", response_model=UserOutput)
def debug_get_user(id: UUID):
    return APPLICATION.repos.users.get(id)


@router.get("/users", response_model=UsersOutput)
def debug_list_users():
    return APPLICATION.services.users.list_users()


@router.post("/users", response_model=UserOutput)
def debug_register_user(user: UserCreateInput):
    return APPLICATION.services.users.register_user(user)


@router.get("/me")
async def debug_moi(current_user: UserOutput = Depends(get_local_env_user)) -> UserOutput:
    return current_user


@router.post("/{login}/admin/make", status_code=204)
def make_admin_dirty(login: str):
    logger.info(f"Starting to make user {login} admin")
    try:
        result = APPLICATION.services.users.dirty_update(login)
        logger.info(f"Successfully made user {login} admin")
        return result
    except Exception as e:
        logger.error(f"Error making user {login} admin: {str(e)}", exc_info=True)
        raise


@router.get("/me/workspaces", response_model=list[WorkspaceOutput])
def debug_mes_workspaces(current_user: UserOutput = Depends(get_local_env_user)):
    return APPLICATION.services.workspaces.get_user_workspaces(current_user.id)


@router.get("/me/exigences", response_model=list[ExigenceOutput])
async def debug_mes_exigences(current_user: UserOutput = Depends(get_local_env_user)):
    return APPLICATION.services.exigences.get_exigence_by_user(current_user.id)


@router.get("/me/identifications", response_model=list[DemandeIdentificationOutput])
def debug_mes_identifications(current_user: UserOutput = Depends(get_local_env_user)):
    return APPLICATION.repos.identifications.find(DemandeIdentificationFiltre(demandeur_id=current_user.id))


@router.get("/me/testcases", response_model=list[TestCaseOutput])
async def debug_mes_testcase(current_user: UserOutput = Depends(get_local_env_user)):
    exigences = [exigence.id for exigence in APPLICATION.services.exigences.get_exigence_by_user(current_user.id)]
    return APPLICATION.repos.testcases.find(TestCaseFilter(exigences_ids=exigences))


@router.get("/me/bouchons", response_model=list[BouchonsApiOutput])
async def debug_mes_bouchons(current_user: UserOutput = Depends(get_local_env_user)):
    exigences = [exigence.id for exigence in APPLICATION.services.exigences.get_exigence_by_user(current_user.id)]
    return APPLICATION.repos.bouchons.find(BouchonsApiFiltre(exigences_ids=exigences))


@router.get("/workspaces")
def debug_list_workspaces(limit: int = 100) -> list[WorkspaceOutput]:
    return APPLICATION.services.workspaces.get_workspaces(limit)


@router.get("/workspaces/{id}", response_model=WorkspaceOutput)
def debug_get_workspace(id: UUID) -> WorkspaceOutput:
    return APPLICATION.repos.workspaces.get(id)


@router.post("/workspaces", response_model=WorkspaceOutput)
def debug_add_workspace(payload: WorkspaceCreateInput):
    return APPLICATION.services.workspaces.create_workspace(payload)


@router.get("/exigences", response_model=list[ExigenceOutput])
def debug_get_exigences(limit: int = 100):
    return APPLICATION.repos.exigences.list(limit=limit)


@router.get("/exigences/{id}", response_model=TestCaseOutput)
def debug_get_exigence(id: UUID):
    return APPLICATION.repos.exigences.get(id)


@router.post("/exigences", response_model=ExigenceOutput, status_code=status.HTTP_201_CREATED)
def debug_add_exigence(exigence: ExigenceCreateInput):
    return APPLICATION.services.exigences.create_exigence(exigence)


@router.get("/testcases", response_model=list[TestCaseOutput])
def debug_list_testcases(limit: int = 100):
    return APPLICATION.repos.testcases.list(limit=limit)


@router.get("/testcases/{id}", response_model=TestCaseOutput)
def debug_get_testcase(id: UUID):
    return APPLICATION.repos.testcases.get(id)


@router.post("/testcases", status_code=status.HTTP_204_NO_CONTENT)
def debug_create_testcase(testcase: TestCaseInput) -> None:
    APPLICATION.services.testcases.add_generated_testcases(testcase)


@router.post("/bouchons", status_code=status.HTTP_204_NO_CONTENT)
def debug_creer_bouchons(bouchons_api: BouchonsApiInput) -> None:
    return APPLICATION.services.bouchons.ajouter_bouchons(bouchons_api)


@router.get("/identifications", response_model=list[DemandeIdentificationOutput])
def debug_list_identifications(limit: int = 100):
    return APPLICATION.repos.identifications.list(limit=limit)


@router.post("/identifications")
def debug_demander_identification(
    demande: DemandeIdentificationInput, background_tasks: BackgroundTasks, current_user=Depends(get_local_env_user)
) -> DemandeIdentificationOutput:
    demande_cree: DemandeIdentificationOutput = APPLICATION.services.identifications.creer_demande(dto=demande, demandeur_id=current_user.id)
    background_tasks.add_task(APPLICATION.services.identifications.identifier, demande_cree.id)
    return demande_cree


@router.get("/documents", response_model=DocumentsOutput)
def debug_list_documents(limit: int = 100):
    """Liste tous les documents dans le système"""
    return APPLICATION.services.documents.list_documents(limit)


@router.get("/documents/{id}", response_model=DocumentOutput)
def debug_get_document(id: UUID):
    """Récupère un document par son ID"""
    return APPLICATION.services.documents.get_document(id)


@router.get("/documents/{id}/download")
def debug_download_document(id: UUID):
    """Télécharge le contenu d'un document par son ID"""
    filename, content = APPLICATION.services.documents.get_document(id)
    return Response(content=content, media_type="application/octet-stream", headers={"Content-Disposition": f"attachment; filename={filename}"})


@router.post("/documents", response_model=DocumentOutput)
async def debug_upload_document(file: UploadFile = File(...), current_user: UserOutput = Depends(get_local_env_user)):
    """Upload un document en mode debug"""
    content = await file.read()
    document_input = DocumentInput(filename=file.filename, content=content, owner_id=current_user.id)
    return APPLICATION.services.documents.save_document(document_input)


@router.delete("/documents/{id}", status_code=204)
def debug_delete_document(id: UUID, current_user: UserOutput = Depends(get_local_env_user)):
    """Supprime un document (mode admin/debug)"""
    APPLICATION.services.documents.delete_document(id, current_user.id)


@router.get("/me/documents", response_model=DocumentsOutput)
def debug_my_documents(current_user: UserOutput = Depends(get_local_env_user)):
    """Liste les documents de l'utilisateur courant"""
    return APPLICATION.services.documents.get_user_documents(current_user.id)


@router.get("/documents/search", response_model=DocumentsOutput)
def debug_search_documents(filename: str = None, owner_id: UUID = None):
    """Recherche des documents selon des critères"""
    filter = DocumentFilter(filename=filename, owner_id=owner_id)
    docs = APPLICATION.services.documents.list_documents().documents
    filtered = [doc for doc in docs if (not filename or filename.lower() in doc.filename.lower()) and (not owner_id or doc.owner_id == owner_id)]
    return DocumentsOutput(total=len(filtered), documents=filtered)


@router.put("/documents/{id}", response_model=DocumentOutput)
async def debug_update_document(id: UUID, file: UploadFile = File(...), current_user: UserOutput = Depends(get_local_env_user)):
    """Met à jour un document (mode debug)"""
    content = await file.read()
    document_input = DocumentInput(filename=file.filename, content=content, owner_id=current_user.id)
    return APPLICATION.services.documents.update_document(id, document_input, current_user.id)


@router.get("/documents/{id}/parse")
def debug_parse_document(id: UUID):
    """Parse le contenu d'un document selon son type (PDF, DOCX, etc.)"""
    return APPLICATION.services.documents.get_parse_documents(id)


@router.post("/feedbacks", response_model=FeedbackOutput, status_code=status.HTTP_201_CREATED)
def debug_create_feedback(feedback: FeedbackInput, current_user: UserOutput = Depends(get_local_env_user)) -> FeedbackOutput:
    logger.info(f"Debug: creating feedback from user={current_user.id}")
    try:
        feedback_with_user = FeedbackCreate(**feedback.model_dump(), user_id=current_user.id)
        result = APPLICATION.services.feedbacks.submit_feedback(feedback_with_user)
        logger.info(f"Debug: successfully created feedback with id={result.id}")
        return result
    except Exception as e:
        logger.error(f"Debug: error creating feedback: {str(e)}", exc_info=True)
        raise
