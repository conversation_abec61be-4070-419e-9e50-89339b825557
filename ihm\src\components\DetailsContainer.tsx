import React, { useState } from 'react';
import { Generation,Bouchon } from 'services/interfaces/generationInterfaces';
import { Requirement } from 'services/interfaces/requirementInterfaces';
import { <PERSON><PERSON>, <PERSON><PERSON>Title, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>, Divider } from '@bytel/trilogy-react-ts';
import GenerationsContainer from './GenerationsContainer';
import GenerationDetailContainer from './GenerationDetailContainer';
import BouchonsContainer from './BouchonsContainer';

interface DetailsContainerProps {
  TestCasesactiveTabIndex: number;
  activeTabIndex: number;
  setActiveTabIndex: React.Dispatch<React.SetStateAction<number>>;
  generations: Generation[];
  bouchons: Bouchon[];
  filteredGenerations: Generation[];
  handleFilterClick: (status: string | null) => void;
  handleGenerationClick: (generationId: string) => void;
  handleRequirementClick: (index: number) => void;
  handleDetailsClick: (requirementId?: number) => void;
  handleGenerateClick: (requirementId?: number) => void;
  activeRequirement: number | null;
  activeGeneration: Generation | null;
  handleValidate: (generation_id: string) => void;
  handleReject: (generation_id: string) => void;
  handleDownload: (generation_id: string) => void;
  
}

const DetailsContainer: React.FC<DetailsContainerProps> = ({
  TestCasesactiveTabIndex,
  activeTabIndex,
  setActiveTabIndex,
  generations,
  bouchons,
  filteredGenerations,
  handleFilterClick,
  handleGenerationClick, 
  activeRequirement,
  activeGeneration,
  handleValidate,
  handleReject,
  handleDownload,
  
}) => {
  const [isModalActive, setIsModalActive] = useState(false);

  const openModal = () => setIsModalActive(true);
  const closeModal = () => setIsModalActive(false);
  
  return (<>    
        {activeRequirement !== null && 
        generations.length > 0 && 
        TestCasesactiveTabIndex === 0 && (
          
          <GenerationsContainer 
            activeTabIndex={activeTabIndex}
            setActiveTabIndex={setActiveTabIndex}
            generations={generations}
            filteredGenerations={filteredGenerations}
            handleFilterClick={handleFilterClick}
            handleGenerationClick={(generationId) => {
              handleGenerationClick(generationId);
              openModal(); 
            }}
          />
        )}
      {activeRequirement !== null && 
        generations.length > 0 && 
        TestCasesactiveTabIndex === 1 && (
          
      <BouchonsContainer
              activeRequirement={activeRequirement}
              bouchons={bouchons}
            />
        )}
        
      {/* Modal pour GenerationDetailContainer */}
      {activeGeneration && (
        <Modal className='modal-large' active={isModalActive} closeIcon onClose={closeModal} ctaContent="Fermer">
          <ModalTitle>Détails de la Génération</ModalTitle>
          <Divider />
          <GenerationDetailContainer 
            activeGeneration={activeGeneration}
            handleValidate={handleValidate}
            handleReject={handleReject}
            handleDownload={handleDownload}
            onClose={closeModal}
          />
        </Modal>
      )}
    </>
  );
};

export default DetailsContainer;
