from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field

from app.common.exceptions import ErreurScraping
from app.domains.exigences.models import ExigenceBase, FormatExigence


class TypeSource(Enum):
    """
    les types de sources de données qui sont supportées par le service d'extraction
    """

    DOCUMENT_UNIQUE = "Documentation Unique"
    CONFLUENCE = "page confluence"
    # PARCOURS_VIDEO = "video parcours utilisateur"


class PageConfluence(BaseModel):
    """
    les extractions réalisée depuis une page confluence
    """

    type: TypeSource = TypeSource.CONFLUENCE
    id_confluence: str = Field(description="l'identifiant de page confluence. on n'extrait uniquement depuis cette page")
    methode: Literal["titre", "tableau"] = Field(description="la méthode d'extraction appliquée sur la page")


class ExigenceDocumentUnique(ExigenceBase):
    type: FormatExigence = FormatExigence.TEXTE_LIBRE
    porteur: str = Field(description="l'acteur en charge de réaliser cette exigence")
    metadonnees: dict = Field(description="contient des informations sur la page confluence d'où a été extraite l'exigence")


class ErreurExtractionDocumentUnique(BaseModel):
    """
    représente une erreur rencontrée durant l'extraction d'une documentation unique
    """

    id: str = Field(description="l'identifiant de la page confluence d'où provient l'erreur")
    url: str = Field(description="le lien de la page confluence d'où provient l'erreur")
    message: str = Field(description="le message d'erreur rencontré durant l'extraction")
    details: ErreurScraping | None = Field(None, description="le détail de l'erreur et le contenu associé. N'est alimenté qu'en mode debug.")


class ExtractionDocumentationUnique(BaseModel):
    """
    les extractions réalisées depuis une Doc Unique de l'espace confluence Livrable Unique Etude
    """

    type: TypeSource = TypeSource.DOCUMENT_UNIQUE
    id: str = Field(
        description="""
        l'identifiant de la page confluence contenant le DU.
        Si le DU a plusieurs pages sous l'arborescence, il s'agit de la page de plus haut niveau."""
    )
    nom: str = Field(description="le nom de la documentation unique")
    url: str = Field(description="le lien de page confluence indiquée")
    pages_extraites: int = Field(description="le nombre de page qui ont été parcouru durant l'extraction")
    exigences: list[ExigenceDocumentUnique] = Field(description="la liste d'exigences récupérés depuis les documents extraits")
    erreurs: list[ErreurExtractionDocumentUnique] = Field(
        description="les erreurs rencontrées sur le document unique qui peuvent être corrigés ou non par l'utilisateur"
    )
