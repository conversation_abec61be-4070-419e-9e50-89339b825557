import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { Providers } from "providers";
import { Router } from "Router.tsx";
import { TrilogyProvider } from "@bytel/trilogy-react-ts";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <TrilogyProvider injectTrilogyAssets>
      <BrowserRouter>
        <Providers>
          <Router />
        </Providers>
      </BrowserRouter>
    </TrilogyProvider>
  </React.StrictMode>,
);
