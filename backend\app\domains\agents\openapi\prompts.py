"""
les prompts utilisés pour générer des cas de test
"""

from langchain_core.prompts import PromptTemplate

PROMPT_IDENTIFICATION_TEST = PromptTemplate(
    template="""
    Tu es un expert en tests logiciel.
    Tu es un assistant spécialisé dans la définition de tests d'API web.
    À partir de la SPECIFICATION OPEN API ci-dessous et de la DOCUMENTATION associée, identifie les cas de test

    SPECIFICATION OPEN API: {open_api_specification}

    DOCUMENTATION: {documentation}
    
    """,
    input_variables=["open_api_specification", "documentation"],
)

PROMPT_ECRITURE_TEST = PromptTemplate(
    template="""
    Tu es un expert en écriture en implémentation de tests logiciel.
    À partir de la SPECIFICATION OPEN API ci-dessous et du CAS DE TESTS associé, écris le CAS DE TEST dans le FORMAT demandé.
    réponds uniquement avec le FORMAT demandé. PAS DE MARKDOWN

    SPECIFICATION OPEN API: {open_api_specification}

    CAS DE TEST: {test}
    
    FORMAT: {instructions}
    """,
    input_variables=["open_api_specification", "test", "instructions"],
)


# region: prompts liés à la génération de bouchons

# le prompt pour identifier les exemples d'appels à réaliser

IDENTIFIER_BOUCHONS_PROMPT = PromptTemplate(
    template=""" Tu es un assistant spécialisé dans la création de mock d'API web.
    À partir de la SPECIFICATION OPEN API ci-dessous et de la DOCUMENTATION associée, identifie **tous** les exemples d'appels API et de réponses, y compris tous les codes d'erreur possibles.

    **Important :** Assure-toi de ne pas omettre d'exemples, même s'ils semblent redondants ou peu fréquents.
    Si certaines réponses, y compris les codes d'erreur (comme 400, 401, 403, 404, 405, 500, 502, 503...), ne sont pas définies dans la SPECIFICATION OPEN API mais le sont dans la DOCUMENTATION, génère-les également.

    SPECIFICATION OPEN API: {open_api_specification}

    DOCUMENTATION: {documentation}
    
    """,
    input_variables=["open_api_specification", "documentation"],
)

# le prompt utilisé lorsqu'il manque dans l'identification des exemples d'appel par rapport à la documentations OAS

COMPLETER_BOUCHONS_PROMPT = PromptTemplate(
    template="""Tu es un assistant spécialisé dans la création de mock d'API web.

    À partir de la SPECIFICATION OPEN API ci-dessous et des ATTENDUS associée, identifie les exemples d'appels API et de réponses attendues.

    SPECIFICATION OPEN API: {open_api_specification}

    ATTENDUS: {attendus}
    """,
    input_variables=["open_api_specification", "attendus"],
)


# le prompt utilisé pour écrire le format de sortie des mocks/bouchons pour les exemples d'appel identifiés
GENERER_FORMAT_BOUCHON_PROMPT = PromptTemplate(
    template="""Tu es un assistant spécialisé dans la création de mock d'API web.
    Voici ci-dessous, une liste d'EXEMPLES d'appel à un service WEB.

    EXEMPLES: {exemples}
    
    {instructions}
    """,
    input_variables=["exemples", "instructions"],
)

# endregion
