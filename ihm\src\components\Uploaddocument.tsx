import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Divider,
  Text,
} from '@bytel/trilogy-react-ts';
import { FaCloudUploadAlt } from "react-icons/fa";
import { useAPIClient } from "providers/api";
import { useSnackbar } from "providers/snackbar";
import { useMutation, useQueryClient } from "react-query";
import { AxiosError } from "axios";
import { useDropzone, FileRejection } from 'react-dropzone';

interface UploadDocumentProps {
  buttonClassName?: string;
  buttonStyle?: React.CSSProperties;
  onSuccess?: () => void;
}

const UploadDocument: React.FC<UploadDocumentProps> = ({
  buttonClassName = '',
  buttonStyle = {},
  onSuccess
}) => {
  const { showError, showInfo } = useSnackbar();
  const apiClient = useAPIClient();
  const queryClient = useQueryClient();

  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [fileToUpload, setFileToUpload] = useState<File | null>(null);

  // Le callback onDrop gère la sélection et la validation des fichiers
  const onDrop = useCallback((acceptedFiles: File[], fileRejections: FileRejection[]) => {
    if (fileRejections.length > 0) {
      setFileToUpload(null);
      const errors = fileRejections[0].errors;
      if (errors.find(e => e.code === 'file-too-large')) {
        showError(null, { message: "Le fichier ne doit pas dépasser 100 Mo." });
      } else if (errors.find(e => e.code === 'file-invalid-type')) {
        showError(null, { message: "Type de fichier non valide. Seuls les .docx, .txt, .pdf et .xlsx sont acceptés." });
      }
      return;
    }

    if (acceptedFiles && acceptedFiles.length > 0) {
      setFileToUpload(acceptedFiles[0]);
    }
  }, [showError]);

  // Configuration de useDropzone avec les contraintes de validation
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false, // N'accepter qu'un seul fichier
    maxSize: 100 * 1024 * 1024, // 100 Mo
    accept: {
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    }
  });

  // Mutation pour uploader un document
  const uploadMutation = useMutation(
    (file: File) => apiClient.documentService.uploadDocument(file),
    {
      onSuccess: () => {
        showInfo("Document uploadé avec succès !");
        queryClient.invalidateQueries('userDocuments');
        setIsUploadModalOpen(false);
        setFileToUpload(null); // Réinitialise le fichier
        if (onSuccess) onSuccess();
      },
      onError: (error: AxiosError) => {
        showError(error, { message: "Erreur lors de l'upload du document" });
      }
    }
  );

  const handleUploadSubmit = () => {
    if (fileToUpload) {
      uploadMutation.mutate(fileToUpload);
    } else {
      showInfo("Veuillez sélectionner un fichier.");
    }
  };

  const handleCloseModal = () => {
    setIsUploadModalOpen(false);
    setFileToUpload(null); // Réinitialise le fichier à la fermeture
  }

  return (
    <>
      <Button
        className={buttonClassName}
        style={buttonStyle}
        onClick={() => setIsUploadModalOpen(true)}
      >
        <span className="icon"><FaCloudUploadAlt /></span>
        Uploader un document
      </Button>

      {/* Modal d'upload */}
      <Modal active={isUploadModalOpen} onClose={handleCloseModal} size="small">
        <ModalTitle>Uploader un nouveau document</ModalTitle>
        <Divider />
        <div {...getRootProps()} style={{
          border: `2px dashed ${isDragActive ? '#007bff' : '#bbb'}`,
          padding: '20px',
          borderRadius: '8px',
          textAlign: 'center',
          marginTop: '8px',
          cursor: 'pointer',
          transition: 'border .24s ease-in-out'
        }}>
          <input {...getInputProps()} />
          {fileToUpload ? (
            <Text>Fichier sélectionné : {fileToUpload.name} ({(fileToUpload.size / 1024).toFixed(2)} Ko)</Text>
          ) : (
            <Text>{isDragActive ? "Déposez le fichier ici..." : "Faites glisser un fichier ici, ou cliquez pour sélectionner"}</Text>
          )}
        </div>

        <ModalFooter>
          <Button variant="NEUTRAL" onClick={handleCloseModal}>Annuler</Button>
          <Button variant="PRIMARY" onClick={handleUploadSubmit} loading={uploadMutation.isLoading} disabled={!fileToUpload}>
            Uploader
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default UploadDocument;