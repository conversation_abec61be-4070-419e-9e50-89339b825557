import React from 'react'
import {
  Divider,
  IconName,
  List,
  ListIconStatus,
  ListItem,
  ListItemDescription,
  Section,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'

export const ListScreen = (): JSX.Element => {
  return (
    <>
      <Section>
        <Title level={TitleLevels.THREE}>{`Liste d'éléments classiques`}</Title>

        <Divider />
        <List>
          <ListItem>Bonjour</ListItem>
          <ListItem>Bonjour</ListItem>
          <ListItem>Bonjour</ListItem>
        </List>
      </Section>
      {/*
       * ##############
       * LISTE ELEMENTS AVEC ICONES
       * ##############
       */}
      <Section>
        <Title level={TitleLevels.THREE}>{`Liste d'éléments avec icônes`}</Title>
        <Divider />

        <List hasIcon>
          <ListItem status={ListIconStatus.SUCCESS} customIcon={IconName.CHECK}>
            Bonjour
          </ListItem>
          <ListItem status={ListIconStatus.ERROR} customIcon={IconName.TIMES}>
            Bonjour
          </ListItem>
          <ListItem status={ListIconStatus.ERROR} customIcon={IconName.TIMES}>
            Bonjour
          </ListItem>
        </List>
      </Section>
      {/*
       * ##############
       * LISTE DESCRIPTIONS
       * ##############
       */}
      <Section>
        <Title level={TitleLevels.THREE}>Liste de descriptions</Title>
        <Divider />

        <List>
          <ListItem title='Title'>
            <ListItemDescription>
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book
            </ListItemDescription>
          </ListItem>
          <ListItem title='Title'>
            <ListItemDescription>
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book
            </ListItemDescription>
          </ListItem>
        </List>
      </Section>
      {/*
       * ##############
       * LISTE DESCRIPTION INVERSE
       * ##############
       */}
      <Section>
        <Title level={TitleLevels.THREE}>Liste de descriptions inversé</Title>
        <Divider />

        <List>
          <ListItem title='Title'>
            <ListItemDescription>
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book
            </ListItemDescription>
            <ListItemDescription>
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book
            </ListItemDescription>
          </ListItem>
          <ListItem title='Title'>
            <ListItemDescription>
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book
            </ListItemDescription>
            <ListItemDescription>
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book
            </ListItemDescription>
          </ListItem>
        </List>
      </Section>
    </>
  )
}
