---
id: ADR
hide_table_of_contents: true
tags:
  - ADR
  - sommaire
  - ADR:ouvert
  - ADR:validé
  - ADR:rejeté
---


## Un ADR c'est quoi ?

Un ADR (Architecture Decision Record) est un document qui capture une décision d'architecture faite dans la cadre du devéloppement d'une application.

Il permet ainsi de tracer pourquoi une décision a été prise par rapport à une autre et de garder une trace de cette décision pour les futures équipes qui travailleront sur le projet.

Ainsi, elles pourront comprendre pourquoi l'application est faite d'une certaine manière et pourront plus facilement prendre des décisions en connaissance de cause.

Il est intéressant de noter que l'on écrit des ADR pour des choix qui vont impacter longuement l'application et qui sont difficilement réversibles.

## Status des ADR

Les ADR ont différents status de réalisations pour lequel ont utilise le système de Tags de Docusaurus.

Ci-dessous les différents status possibles:

| Tag | Description | 
|-|-| 
| [ADR:ouvert](/tags/adr-ouvert) | le sujet est ouvert et en étude | 
| [ADR:validé](/tags/adr-valide) | le sujet a été validé et une solution choisie | 
| [ADR:rejeté](/tags/adr-rejete) | le sujet a été écarté car aucune solution n'était satisfaisante ou la problématique n'est plus d'actualité | 

