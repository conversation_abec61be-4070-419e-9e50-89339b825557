import React from "react";
import { But<PERSON>,Icon } from "@bytel/trilogy-react-ts";
import { AiOutlineSetting } from 'react-icons/ai'; 
import { IoPlay } from "react-icons/io5";
import { BsFiletypeCsv, BsFiletypeJson } from "react-icons/bs";
import { FaCloudDownloadAlt } from 'react-icons/fa';
import xraylogo from '../layout/xray-logo.svg';

const ActionDropdown: React.FC<{
   isLoading?: boolean;
  isActive?: boolean;
  isExtended?: boolean;
  handleDownload?: (generationId: string, formatExport: string, type: string, NameExport: string, formatimplementation: string) => void;
  generationId?: string | null;
  typeTelechargement?: string | null;
  NameExport?: string | null;
  formatimplementation?: string | null;
  onClose?: () => void;
}> = ({ 
  isActive,
  isExtended,
  handleDownload,
  generationId,
  NameExport,
  formatimplementation,
  typeTelechargement="exigences",
  onClose
}) => {
  const [open, setOpen] = React.useState(false);

  return (
    <div className="dropdown-container">
      
      <Button 
        className="button is-secondary" 
        style={{ backgroundColor: "rgb(228, 230, 232)" }}
        onClick={() => setOpen((o) => !o)}
        
      >
        <span><img style={{ width: "84px" }} src={xraylogo} alt="Xray Logo"/></span>
        <span className="icon">
              <Icon name="tri-download" size="small" color='blue' style={{ marginLeft: 40 }}/>
            
        </span>
      </Button>
      {/* @TODO old Button avoire ce qui nous convient */}
      {/* <Button className={`fab button is-secondary ${isExtended ? 'is-extended' : ''}`}  style={{ backgroundColor: '#007b52' }}
        onClick={() => setOpen((o) => !o)}
      >
        <span class="icon"><FaCloudDownloadAlt/></span>
        Télécharger
      </Button> */}
      {open && (
        <div className="dropdown-menu">
          <button
            className={`dropdown-button ${isActive ? 'active' : 'inactive'}`}
            onClick={() => {
              if (handleDownload && generationId) {
                handleDownload(generationId, 'json', typeTelechargement, NameExport, formatimplementation);
                if (onClose) onClose();
              }
              setOpen(false);
            }}
          > 
            <BsFiletypeJson size={18} className="dropdown-icon" />
            Export JSON
          </button>
          <button
            className={`dropdown-button ${isActive ? 'active' : 'inactive'}`}
            onClick={() => {
              if (handleDownload && generationId) {
                handleDownload(generationId, 'csv', typeTelechargement, NameExport, formatimplementation);
                if (onClose) onClose();
              }
              setOpen(false);
            }}
          >
            <BsFiletypeCsv size={18} className="dropdown-icon" />
            Export CSV
          </button>
        </div>
      )}
    </div>
  );
};
export default ActionDropdown;