import uuid
from datetime import datetime

from pydantic import BaseModel
from sqlmodel import Field, SQLModel


class FeedbackInput(BaseModel):
    rating: int
    comment: str
    exigence_id: str
    workspace_id: str
    generation_id: str
    type: str


class FeedbackCreate(FeedbackInput):
    user_id: uuid.UUID


class FeedbackOutput(FeedbackCreate):
    id: uuid.UUID
    created_at: datetime


# Modèle de stockage pour la table SQLModel
class Feedback(SQLModel, table=True):
    __tablename__ = "feedback"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    rating: int
    comment: str
    exigence_id: str
    workspace_id: str
    generation_id: str
    type: str
    user_id: uuid.UUID = Field(foreign_key="utilisateurs.id")
    created_at: datetime = Field(default_factory=datetime.now)
