{"plain": {"color": "#D4D4D4", "backgroundColor": "#212121"}, "styles": [{"types": ["comment", "prolog", "doctype", "cdata"], "style": {"color": "#999988", "fontStyle": "italic"}}, {"types": ["namespace"], "style": {"opacity": 0.7}}, {"types": ["string", "attr-value"], "style": {"color": "#e3116c"}}, {"types": ["punctuation", "operator"], "style": {"color": "#393A34"}}, {"types": ["entity", "url", "symbol", "number", "boolean", "variable", "constant", "property", "regex", "inserted"], "style": {"color": "#36acaa"}}, {"types": ["at<PERSON>le", "keyword", "attr-name", "selector"], "style": {"color": "#00a4db"}}, {"types": ["function", "deleted", "tag"], "style": {"color": "#d73a49"}}, {"types": ["function-variable"], "style": {"color": "#6f42c1"}}, {"types": ["tag", "selector", "keyword"], "style": {"color": "#00009f"}}, {"types": ["title"], "style": {"color": "#569CD6", "fontWeight": "bold"}}, {"types": ["property", "parameter"], "style": {"color": "#9CDCFE"}}, {"types": ["script"], "style": {"color": "#D4D4D4"}}, {"types": ["boolean", "arrow", "at<PERSON>le", "tag"], "style": {"color": "#569CD6"}}, {"types": ["number", "color", "unit"], "style": {"color": "#B5CEA8"}}, {"types": ["font-matter"], "style": {"color": "#CE9178"}}, {"types": ["keyword", "rule"], "style": {"color": "#C586C0"}}, {"types": ["regex"], "style": {"color": "#D16969"}}, {"types": ["maybe-class-name"], "style": {"color": "#4EC9B0"}}, {"types": ["constant"], "style": {"color": "#4FC1FF"}}]}