import React, { useState, useEffect } from 'react';
import { Box, Title, TitleLevels } from '@bytel/trilogy-react-ts';
import { useAPIClient } from '../../providers/api';
import { FeedbackOutput } from '../../services/feedbackService';
import FeedbackTable from './components/FeedbackTable';

const FeedbackManagement: React.FC = () => {
  const [feedbacks, setFeedbacks] = useState<FeedbackOutput[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { feedbackService } = useAPIClient();

  const fetchFeedbacks = async () => {
    setIsLoading(true);
    try {
      const data = await feedbackService.listFeedbacks();
      setFeedbacks(data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des Feedbacks');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedbacks();
  }, []);

  return (
    <Box className="box-admin">
      <Title level={TitleLevels.THREE}>
        Liste des Feedbacks
      </Title>

      {error && <div className="alert alert-danger">{error}</div>}
      
      <FeedbackTable 
        feedbacks={feedbacks} 
        isLoading={isLoading}
      />
    </Box>
  );
};

export default FeedbackManagement;