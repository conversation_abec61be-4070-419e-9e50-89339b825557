import React from "react";
import { But<PERSON> } from "@bytel/trilogy-react-ts";
import { useAPIClient } from "providers/api";


/**
 * Un exemple bidon de composant react pour découvrir et essayer des appels vers le backend
 */
export const ApiButton: React.FC<{uri:String}> = ( {uri} ) => {

    const [infoText, setInfoText] = React.useState<string>(`Essayer un appel à l'uri ${uri}`);
    const [isLoading, setIsLoading] = React.useState<boolean>(false);
    
    const {backend} = useAPIClient()

    const handleClick = async () => {
        setIsLoading(true)
        // histoire de voir un peu l'animation
        await new Promise(r => setTimeout(r, 500)); 
        try {
            const result = await backend.get(uri)
            setInfoText(JSON.stringify(result))
        } catch (error: any) {
            console.error(error)
            setInfoText(error.message)
        }finally{
            setIsLoading(false)
        }
    }

    return(
        <Button onClick={() => handleClick()} loading={isLoading}>{infoText}</Button>
    )
}