/*********************************************************************
 ARCANE PLUGIN VSCODE

 authPkce.tsx
 Créé le : Tue May 09 2023 15:27:06
 Auteur : FLBOUGUE

 (C) Copyright Bouygues Telecom 2023.
 **********************************************************************/

import { OAuth2Authorities, OAuth2Provider, TypeConfig } from "@bytel/react-oauth2";
import React from "react";
import { useConfig } from "./config";

export const AuthProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const config = useConfig();
  const oidcConfig: TypeConfig = {
    client_id: config.OAUTH2_OIDC_CLIENT_ID,
    authority: config.OAUTH2_OIDC_AUTHORITY as OAuth2Authorities,
    // Configuration nécessaire pour OAuth2 car la politique CORS ne permet pas de récupérer les endpoints depuis le navigateur. à supprimer sur Sésame
    //     authority_configuration: {
    //       token_endpoint: config.OAUTH2_OIDC_AUTHORITY + '/token',
    //       authorization_endpoint: config.OAUTH2_OIDC_AUTHORITY + '/authorize',
    //       issuer: config.OAUTH2_OIDC_AUTHORITY,
    //       revocation_endpoint: config.OAUTH2_OIDC_AUTHORITY + '/revoke',
    //     },
    //     scope: 'roles openid profile PersonneConsult'
  };

  return <OAuth2Provider config={oidcConfig}>{children}</OAuth2Provider>;
};
