import React from 'react'
import {
  Badge,
  BadgeColor,
  BadgeTextDirection,
  Columns,
  ColumnsItem,
  Divider,
  Icon,
  IconName,
  Section,
  Text,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'

export const BadgeScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={TitleLevels.THREE}>children</Title>
      <Badge>
        <Text>10</Text>
      </Badge>
      <Divider />

      <Title level={TitleLevels.THREE}>content string</Title>
      <Badge content={'1'} />
      <Divider />

      <Title level={TitleLevels.THREE}>content number</Title>
      <Badge content={1} />
      <Divider />

      <Title level={TitleLevels.THREE}>textContent</Title>
      <Badge content={2} textContent='Text with badge' />
      <Divider />

      <Title level={TitleLevels.THREE}>icon badgeContent </Title>
      <Icon name={IconName.ESIM} badgeContent='42' />
      <Divider />

      <Title level={TitleLevels.TWO}>color + text </Title>
      <Columns inlined>
        {Object.values(BadgeColor).map((color, index) => {
          return (
            <ColumnsItem size={6} key={index}>
              <Badge color={color} content={2} textContent='Text' />
            </ColumnsItem>
          )
        })}
      </Columns>
      <Divider />

      <Title level={TitleLevels.TWO}>direction </Title>
      <Columns inlined>
        {Object.values(BadgeTextDirection).map((direction, index) => {
          return (
            <ColumnsItem size={6} key={index}>
              <Badge direction={direction} content={2} textContent='Text' />
            </ColumnsItem>
          )
        })}
      </Columns>
    </Section>
  )
}
