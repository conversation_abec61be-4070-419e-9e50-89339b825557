---
title: <PERSON><PERSON><PERSON>
tags:
  - specificiations
  - <PERSON><PERSON><PERSON>
hide_table_of_contents: false
---


## Accélérer les phases de tests logiciel à l'aide de l'IA générative

### Contexte 

Aujourd'hui, la DSI Bouygues Telecom a des phases de test logiciel dont l'efficacité est très variée d'une application à une autre pour différentes raisons (perte de connaissance, pratiques de test...).

Avec l'arrivée grandissante de l'IA générative, il devient maintenant possible de générer des textes dans un format souhaité (markdown, json, code...) à partir de différents entrants (image, texte, audio, vidéo...) tout en conservant un lien entre l'entrée fourni et la sortie obtenu.

Cette arrivée a donc motivé la DSI Bougues Telecom à lancer des initiatives sur les utilisations possibles de l'IA dans les différents coeurs de métier DSI.


### Périmètre

Dans le cadre de l'IA générative appliquée au test, on peut s'appuyer sur le standard des activités de test de l'ISTQB:

- Planification des tests
- Suivi et contrôle des tests
- Analyse de test (plus communément appelée `Identification des Tests` en interne)
- Conception des tests
- Implémentation des tests
- Exécution des tests
- Clôture des tests


Parmi ces activités, les phases suivantes ont été identifiés comme ayant un fort potentielle:

- Identification des tests
- Conception des tests 
- Implémentation des tests


### Objectifs

La solution applicative a pour objectif d'accélérer tout ce qui concerne la conception des tests.

Ci-dessous une vision des attendus sur chaque activité.

#### Identification

Elle doit permettre d'identifier plus rapidement les tests qui serait à réaliser à partir d'exigences

Il va s'agir içi de déterminer quels sont les cas de tests et leur objectif.

#### Conception

Elle doit permettre de concevoir plus rapidement les tests précédemment identifiés.

Il va s'agir içi de définir plus précisement les jeux de données nécessaires et d'écrire les procédures de test.

Une procédure pouvant être manuelle (étapes d'une fiche de test) ou automatisé (script de test)


#### Implémentation

Elle doit permettre de concevoir plus rapidement l'environnement de test

Il s'agit içi d'implémenter les jeux de données et/ou doublons de test nécessaires à l'exécution des tests.


### Mesures

Comme cela a pu être constaté avec des outils comme copilot, la mesure de la productivité peut s'avérer complexe.

Les Key Point Indicator vont donc être tourné sur les usages et les coûts associés.

:::warning

à déterminer plus précisement avec les parties prenantes

:::

| indicateur | description |
|- |- |
| Coûts LLM | il doit être possible de connaitre pour chaque éléments générés via IA, les coûts en token associés |
| Utilisations | il doit être possible de suivre les différents usages réalisés |
| Taux d'adoption | Le nombre d'utilisateurs actifs par rapport aux nombre d'utilsateurs inscrits |
| Taux de pénétration | Le nombre d'utilsateurs qui ont essayé le produit par rapport aux nombre d'utilisateurs potentiels de la population ciblée |
| Taux de rétention | Le nombre d'utilsateurs qui ont essayé le produit et continue de l'utiliser |


### Partie prenantes

| nom | fonction | rôle |
|-|-|-|
| Olivier HEITZ | DSI | sponsor de l'initiative IA |  
| Sedik BOUZELHA | responsable TOPSI | sponsor de l'initiative IA appliquée aux tests |  
| Massinissa MECHAREK | responsable TOPSI - Gouvernance et Industrialisation des Tests | Pilote de l'initiative IA appliquée aux tests |

### Public cible et contraintes associées

L'ambition est de toucher l'ensemble des collaborateurs de la DSI Bouygues Telecom identifiés comme ayant besoin de concevoir des tests.


Comme on s'attends à une adoption progressive de l'outil, l'application cible doit être capable de:

- gérer 100 sessions utilisateurs en parallèle en charge constante
- absorber des pics à 400 sessions utilisateurs sur 1h

