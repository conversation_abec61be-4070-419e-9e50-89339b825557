import logging
import uuid
from datetime import datetime
from io import BytesIO, StringIO  # Ajoutez BytesIO à l'import

import docx2txt
import pypdf
from langchain_core.documents import Document as langchain_document

from app.common.exceptions import NotFoundException
from app.domains.users.repository import UsersRepositoryInterface

from .models import DocumentFilter, DocumentInput, DocumentOutput, DocumentsOutput, DocumentStorage
from .repository import DocumentRepositoryInterface


class DocumentNotFoundException(NotFoundException):
    """Exception levée lorsqu'un document n'est pas trouvé"""

    def __init__(self, identifier: str):
        super().__init__(f"Le document {identifier} n'existe pas")


class DocumentService:
    """
    Service de gestion des documents téléchargés par les utilisateurs
    """

    def __init__(self, documents: DocumentRepositoryInterface, users: UsersRepositoryInterface):
        self._repo_documents = documents
        self._repo_users = users
        self.log = logging.getLogger(__name__)

    def save_document(self, document_input: DocumentInput) -> DocumentOutput:
        """
        Sauvegarde un document dans la base de données
        """
        self.log.info("Sauvegarde du document %s pour l'utilisateur %s", document_input.filename, document_input.owner_id)

        # Vérifier que l'utilisateur existe
        user = self._repo_users.get(document_input.owner_id)
        if not user:
            self.log.error("Utilisateur %s non trouvé", document_input.owner_id)
            raise NotFoundException(f"L'utilisateur {document_input.owner_id} n'existe pas")

        try:
            now = datetime.now()
            # Création du DAO
            dao = DocumentStorage(
                filename=document_input.filename,
                content=document_input.content,
                owner_id=document_input.owner_id,
                date_creation=now,
                date_maj=None,
            )

            # Sauvegarde
            created = self._repo_documents.add(dao)
            self.log.info("Document %s sauvegardé avec succès (ID: %s)", document_input.filename, created.id)

            # Retourner le DTO avec la taille
            return DocumentOutput(
                id=created.id,
                filename=created.filename,
                owner_id=created.owner_id,
                date_upload=created.date_upload,
                date_creation=created.date_creation,
                date_maj=created.date_maj,
                size=len(created.content),  # Calculer la taille du contenu
            )
        except Exception as e:
            self.log.error("Erreur lors de la sauvegarde du document: %s", str(e), exc_info=True)
            raise

    def get_document(self, document_id: uuid.UUID) -> DocumentOutput:
        """
        Récupère un document par son ID
        """
        self.log.info("Récupération du document %s", document_id)
        document = self._repo_documents.get(document_id)

        if not document:
            self.log.warning("Document %s non trouvé", document_id)
            raise DocumentNotFoundException(str(document_id))

        return DocumentOutput(
            id=document.id,
            filename=document.filename,
            owner_id=document.owner_id,
            date_upload=document.date_upload,
            date_creation=document.date_creation,
            date_maj=document.date_maj,
            size=len(document.content),  # Calculer la taille du contenu
        )

    def get_parse_documents(self, documents_ids: list[uuid.UUID]):
        """
        fonction de parsing selon le type de fichier
        """
        parsed_documents: list[langchain_document] = []
        for document_id in documents_ids:
            document = self._repo_documents.get(document_id)
            if document:
                if ".docx" in str(document.filename).lower():
                    # Utiliser BytesIO pour créer un objet file-like
                    docx_file = BytesIO(document.content)
                    parsed_doc = langchain_document(
                        page_content=docx2txt.process(docx_file), id=str(document_id), metadata={"source": document.filename}
                    )
                    parsed_documents.append(parsed_doc)
                elif ".pdf" in str(document.filename).lower():
                    pdf_file = BytesIO(document.content)
                    pdf_reader = pypdf.PdfReader(pdf_file)
                    for page_number, page in enumerate(pdf_reader.pages):
                        parsed_documents.append(
                            langchain_document(
                                page_content=page.extract_text(), id=str(document_id), metadata={"source": document.filename, "page": page_number}
                            )
                        )
                else:
                    parsed_doc = langchain_document(
                        page_content=StringIO(document.content).read(), id=str(document_id), metadata={"source": document.filename}
                    )
                    parsed_documents.append(parsed_doc)

        return parsed_documents

    def get_user_documents(self, owner_id: uuid.UUID) -> DocumentsOutput:
        """
        Récupère tous les documents d'un utilisateur
        """
        self.log.info("Récupération des documents de l'utilisateur %s", owner_id)
        documents = self._repo_documents.find(DocumentFilter(owner_id=owner_id))

        document_outputs = [
            DocumentOutput(
                id=doc.id,
                filename=doc.filename,
                owner_id=doc.owner_id,
                date_upload=doc.date_upload,
                date_creation=doc.date_creation,
                date_maj=doc.date_maj,
                size=len(doc.content),  # Calculer la taille du contenu
            )
            for doc in documents
        ]

        return DocumentsOutput(total=len(document_outputs), documents=document_outputs)

    def list_documents(self, limit: int = 100) -> DocumentsOutput:
        """
        Liste tous les documents (avec pagination)
        """
        self.log.info("Listage des documents (limite: %d)", limit)
        documents = self._repo_documents.list(limit)

        document_outputs = [
            DocumentOutput(
                id=doc.id,
                filename=doc.filename,
                owner_id=doc.owner_id,
                date_upload=doc.date_upload,
                date_creation=doc.date_creation,
                date_maj=doc.date_maj,
                size=len(doc.content),  # Calculer la taille du contenu
            )
            for doc in documents
        ]

        return DocumentsOutput(total=len(document_outputs), documents=document_outputs)

    def delete_document(self, document_id: uuid.UUID, requesting_user_id: uuid.UUID) -> bool:
        """
        Supprime un document s'il appartient à l'utilisateur demandeur
        """
        self.log.info("Suppression du document %s par l'utilisateur %s", document_id, requesting_user_id)

        # Vérifier que le document existe et appartient à l'utilisateur
        document = self._repo_documents.get(document_id)
        if not document:
            raise DocumentNotFoundException(str(document_id))

        # Vérifier que l'utilisateur est le propriétaire ou admin
        user = self._repo_users.get(requesting_user_id)
        if not user.admin and document.owner_id != requesting_user_id:
            self.log.warning("Tentative de suppression du document %s par un utilisateur non autorisé %s", document_id, requesting_user_id)
            raise PermissionError("Vous n'êtes pas autorisé à supprimer ce document")

        result = self._repo_documents.delete(document_id)
        if result:
            self.log.info("Document %s supprimé avec succès", document_id)
        else:
            self.log.warning("Échec de la suppression du document %s", document_id)

        return result

    def update_document(self, document_id: uuid.UUID, document_input: DocumentInput, requesting_user_id: uuid.UUID) -> DocumentOutput:
        """
        Met à jour un document existant
        """
        self.log.info("Mise à jour du document %s par l'utilisateur %s", document_id, requesting_user_id)

        # Vérifier que le document existe
        document = self._repo_documents.get(document_id)
        if not document:
            raise DocumentNotFoundException(str(document_id))

        # Vérifier que l'utilisateur est le propriétaire ou admin
        user = self._repo_users.get(requesting_user_id)
        if not user.admin and document.owner_id != requesting_user_id:
            self.log.warning("Tentative de mise à jour du document %s par un utilisateur non autorisé %s", document_id, requesting_user_id)
            raise PermissionError("Vous n'êtes pas autorisé à modifier ce document")

        # Mettre à jour les informations du document
        document.filename = document_input.filename
        document.content = document_input.content
        document.date_maj = datetime.now()

        # Sauvegarder les modifications
        updated = self._repo_documents.update(document)
        self.log.info("Document %s mis à jour avec succès", document_id)

        return DocumentOutput(
            id=updated.id,
            filename=updated.filename,
            owner_id=updated.owner_id,
            date_upload=updated.date_upload,
            date_creation=updated.date_creation,
            date_maj=updated.date_maj,
            size=len(updated.content),  # Calculer la taille du contenu
        )
