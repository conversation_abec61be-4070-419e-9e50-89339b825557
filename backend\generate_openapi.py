"""
simple script qui récupère la specficiations openapi du backend et la dépose dans le dossier catalogue-interface du dépôt.
Cela permet ainsi d'alimenter le générateur typesript côté ihm avec une version à jour des objets exposés par le backend.
"""

import json
import logging
from pathlib import Path

DOSSIER_SORTIE = Path(__file__).parent.parent.joinpath("catalogue-interfaces")
logging.basicConfig(level="INFO")


def generate_spec():
    logging.info("récupération de la spécification openapi...")
    from app.main import app

    spec_content = json.dumps(app.openapi(), indent=4)
    create_json_file(app.title, spec_content)


def create_json_file(filename: str, content: str):
    output_file = Path(DOSSIER_SORTIE).joinpath(f"{filename.lower()}-services-swagger.json")
    logging.info("sauvegarde dans le fichier %s", output_file.absolute())
    output_file.write_text(content)
    logging.info("sauvegarde terminé")


if __name__ == "__main__":
    generate_spec()
