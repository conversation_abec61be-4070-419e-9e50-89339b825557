import React from 'react'
import {
  Divider,
  IconName,
  Input,
  Menu,
  MenuItem,
  MenuScrolling,
  Section,
  SubMenuItem,
  Text,
  TextLevels,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'

export const MenuScreen = (): JSX.Element => {
  return (
    <>
      <Section>
        <Menu>
          <MenuItem onClick={() => alert('Click on item 1')} to='/'>
            <Text>Item 1</Text>
          </MenuItem>
          <MenuItem to='/' active>
            <Text>Item 2</Text>
          </MenuItem>
          <MenuItem>
            <SubMenuItem>
              <MenuItem to='/'>
                <Text>Item 3.1</Text>
              </MenuItem>
              <MenuItem to='/'>
                <Text>Item 3.2</Text>
              </MenuItem>
            </SubMenuItem>
          </MenuItem>
          <MenuItem to='/'>
            <Text>Item 3.3</Text>
          </MenuItem>
        </Menu>
      </Section>
      {/*
       * ##############
       * MENU DEROULANT
       * ##############
       */}
      <Section>
        <Title level={TitleLevels.THREE}>Menu Déroulant</Title>
        <Divider />
        <MenuScrolling hasBackgroundWhite>
          <Text level={TextLevels.ONE}>Miguel</Text>

          <Input placeholder='Rechercher' hasIcon search />
          <Menu notASide>
            <MenuItem active arrow>
              Infos personnelles
            </MenuItem>
            <MenuItem arrow>Factures et paiements</MenuItem>
            <MenuItem arrow badge={3}>
              <Text>Messagerie</Text>
            </MenuItem>
            <MenuItem arrow>
              <Text>Commande</Text>
            </MenuItem>
          </Menu>
          <Menu notASide>
            <MenuItem arrow icon={IconName.QUESTION_CIRCLE}>
              <Text>Aide & Contact</Text>
            </MenuItem>
            <MenuItem arrow icon={IconName.SHOPPING_CART}>
              <Text>Boutique en ligne</Text>
            </MenuItem>
          </Menu>
          <Menu notASide>
            <MenuItem icon={IconName.POWER}>
              <Text>Déconnexion</Text>
            </MenuItem>
          </Menu>
        </MenuScrolling>
        <MenuScrolling pulled='right' hasBackgroundWhite>
          <Text level={TextLevels.ONE}>Miguel</Text>
          <Input placeholder='Rechercher' hasIcon search />
          <Menu notASide>
            <MenuItem arrow>
              <Text>Infos personnelles</Text>
            </MenuItem>
            <MenuItem arrow>
              <Text>Factures et paiements</Text>
            </MenuItem>
            <MenuItem arrow badge={3}>
              <Text>Messagerie</Text>
            </MenuItem>
            <MenuItem arrow>
              <Text>Commande</Text>
            </MenuItem>
          </Menu>
          <Menu notASide>
            <MenuItem arrow icon={IconName.QUESTION_CIRCLE}>
              <Text>Aide & Contact</Text>
            </MenuItem>
            <MenuItem arrow icon={IconName.SHOPPING_CART}>
              <Text>Boutique en ligne</Text>
            </MenuItem>
          </Menu>
          <Menu notASide>
            <MenuItem icon={IconName.POWER}>
              <Text>Déconnexion</Text>
            </MenuItem>
          </Menu>
        </MenuScrolling>
      </Section>
    </>
  )
}
