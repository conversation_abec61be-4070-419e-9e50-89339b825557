import React, { useState, useEffect,useRef } from 'react';
import { Box } from '@bytel/trilogy-react-ts';
import UserTable from './components/UserTable.tsx';
import { UserOutput} from '../../types/user';
import { useAPIClient } from '../../providers/api';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserOutput[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const api = useAPIClient();

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const data = await api.adminService.listUsers();
      setUsers(data.users);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des utilisateurs');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  const mountedRef = useRef(false);
  useEffect(() => {
    if (!mountedRef.current) {
      mountedRef.current = true;
      fetchUsers();
      return;
    }
    console.log(mountedRef)
    fetchUsers();
  }, []);

  const handleMakeAdmin = async (login: string) => {
    try {
      await api.adminService.makeAdmin(login);
      fetchUsers();
    } catch (err) {
      setError('Erreur lors de la promotion de l\'utilisateur');
      console.error(err);
    }
  };

  const handleRemoveAdmin = async (login: string) => {
    try {
      await api.adminService.RemoveAdmin(login);
      fetchUsers();
    } catch (err) {
      setError('Erreur lors de la suppression des droits d\'administration de l\'utilisateur');
      console.error(err);
    }
  };

  return (
    <Box className="box-admin">
      
      <h3>Liste Utilisateurs</h3>      

      {error && <div className="alert alert-danger">{error}</div>}
      
      <UserTable 
        users={users} 
        isLoading={isLoading} 
        onMakeAdmin={handleMakeAdmin}
        onRemoveAdmin={handleRemoveAdmin}
        
      />  
      
    </Box>
  );
};

export default UserManagement;