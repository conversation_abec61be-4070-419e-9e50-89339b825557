import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from app.domains.identification.models import (
    DemandeIdentificationFiltre,
    DemandeIdentificationInput,
    DemandeIdentificationStorage,
    IdentificationEchouee,
    IdentificationEnCours,
    IdentificationReussie,
)


class IdentificationsRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dao: DemandeIdentificationInput) -> DemandeIdentificationStorage: ...
    @abstractmethod
    def update(self, evenement: IdentificationEchouee | IdentificationEnCours | IdentificationReussie) -> DemandeIdentificationStorage: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[DemandeIdentificationStorage]: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> DemandeIdentificationStorage | None: ...
    @abstractmethod
    def find(self, dto: DemandeIdentificationFiltre) -> Iterable[DemandeIdentificationStorage] | None: ...
    @abstractmethod
    def filtre(self, dto: DemandeIdentificationFiltre, limit: int = None) -> Iterable[DemandeIdentificationStorage] | None: ...
