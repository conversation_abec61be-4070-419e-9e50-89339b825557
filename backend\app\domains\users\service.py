import logging
import uuid
from datetime import datetime

from app.common.exceptions import AlreadyExistsException, InsufficientPermissionsException, NotFoundException

from .models import UserCreateInput, UserOutput, UsersOutput, UserStorage, UserUpdateInput
from .repository import UsersRepositoryInterface


class UserAlreadyExistsException(AlreadyExistsException):
    message = "L'utilisateur existe déjà"


class UserNotFoundException(NotFoundException):
    message = "L'utilisateur n'existe pas"


class UserService:
    def __init__(self, repository: UsersRepositoryInterface):
        self._repo = repository
        self._logger = logging.getLogger(__name__)

    def register_user(self, user: UserCreateInput) -> UserOutput | UserAlreadyExistsException:
        already_exist = self._repo.find(login=user.login)
        if already_exist:
            raise UserAlreadyExistsException(user.login)
        dao = UserStorage(**user.model_dump(), date_inscription=datetime.now(), admin=False)
        created = self._repo.add(dao)
        return UserOutput(**created.model_dump())

    def list_users(self) -> UsersOutput:
        users_data = self._repo.list()
        response = UsersOutput(users=[UserOutput(**user.model_dump()) for user in users_data], total=len(users_data))
        return response

    def get_user_by_login(self, login: str) -> UserOutput | UserNotFoundException:
        # TODO: revoir la logique des exceptions
        user = self._repo.find(login=login)
        if user:
            return UserOutput(**user.model_dump())
        else:
            raise UserNotFoundException(login)

    def is_admin(self, login: str) -> UserOutput | UserNotFoundException:
        # TODO: revoir la logique des exceptions
        user = self._repo.find(login=login)
        if user.admin:
            return UserOutput(**user.model_dump())
        else:
            raise InsufficientPermissionsException(message=f"L'utilisateur {login} n'est pas administrateur")

    def make_admin(self, login: str, current_user: uuid.UUID) -> None | UserNotFoundException | InsufficientPermissionsException:
        subject = self._repo.find(login=login)
        actor = self._repo.get(entity_id=current_user)  # probablement plus complexe que ça
        if actor and actor.admin:
            if subject:
                self._repo.update(UserUpdateInput(id=subject.id, admin=True))
            else:
                raise UserNotFoundException(login)
        else:
            raise InsufficientPermissionsException(message=f"l'utilisateur {current_user} n'a pas les droits nécessaires")

    def dirty_update(self, login: str):
        subject = self._repo.find(login)
        if subject:
            dto = UserUpdateInput(id=subject.id, admin=True)
            self._logger.info(f"Updating user {login} to admin status.")
            self._logger.debug("User update process initiated dto:%s", dto)
            self._repo.update(dto)
        else:
            self._logger.warning(f"User {login} not found for update.")

    def remove_admin(self, login: str, current_user: uuid.UUID) -> None | UserNotFoundException | InsufficientPermissionsException:
        """Remove admin privileges from a user."""

        subject = self._repo.find(login=login)
        actor = self._repo.get(entity_id=current_user)  # probablement plus complexe que ça
        if actor and actor.admin:
            if subject:
                dto = UserUpdateInput(id=subject.id, admin=False)
                self._repo.update(dto)
                self._logger.info(f"Removed admin privileges from user {login}.")
            else:
                self._logger.warning(f"User {login} not found for admin removal.")
                raise UserNotFoundException(login)
        else:
            raise InsufficientPermissionsException(message=f"l'utilisateur {current_user} n'a pas les droits nécessaires")

    def handle_first_connection(self, login: str) -> UserOutput:
        """Vérifie si l'utilisateur existe, le crée si nécessaire.

        Args:
            login (str): Login de l'utilisateur à vérifier/créer

        Returns:
            UserOutput: L'utilisateur existant ou nouvellement créé
        """
        try:
            # Tente de récupérer l'utilisateur existant
            return self.get_user_by_login(login)
        except UserNotFoundException:
            # Crée l'utilisateur s'il n'existe pas
            user_input = UserCreateInput(login=login)
            return self.register_user(user=user_input)
