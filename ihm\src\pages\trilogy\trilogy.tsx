import React from "react";
import '../workspace/workspaceAll.css'
// ne pas importer via le index.ts car des props sont manquantes dans certains screens
// import {TextScreen} from './screens/Text'
import * as TrilogyScreens from './screens'
import { Divider } from "@bytel/trilogy-react-ts";

const trilogyScreens = [
  // TrilogyScreens.AccordionScreen,
  // TrilogyScreens.AlertScreen,
  TrilogyScreens.CheckboxScreen,
  TrilogyScreens.InputScreen,
  // TrilogyScreens.InfoBlockScreen,
  // TrilogyScreens.ColumnScreen,
  TrilogyScreens.SelectView,
  // TrilogyScreens.SelectorView
]

export const TrilogyPage: React.FC = () => {


  return (
    <React.Fragment>
      {trilogyScreens.map((Item) => <><Divider/><Item/></>)} 
    </React.Fragment>)
};
