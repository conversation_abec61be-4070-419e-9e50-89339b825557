import { B<PERSON><PERSON><PERSON> } from "@bytel/query";
import { UserCreateInput, UserOutput, UsersOutput } from "../types/user";
import { WorkspaceBase, WorkspaceOutput } from "../types/workspace";
import { DemandeIdentificationOutput, DemandeIdentificationFiltre } from "../types/identification"; // À créer si nécessaire

export class AdminService {
  private bquery: BQuery;

  constructor(bquery: BQuery) {
    this.bquery = bquery;
  }

  // Services pour les utilisateurs
  async listUsers(): Promise<UsersOutput> {
    return this.bquery.get<UsersOutput>(`/users`);
  }

  async getCurrentUser(): Promise<UserOutput> {
    return this.bquery.get<UserOutput>(`/users/me`);
  }

  async createUser(user: UserCreateInput): Promise<UserOutput> {
    return this.bquery.post<UserOutput>(`/users`, user);
  }

  async makeAdmin(login: string): Promise<void> {
    return this.bquery.post<void>(`/users/${login}/admin/make`);
  }
  async RemoveAdmin(login: string): Promise<void> {
    return this.bquery.post<void>(`/users/${login}/admin/remove`);
  }

  // Services pour les workspaces
  async listWorkspaces(): Promise<WorkspaceOutput[]> {
    return this.bquery.get<WorkspaceOutput[]>(`/workspaces`);
  }

  async getWorkspace(id: string): Promise<WorkspaceOutput> {
    return this.bquery.get<WorkspaceOutput>(`/workspaces/${id}`);
  }

  async createWorkspace(workspace: WorkspaceBase): Promise<WorkspaceOutput> {
    return this.bquery.post<WorkspaceOutput>(`/workspaces`, workspace);
  }

  // Services pour les identifications
  async listIdentifications(DemandeIdentificationFiltre: DemandeIdentificationFiltre): Promise<DemandeIdentificationOutput[]> {
    return this.bquery.post<DemandeIdentificationOutput[]>(`/admin/identifications`, DemandeIdentificationFiltre);
  }

  async getIdentification(id: string): Promise<DemandeIdentificationOutput> {
    return this.bquery.get<DemandeIdentificationOutput>(`/identifications/${id}`);
  }

  async cancelIdentification(id: string): Promise<DemandeIdentificationOutput> {
    return this.bquery.get<DemandeIdentificationOutput>(`/identifications/${id}/abandonner`);
  }
}
