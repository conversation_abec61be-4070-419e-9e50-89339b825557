// composant custom rajout" pour que le wiki soit un peu plus interactif/accessible
// peut s'utiliser directement dans les .mdx en important via @site
// c'est fait vite fait, faudra probablement revoir à un moment

import { useEffect, useRef, useState, Fragment } from "react";

export const Paragraphe = ({ text }) => {
    const splitText = text.split('\n\n')
  
    if (splitText.length === 1) {
      return <NewlineToBreak text={text} />
    }
  
    return splitText.map((item, idx) =>
      item ? (
        <Fragment key={idx}>
          <p>
            <NewlineToBreak text={item} />
          </p>
        </Fragment>
      ) : null
    )
  }

  const NewlineToBreak = ({ text }) => {
    const splitText = text.split('\n')
  
    if (splitText.length === 1) {
      return text
    }
  
    return splitText.map((item, idx) =>
      item ? (
        <Fragment key={idx}>
          {item}
          <br />
        </Fragment>
      ) : null
    )
  }

const closeButtonStyle = {
    "font-size": '.75em',   
    "position": "absolute",   
    "top": ".25em",
    "right": ".25em",
    "margin": "1rem"
}

function Modal({ openModal, closeModal, title, children }) {
    const ref = useRef();

    useEffect(() => {
        if (openModal) {
            ref.current?.showModal();
        } else {
            ref.current?.close();
        }
    }, [openModal]);

    return (
        <dialog
            ref={ref}
            onCancel={closeModal}
            onBlur={closeModal}
        ><div>
            <p>{title}</p>
            <button onClick={closeModal} style={closeButtonStyle}>
                X
            </button>
            </div>
            {children}
        </dialog>
    );
}

/**
 * Composant react permettant d'annoter avec plus d'informations un texte.
 * Pour cela, on transforme le texte donné en lien cliquable qui affichera dans une modale le détail indiqué
 */
export function Annoter({details, titre, children}) {
    const [modal, setModal] = useState(false);

    return (
        <>
            <a
                onClick={() => setModal(true)}
            >
                {children}
            </a>
            <Modal
                openModal={modal}
                closeModal={() => setModal(false)}
                title={titre ? titre : children}
            >
                {details}
            </Modal>
        </>
    )
}

