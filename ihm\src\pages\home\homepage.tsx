import React from "react";
import { useNavigate } from "react-router-dom";
import cat_ia from 'layout/cat_white.png';
import {
  Box,
  BoxContent,
  Button,
  Card,
  CardContent,
  Columns,
  ColumnsItem,
  Container,
  Divider,
  Icon,
  IconName,
  Section,
  Text,
  TextLevels,
  Title,
  TitleLevels,
  View
} from "@bytel/trilogy-react-ts";
import {
  TypographyAlign,
  TypographyBold,
  TypographyColor,
  IconColor
} from "@bytel/trilogy-react-ts";

const Homepage: React.FC = () => {
  const navigate = useNavigate();

  const goToWorkspace = () => {
    navigate("/workspace");
  };

  return (
    <Container>
      {/* Introduction */}
      <Section>
        <Box>
          <BoxContent>
            <View>
              <Title
                level={TitleLevels.ONE}
                typo={[TypographyAlign.TEXT_CENTERED]}
              >
                <img
                  src={cat_ia}
                  style={{
                    width: "80px",
                    height: "80px",
                    verticalAlign: "middle",
                    marginRight: "20px"
                  }}
                  alt="CatIA icon"
                />
                CatIA - Conception Accélérée des Tests avec IA
              </Title>
              <Text
                level={TextLevels.ONE}
                typo={[
                  TypographyAlign.TEXT_CENTERED,
                  TypographyBold.TEXT_WEIGHT_MEDIUM
                ]}
              >
                CATIA est une application dédiée à l'identification de tests à partir d'exigences, quel que soit leur format (swagger, pdf, vidéo...).
                <br />
                Elle vous permet d'établir un cahier de tests tout en étant accompagné grâce à de l'IA générative.
              </Text>
            </View>
          </BoxContent>
        </Box>
      </Section>

      {/* Comment ça marche ? */}
      <Section background="WHITE">
        <Title
          level={TitleLevels.THREE}
          typo={[TypographyAlign.TEXT_CENTERED]}
        >
          Comment ça marche ?
        </Title>
        <Divider />
        <Columns>
          <ColumnsItem size={4}>
            <Box>
              <BoxContent>
                <View>
                  <div style={{ fontSize: "48px", textAlign: "center", marginBottom: "15px" }}>
                    🏗️
                  </div>
                  <Title level={TitleLevels.THREE} typo={[TypographyColor.TEXT_INFO]}>
                    Créez un espace de travail et ajoutez vos exigences
                  </Title>
                  <Text level={TextLevels.TWO}>
                    Ajoutez vos exigences manuellement ou importez-les depuis une documentation unique Confluence.<br/>
                    Les exigences peuvent être du texte libre ou des spécifications OpenAPI.
                  </Text>
                </View>
              </BoxContent>
            </Box>
          </ColumnsItem>
          <ColumnsItem size={4}>
            <Box>
              <BoxContent>
                <View>
                  <div style={{ fontSize: "48px", textAlign: "center", marginBottom: "15px" }}>
                    🚦
                  </div>
                  <Title level={TitleLevels.THREE} typo={[TypographyColor.TEXT_SUCCESS]}>
                    Lancez l'identification des tests
                  </Title>
                  <Text level={TextLevels.TWO}>
                    Lancez l'identification sur une ou plusieurs exigences.<br/>
                    Vous pouvez ajouter des documentations supplémentaires à prendre en compte lors de l'identification.
                  </Text>
                </View>
              </BoxContent>
            </Box>
          </ColumnsItem>
          <ColumnsItem size={4}>
            <Box>
              <BoxContent>
                <View>
                  <div style={{ fontSize: "48px", textAlign: "center", marginBottom: "15px" }}>
                    📤
                  </div>
                  <Title level={TitleLevels.THREE} typo={[TypographyColor.TEXT_DANGER]}>
                    Exportez les tests qui vous intéressent
                  </Title>
                  <Text level={TextLevels.TWO}>
                    Faites le tri dans les tests en les validant ou rejetant.<br/>
                    Téléchargez vos tests validés en format manuel Xray ou automatisé.<br/>
                    Téléchargez des bouchons pour vos spécifications OpenAPI.
                  </Text>
                </View>
              </BoxContent>
            </Box>
          </ColumnsItem>
        </Columns>
        <Box onClick={goToWorkspace} style={{ margin: "32px 0 0 0", cursor: "pointer" }}>
          <BoxContent background="INFO">
            <Title level="TWO" typo={[TypographyColor.TEXT_WHITE, TypographyBold.TEXT_WEIGHT_MEDIUM]} style={{ textAlign: "center" }}>
              🚀 Commencez dès maintenant à structurer vos projets !
            </Title>
          </BoxContent>
        </Box>
      </Section>

      {/* Contexte et fonctionnalités */}
      <Section background="WHITE">
        <Title
          level={TitleLevels.THREE}
          typo={[TypographyAlign.TEXT_CENTERED]}
        >
          Pourquoi utiliser CatIA ?
        </Title>
        <Divider />

        <Columns>
          <ColumnsItem size={4}>
            <Box>
              <BoxContent>
                <View>
                  <div style={{ fontSize: "48px", textAlign: "center", marginBottom: "15px" }}>
                    ⚡
                  </div>
                  <Title level={TitleLevels.THREE} typo={[TypographyColor.TEXT_INFO]}>
                    Génération rapide de tests
                  </Title>
                  <Text level={TextLevels.TWO}>
                    CATIA identifie et génère automatiquement des cas de test à partir de vos exigences, quel que soit leur format (swagger, pdf, vidéo, etc.).
                  </Text>
                </View>
              </BoxContent>
            </Box>
          </ColumnsItem>
          <ColumnsItem size={4}>
            <Box>
              <BoxContent>
                <View>
                  <div style={{ fontSize: "48px", textAlign: "center", marginBottom: "15px" }}>
                    🤖
                  </div>
                  <Title level={TitleLevels.THREE} typo={[TypographyColor.TEXT_SUCCESS]}>
                    Accompagnement par l'IA
                  </Title>
                  <Text level={TextLevels.TWO}>
                    Profitez de l'IA générative pour obtenir des suggestions pertinentes et ne plus jamais souffrir du syndrome de la page blanche.
                  </Text>
                </View>
              </BoxContent>
            </Box>
          </ColumnsItem>
          <ColumnsItem size={4}>
            <Box>
              <BoxContent>
                <View>
                  <div style={{ fontSize: "48px", textAlign: "center", marginBottom: "15px" }}>
                    📒
                  </div>
                  <Title level={TitleLevels.THREE} typo={[TypographyColor.TEXT_DANGER]}>
                    Cahier de tests structuré
                  </Title>
                  <Text level={TextLevels.TWO}>
                    Constituez facilement un cahier de tests complet, adapté à vos besoins, et améliorez la qualité de vos livrables.
                  </Text>
                </View>
              </BoxContent>
            </Box>
          </ColumnsItem>
        </Columns>
      </Section>
    </Container>
  );
};

export default Homepage;