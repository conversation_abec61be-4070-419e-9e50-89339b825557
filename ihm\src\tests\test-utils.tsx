/* eslint-disable @typescript-eslint/no-explicit-any */
import { expect, MockInstance, vi } from "vitest";
import { RenderResult } from "@testing-library/react";
import "@testing-library/jest-dom";
import { randomUUID } from "crypto";

export function mockObject<T extends Record<string, unknown> | object>(obj: T) {
  Object.getOwnPropertyNames(Object.getPrototypeOf(obj)).forEach((methode) => {
    if (
      !Object.getOwnPropertyNames(Object.prototype).includes(methode) &&
      // @ts-expect-error
      typeof obj[methode] === "function"
    ) {
      // @ts-ignore
      obj[methode] = vi.fn();
    }
  });
  Object.keys(obj).forEach((key) => {
    if (
      // @ts-expect-error
      typeof obj[key] === "object" &&
      // @ts-expect-error
      !Array.isArray(obj[key]) &&
      // @ts-expect-error
      obj[key] != null
    ) {
      // @ts-expect-error
      mockObject(obj[key]);
    }
    // @ts-expect-error
    if (typeof obj[key] === "function") {
      // @ts-ignore
      obj[key] = vi.fn();
    }
  });
  return vi.mocked(obj, true);
}

export function mockedComponent<R extends Record<string, any>>(componentModule: R, componentName: keyof R) {
  const mockedText = `mocked: ${randomUUID()}`;

  const isPureReactComponent = typeof componentModule[componentName] === "function";
  const isObserverReactComponent =
    typeof componentModule[componentName] === "object" &&
    // @ts-ignore
    typeof componentModule[componentName]["type"] === "function";

  let mockFunction: MockInstance;
  if (isPureReactComponent) {
    mockFunction = vi
      // @ts-ignore
      .spyOn(componentModule, componentName)
      .mockReturnValue((<>{mockedText}</>) as any);
  } else if (isObserverReactComponent) {
    mockFunction = vi
      // @ts-ignore
      .spyOn(componentModule[componentName], "type")
      .mockReturnValue((<>{mockedText}</>) as any);
  } else {
    throw new Error("This component can't be mocked at the moment");
  }

  type TypeProps = Parameters<R[typeof componentName]>[0];

  const expectToBeInTheDocument = (container: RenderResult) => expect(container.getByText(mockedText)).toBeInTheDocument();
  const expectNotToBeInTheDocument = (container: RenderResult) => expect(container.queryByText(mockedText)).not.toBeInTheDocument();
  const expectToHaveBeenCalledWith = (expectedProps: TypeProps) => expect(mockFunction).toHaveBeenCalledWith(expectedProps, expect.anything());

  return {
    expectToBeInTheDocument,
    expectNotToBeInTheDocument,
    expectToHaveBeenCalledWith,
  };
}
