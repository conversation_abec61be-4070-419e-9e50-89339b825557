# language: fr
@test_integration @non-passant
Fonctionnalité: Gérer les demandes incohérentes
    En tant que fournisseur de l'agent WebJDD
    Je souhaite gérer les situations où les demandes de l'utilisateur sont incohérentes
    Afin de pouvoir limiter mes coûts de traitement tout en garantissant une qualité de service
    Pour cela, l'agent WebJDD doit être capable de détecter les messages qui ne sont pas des demandes de jeux de données complètes
    Et stopper son traitement pour proposer à l'utilisateur de formuler une demande de jeux de données

  Plan du scénario: Messages qui ne sont pas des des demandes de jeux de données
    Etant donné que je suis un utilisateur WebJDD identifié
    Quand je demande à l'agent WebJDD "<demande>"
    Alors l'agent WebJDD identifie aucun type de jeux de données
    Et l'agent WebJDD renvoie une réponse à l'utilisateur

    Exemples:
      | demande         |
      | Salut ! ça va ? |
      | Bonjour         |

  Plan du scénario: Messages qui sont des demandes de jeux de données imprécises
    Etant donné que je suis un utilisateur WebJDD identifié
    Quand je demande à l'agent WebJDD "<demande>"
    Alors l'agent WebJDD identifie aucun type de jeux de données
    Et l'agent WebJDD renvoie une réponse à l'utilisateur

    Exemples:
      | demande                         |
      | jdd                             |
      | j'ai besoin d'un jeu de données |
      | il me faut un jdd               |
