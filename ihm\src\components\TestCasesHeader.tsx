import React from 'react';
import { Title, TitleLevels, Tabs, TabsItem } from '@bytel/trilogy-react-ts';
import { Requirement } from 'services/interfaces/requirementInterfaces';
import ActionDropdown from 'components/ActionDropdown';

interface TestCasesHeaderProps {
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  activeRequirement: number | null;
  generationsLength: number;
  handleDownload?: (generationId: string, formatExport: string, type: string, NameExport: string, formatimplementation: string) => void;
  filteredRequirements: Requirement[];
  TestCasesactiveTabIndex: number; // Ajout de la prop activeTabIndex
  setTestCasesActiveTabIndex: (index: number) => void; // Ajout de la méthode pour changer l'onglet actif
}

const TestCasesHeader: React.FC<TestCasesHeaderProps> = ({
  searchTerm,
  handleSearchChange,
  activeRequirement,
  generationsLength,
  handleDownload,
  filteredRequirements,
  TestCasesactiveTabIndex,
  setTestCasesActiveTabIndex
}) => (
  <div className="testcases-header">
    <div className="search-bar">
      <input
        type="text"
        placeholder="Recherche exigence..."
        value={searchTerm}
        onChange={handleSearchChange}
      />
    </div>
    {activeRequirement !== null && generationsLength > 0 && (
      <><div className="testcases-container">
        <Tabs textAlign="has-text-left">
          <TabsItem  active={TestCasesactiveTabIndex === 0}  onClick={() => setTestCasesActiveTabIndex(0)}>
                <Title className="mt-0 testcases-title" level={TitleLevels.TWO}>
                  Cas de test
                </Title> 
          </TabsItem>
          {filteredRequirements[activeRequirement]?.type === 'OpenAPI' && (
            <TabsItem active={TestCasesactiveTabIndex === 1} onClick={() => setTestCasesActiveTabIndex(1)}>
              <Title className="mt-0 testcases-title" level={TitleLevels.TWO}>
                Bouchons OpenAPI
              </Title>
            </TabsItem>
          )}
        </Tabs>
      </div>
      <div className="right-content">
        {activeRequirement !== null && filteredRequirements[activeRequirement] && TestCasesactiveTabIndex===0 && (
                  <ActionDropdown
                    isExtended={true}
                    isActive={true}
                    handleDownload={handleDownload}
                    generationId={filteredRequirements[activeRequirement]?.id}
                    NameExport={filteredRequirements[activeRequirement]?.nom}
                    formatimplementation='xray'
                  /> )}
                </div>             
      </>
    )}
  </div>
);

export default TestCasesHeader;
