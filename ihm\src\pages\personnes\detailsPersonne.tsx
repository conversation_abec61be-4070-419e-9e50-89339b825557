import { Box, Container, Title, TitleLevels } from "@bytel/trilogy-react-ts";
import React from "react";
import { useAPIClient } from "providers/api";

import { useSnackbar } from "providers/snackbar";
import { useQuery } from "react-query";
import { AxiosError } from "axios";
import { useTypedParams } from "helpers/router.ts";

export const DetailsPersonnePage: React.FC = () => {
  const { id } = useTypedParams<{ id: string }>();
  const { showError } = useSnackbar();
  const { personneService } = useAPIClient();

  const { data: personne, error, isLoading } = useQuery(["personne", id], () => personneService.consulterPersonne(id));

  if (error) {
    showError(error as AxiosError, {
      message: "Erreur lors de la récupération de la personne",
    });
  }
  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <Container className={"body-page-personnes"}>
      <Box className={"paper-personnes"} testId="userDetailsTitle">
        <Title level={TitleLevels.ONE}>{personne?.prenom} {personne?.nom} {personne?.dateNaissance}</Title>
        <div>{JSON.stringify(personne)}</div>
      </Box>
    </Container>
  );
};
