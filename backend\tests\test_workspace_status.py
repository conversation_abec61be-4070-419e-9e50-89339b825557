"""
Tests unitaires pour le nouveau service de statut des workspaces
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock

from app.domains.workspace.service import WorkspaceService, WorkspaceNotFoundException
from app.domains.workspace.models import WorkspaceStorage, WorkspaceStatusOutput
from app.domains.identification.models import DemandeIdentificationStorage, StatutsDemande, PhotoEntrants


class TestWorkspaceStatus:
    """Tests pour la nouvelle méthode get_workspace_status"""

    def setup_method(self):
        """Configuration des mocks pour chaque test"""
        self.mock_workspace_repo = Mock()
        self.mock_identification_repo = Mock()
        self.service = WorkspaceService(self.mock_workspace_repo, self.mock_identification_repo)
        
        # Workspace de test
        self.workspace_id = uuid.uuid4()
        self.workspace = WorkspaceStorage(
            id=self.workspace_id,
            nom="Test Workspace",
            description="Workspace de test",
            date_creation=datetime.now(),
            owner_id=uuid.uuid4()
        )

    def test_get_workspace_status_workspace_not_found(self):
        """Test quand le workspace n'existe pas"""
        # Arrange
        self.mock_workspace_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(WorkspaceNotFoundException):
            self.service.get_workspace_status(self.workspace_id)

    def test_get_workspace_status_no_identification_requests(self):
        """Test quand il n'y a aucune demande d'identification"""
        # Arrange
        self.mock_workspace_repo.get.return_value = self.workspace
        self.mock_identification_repo.find.return_value = []
        
        # Act
        result = self.service.get_workspace_status(self.workspace_id)
        
        # Assert
        assert isinstance(result, WorkspaceStatusOutput)
        assert result.is_busy is False
        assert result.last_identification_request is None

    def test_get_workspace_status_with_busy_workspace(self):
        """Test quand le workspace est occupé (demande en cours)"""
        # Arrange
        self.mock_workspace_repo.get.return_value = self.workspace
        
        # Demande en cours
        demande_en_cours = DemandeIdentificationStorage(
            id=uuid.uuid4(),
            workspace_id=self.workspace_id,
            demandeur_id=uuid.uuid4(),
            date_creation=datetime.now(),
            statut=StatutsDemande.EN_COURS,
            entrants=PhotoEntrants(exigences=[])
        )
        
        # Mock des appels au repository
        # Premier appel : toutes les demandes (pour récupérer la dernière)
        self.mock_identification_repo.find.side_effect = [
            [demande_en_cours],  # Toutes les demandes
            [demande_en_cours]   # Demandes en cours
        ]
        
        # Act
        result = self.service.get_workspace_status(self.workspace_id)
        
        # Assert
        assert isinstance(result, WorkspaceStatusOutput)
        assert result.is_busy is True
        assert result.last_identification_request is not None
        assert result.last_identification_request["id"] == str(demande_en_cours.id)
        assert result.last_identification_request["statut"] == StatutsDemande.EN_COURS.value

    def test_get_workspace_status_with_completed_identification(self):
        """Test avec une demande d'identification terminée (workspace libre)"""
        # Arrange
        self.mock_workspace_repo.get.return_value = self.workspace
        
        # Demande terminée
        demande_terminee = DemandeIdentificationStorage(
            id=uuid.uuid4(),
            workspace_id=self.workspace_id,
            demandeur_id=uuid.uuid4(),
            date_creation=datetime.now(),
            date_debut=datetime.now(),
            date_fin=datetime.now(),
            statut=StatutsDemande.REUSSIE,
            entrants=PhotoEntrants(exigences=[])
        )
        
        # Mock des appels au repository
        self.mock_identification_repo.find.side_effect = [
            [demande_terminee],  # Toutes les demandes
            []                   # Aucune demande en cours
        ]
        
        # Act
        result = self.service.get_workspace_status(self.workspace_id)
        
        # Assert
        assert isinstance(result, WorkspaceStatusOutput)
        assert result.is_busy is False
        assert result.last_identification_request is not None
        assert result.last_identification_request["id"] == str(demande_terminee.id)
        assert result.last_identification_request["statut"] == StatutsDemande.REUSSIE.value
        assert result.last_identification_request["date_fin"] is not None

    def test_get_workspace_status_multiple_requests_returns_latest(self):
        """Test que la méthode retourne bien la demande la plus récente"""
        # Arrange
        self.mock_workspace_repo.get.return_value = self.workspace
        
        # Plusieurs demandes (la première dans la liste est la plus récente car triée par date desc)
        demande_recente = DemandeIdentificationStorage(
            id=uuid.uuid4(),
            workspace_id=self.workspace_id,
            demandeur_id=uuid.uuid4(),
            date_creation=datetime.now(),
            statut=StatutsDemande.REUSSIE,
            entrants=PhotoEntrants(exigences=[])
        )
        
        demande_ancienne = DemandeIdentificationStorage(
            id=uuid.uuid4(),
            workspace_id=self.workspace_id,
            demandeur_id=uuid.uuid4(),
            date_creation=datetime.now(),
            statut=StatutsDemande.REUSSIE,
            entrants=PhotoEntrants(exigences=[])
        )
        
        # Mock des appels au repository
        self.mock_identification_repo.find.side_effect = [
            [demande_recente, demande_ancienne],  # Toutes les demandes (triées par date desc)
            []                                    # Aucune demande en cours
        ]
        
        # Act
        result = self.service.get_workspace_status(self.workspace_id)
        
        # Assert
        assert isinstance(result, WorkspaceStatusOutput)
        assert result.is_busy is False
        assert result.last_identification_request is not None
        assert result.last_identification_request["id"] == str(demande_recente.id)
