import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Divider, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@bytel/trilogy-react-ts";
import { FeedbackWithDetails } from "../../../types/feedback";

interface FeedbackDetailsModalProps {
  feedback: FeedbackWithDetails | null;
  isOpen: boolean;
  onClose: () => void;
}

const FeedbackDetailsModal: React.FC<FeedbackDetailsModalProps> = ({
  feedback,
  isOpen,
  onClose,
}) => {
  if (!feedback) return null;

  return (
    <Modal active={isOpen} triggerMarkup={ModalMarkup.BUTTON} closeIcon onClose={onClose}>
      <ModalTitle>Détails du feedback</ModalTitle>
      <Divider />
      <div>
        <Box className="mb-3">
          <Text>Exigence :</Text>
          <pre
            style={{
              backgroundColor: "#f5f5f5",
              padding: "10px",
              borderRadius: "4px",
              maxHeight: "300px",
              overflowY: "auto",
              whiteSpace: "pre-wrap",
              fontSize: "13px",
            }}
          >
            {feedback.exigence?.nom || 'Exigence non sélectionnée'}
          </pre>
        </Box>

        <Box className="mb-3">
          <Text>Testcase :</Text>
          <pre
            style={{
              backgroundColor: "#f5f5f5",
              padding: "10px",
              borderRadius: "4px",
              maxHeight: "300px",
              overflowY: "auto",
              whiteSpace: "pre-wrap",
              fontSize: "13px",
            }}
          >
            {feedback.generation?.titre || 'Testcase non sélectionné'}
          </pre>
        </Box>

        <Box className="mb-3">
          <Text>Commentaire :</Text>
          <pre
            style={{
              backgroundColor: "#f5f5f5",
              padding: "10px",
              borderRadius: "4px",
              maxHeight: "300px",
              overflowY: "auto",
              whiteSpace: "pre-wrap",
              fontSize: "13px",
            }}
          >
            {feedback.comment || 'Aucun commentaire'}
          </pre>
        </Box>
      </div>
      <ModalFooter>
        <Button variant="SECONDARY" onClick={onClose}>
          Fermer
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default FeedbackDetailsModal;