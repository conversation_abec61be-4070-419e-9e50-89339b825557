import logging
from pathlib import Path

import asgi_correlation_id
import colorlog
import uvicorn

from app.common.art import print_app_plate
from app.common.config import AppSettings


def configure_logging(settings: AppSettings):
    print_app_plate(settings.APP_NAME)

    # Format commun pour tous les handlers
    log_format = "{asctime} | {levelname:^8s} | {correlation_id} | {name} | {message}"

    # Format spécifiques à la sortie (console, fichier...)
    file_formater = logging.Formatter(log_format, style="{")
    console_formater = colorlog.ColoredFormatter(
        fmt="{log_color}" + log_format,
        style="{",
        log_colors={
            "DEBUG": "white",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "bold_red",
        },
    )

    # définition des loggers
    root_logger = logging.getLogger()
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_error_logger = logging.getLogger("uvicorn.error")
    if settings.LOG_LEVEL != "INFO":
        logging.getLogger("sqlalchemy.engine").setLevel(settings.LOG_LEVEL)

    # Handler Console
    console_handler = colorlog.StreamHandler()
    console_handler.setFormatter(console_formater)
    console_handler.addFilter(asgi_correlation_id.CorrelationIdFilter())

    # Configuration des loggers UVicorn
    uvicorn_access_logger.setLevel(logging.INFO)
    uvicorn_access_logger.addFilter(asgi_correlation_id.CorrelationIdFilter())
    uvicorn_access_logger.addHandler(console_handler)

    uvicorn_error_logger.setLevel(logging.INFO)
    uvicorn_error_logger.addFilter(asgi_correlation_id.CorrelationIdFilter())
    uvicorn_error_logger.addHandler(console_handler)

    # Configuration root logger
    root_logger.setLevel(settings.LOG_LEVEL)
    root_logger.addHandler(console_handler)

    # Modification de la configuration de FastAPI et Uvicorn
    LOGGING_CONFIG = uvicorn.config.LOGGING_CONFIG
    LOGGING_CONFIG["formatters"]["access"]["fmt"] = log_format
    LOGGING_CONFIG["handlers"]["access"]["filters"] = [asgi_correlation_id.CorrelationIdFilter()]
    LOGGING_CONFIG["handlers"]["default"]["filters"] = [asgi_correlation_id.CorrelationIdFilter()]

    # Optionnel: creation d'un fichier de log
    if settings.LOG_FILE:
        # Création du répertoire de logs
        Path(settings.LOG_FILE).parent.mkdir(parents=True, exist_ok=True)
        # Handler Fichier
        file_handler = logging.FileHandler(settings.LOG_FILE)
        file_handler.setFormatter(file_formater)
        file_handler.addFilter(asgi_correlation_id.CorrelationIdFilter())
        # Configuration des loggers avec le handler fichier
        uvicorn_access_logger.addHandler(file_handler)
        uvicorn_error_logger.addHandler(file_handler)
        root_logger.addHandler(file_handler)
