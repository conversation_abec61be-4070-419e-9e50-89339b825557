import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>sList, <PERSON><PERSON> } from "@bytel/trilogy-react-ts";
import { DemandeIdentificationOutput } from "../../../types/identification";
import CustomLoader from "../../../components/CustomLoader";
import IdentificationDetailsModal from "./IdentificationDetailsModal";

interface IdentificationTableProps {
  identifications: DemandeIdentificationOutput[];
  isLoading: boolean;
  onCancelIdentification: (id: string) => void;
}

const IdentificationTable: React.FC<IdentificationTableProps> = ({ identifications, isLoading, onCancelIdentification }) => {
  const [selectedIdentification, setSelectedIdentification] = useState<DemandeIdentificationOutput | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const handleViewDetails = (identification: DemandeIdentificationOutput) => {
    setSelectedIdentification(identification);
    setShowDetailsModal(true);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleString("fr-FR");
    } catch (e) {
      console.error("Error formatting date:", e);
      return dateString || "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "en cours":
        return <Chips>En cours</Chips>;
      case "erreur":
        return <Chips disabled >Erreur</Chips>;
      case "crée":
        return <Chips>Crée</Chips>;
      case "succès":
        return <Chips active>Succès</Chips>;
      default:
        return <Chips>{status || "Inconnu"}</Chips>;
    }
  };

  const closeModal = () => {
    setShowDetailsModal(false);
    setSelectedIdentification(null);
  };

  const calculateDuration = (startDate: string, endDate: string): string => {
    // Vérifier que la date de début existe
    if (!startDate) {
      return "N/A";
    }

    try {
      // Convertir les chaînes en objets Date
      const start = new Date(startDate);
      const end = endDate ? new Date(endDate) : new Date(); // Si pas de date de fin, utiliser maintenant

      // Calculer la différence en millisecondes
      const diffMs = end.getTime() - start.getTime();

      // Si la différence est négative ou nulle, retourner N/A
      if (diffMs <= 0) {
        return "N/A";
      }

      // Convertir en secondes, minutes, heures
      const diffSecs = Math.floor(diffMs / 1000);
      const diffMins = Math.floor(diffSecs / 60);
      const diffHours = Math.floor(diffMins / 60);

      // Si moins d'une minute
      if (diffMins < 1) {
        return `${diffSecs} sec`;
      }
      // Si moins d'une heure
      else if (diffHours < 1) {
        const remainingSecs = diffSecs % 60;
        return `${diffMins} min ${remainingSecs} sec`;
      }
      // Si plus d'une heure
      else {
        const remainingMins = diffMins % 60;
        return `${diffHours} h ${remainingMins} min`;
      }
    } catch (e) {
      console.error("Error calculating duration:", e);
      return "N/A";
    }
  };

  return (
    <>
      {isLoading ? (
        <div className="loader-container my-5">
          <CustomLoader size="large" />
        </div>
      ) : (
        <table className="table table-admin">
          <thead>
            <tr>
              <th>Date de demande</th>
              <th>Dure de la demande</th>
              <th>Statut</th>
              <th>Demandeur</th>
              <th>Workspace</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {identifications && identifications.length > 0 ? (
              identifications.map((identification) => (
                <tr key={identification.id}>
                  <td>{formatDate(identification.date_creation)}</td>
                  <td>
                    {identification.date_debut ? calculateDuration(identification.date_debut, identification.date_fin) : "N/A"}
                    {!identification.date_fin && identification.date_debut && " (en cours)"}
                  </td>
                  <td>
                    <ChipsList>{getStatusBadge(identification.statut)}</ChipsList>
                  </td>
                  <td>{identification.demandeur_name || identification.demandeur_id.slice(0, 8) + "..." || "N/A"}</td>
                  <td>{identification.workspace_name || identification.workspace_id.slice(0, 8) + "..." || "N/A"}</td>
                  <td>
                    <Button variant="WARNING" onClick={() => handleViewDetails(identification)} className="mr-2">
                      Détails
                    </Button>
                    {identification.statut === "en cours" && (
                      <Button  variant="DANGER" onClick={() => onCancelIdentification(identification.id)}>
                        Annuler
                      </Button>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center p-4">
                  Aucune identification trouvée
                </td>
              </tr>
            )}
          </tbody>
        </table>
      )}

      <IdentificationDetailsModal
        identification={selectedIdentification}
        isOpen={showDetailsModal}
        onClose={closeModal}
        onCancelIdentification={onCancelIdentification}
        formatDate={formatDate}
        getStatusBadge={getStatusBadge}
        calculateDuration={calculateDuration}
      />
    </>
  );
};

export default IdentificationTable;
