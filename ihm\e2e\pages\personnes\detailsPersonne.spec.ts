import { expect, test } from "@playwright/test";
import { authenticate } from "../../auth";
import { Individu } from "services/interfaces/generated/HELLOWORLD-FORMATION/consulterPersonne.ts";

test.beforeEach(({ page }) => authenticate(page));

test("Affichage d'une personne", async ({ page, context }) => {
  context.serviceWorkers();
  const personne = { nom: "Bond", prenom: "<PERSON>", id: "jbond", type: "INDIVIDU" } satisfies Individu;
  await page.route("/api/personnes/123", (route) => {
    return route.fulfill({ json: personne });
  });
  await page.goto("/personnes/123");

  await expect(page.getByTestId("userDetailsTitle")).toBeVisible();
});
