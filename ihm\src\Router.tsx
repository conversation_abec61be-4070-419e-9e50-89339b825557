import * as React from "react";
import { Layout } from "layout/layout.tsx";
import { Navigate, Outlet, useRoutes } from "react-router-dom";
import { DetailsPersonnePage } from "./pages/personnes/detailsPersonne.tsx";
import { DebugPage} from "./pages/debug.tsx"
import { TrilogyPage} from "./pages/trilogy/trilogy.tsx"
import { RecherchePersonnesPage } from "./pages/personnes/recherchePersonne.tsx";
import { RouteContext, RouteObjectWithMetadata } from "helpers/router.ts";
import { Authenticated } from "@bytel/react-oauth2";
import WorkspacePage from "./pages/workspace/workspace.tsx";
import WorkspaceDetailPage from "./pages/workspace/workspaceDetail.tsx";
import AdministrationPage from "./pages/administration/index.tsx";
import ProfilePage from "./pages/profile";
import Homepage from "./pages/home/<USER>";
import { ProtectedRoute } from "./components/ProtectedRoute"; // Import du composant de protection

export const Router: React.FC = () => {
  const _ROUTES = React.useMemo<RouteObjectWithMetadata[]>(() => {
    return [
      {
        path: "/*",
        element: (
          <Authenticated>
            <Layout>
              <Outlet />
            </Layout>
          </Authenticated>
        ),
        children: [
          {
            titre: "Accueil",
            index: true,
            element: <Homepage />,
          },
          {
            titre: "Workspace",
            path: "workspace",
            element: <WorkspacePage />,
          },
          {
            titre: "Détails Workspace",
            path: "workspace/:id",
            element: <WorkspaceDetailPage />,
          },
          {
            titre: "Administration",
            path: "administration",
            element: (
              <ProtectedRoute requireAdmin={true}>
                <AdministrationPage />
              </ProtectedRoute>
            ),
          },
          {
            titre: "Détails Personne",
            path: "personnes/:id",
            element: <DetailsPersonnePage />,
          },
          {
            titre: "Recherche Personnes",
            path: "personnes",
            element: <RecherchePersonnesPage />,
          },
          {
            titre: "Mon Profil",
            path: "profile",
            element: <ProfilePage />,
          },
          {
            titre: "Debug",
            path: "debug",
            element: <DebugPage />,
          },
          {
            titre: "Trilogy",
            path: "trilogy",
            element: <TrilogyPage />,
          }
        ],
      },
    ];
  }, []);

  return <RouteContext.Provider value={_ROUTES}>{useRoutes(_ROUTES)}</RouteContext.Provider>;
};
