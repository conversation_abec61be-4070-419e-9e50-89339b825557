import { Service } from "./commonService";
import { Bouchon, Generation } from "services/interfaces/generationInterfaces";
import { BQuery } from "@bytel/query";

/**
 * Service pour la gestion des générations (testcases)
 */
export class GenerationService extends Service {
  constructor(bquery: BQuery) {
    super(bquery);
  }

  /**
   * Récupère toutes les générations (testcases) associées à une exigence
   * @param requirementId L'identifiant de l'exigence
   */
  async getGenerationsByRequirementId(requirementId: string): Promise<Generation[]> {
    try {
      const response = await this.bquery.get(`/exigences/${requirementId}/testcases`);
      return response as Generation[];
    } catch (error) {
      console.error(`Failed to fetch test cases for requirement with id ${requirementId}:`, error);
      return [];
    }
  }

  /**
   * Fonction pour valider une génération par ID
   * @param generationId L'identifiant de la génération à valider
   */
  async validateGeneration(generationId: string): Promise<void> {
    try {
      const response = await this.bquery.get(`/testcase/${generationId}/validate`);
      console.log(`Generation ${generationId} validated successfully:`, response);

      // Vous pouvez retourner une valeur ou un objet si nécessaire, par exemple, un état détaillé
      return response;
    } catch (error) {
      console.error(`Failed to validate generation with id ${generationId}:`, error);
      throw error; // Vous pouvez décider de lancer l'erreur ou de gérer autrement
    }
  }

  /**
   * Fonction pour rejeter une génération par ID
   * @param generationId L'identifiant de la génération à rejeter
   */
  async rejectGeneration(generationId: string): Promise<void> {
    try {
      const response = await this.bquery.get(`/testcase/${generationId}/reject`);
      console.log(`Generation ${generationId} rejected successfully:`, response);

      return response;
    } catch (error) {
      console.error(`Failed to reject generation with id ${generationId}:`, error);
      throw error;
    }
  }

  /**
   * Fonction pour télécharger une génération ou une liste de générations
   * @param identifiant L'identifiant du testcase, de l'exigence ou du workspace
   * @param format Le format de téléchargement (csv, xlsx, etc.)
   * @param type Le type de ressource ('testcase', 'exigences', 'workspace')
   */
  async downloadGeneration(identifiant: string, format: string = 'csv', type: string): Promise<any> {
    try {
      // Valider le type
      if (!['testcase', 'exigences', 'workspace'].includes(type)) {
        throw new Error(`Type invalide: ${type}. Types attendus: 'testcase', 'exigences', ou 'workspace'.`);
      }

      // Construire l'URL en fonction du type
      const endpoint = type === 'testcase'
        ? `/testcase/${identifiant}/export`
        : `/${type}/${identifiant}/testcases/export`;

      // Effectuer la requête avec le point de terminaison construit
      return await this.bquery.get(`${endpoint}?format=${format}`, {
        responseType: 'blob'
      });

    } catch (error) {
      console.error(`Échec du téléchargement du ${type} avec l'identifiant ${identifiant}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les bouchons associés à une exigence
   * @param requirementId L'identifiant de l'exigence
   */
  async getBouchonsByRequirementId(requirementId: string): Promise<Bouchon[]> {
    try {
      const response = await this.bquery.get(`/exigences/${requirementId}/bouchons`);
      return response as Bouchon[];
    } catch (error) {
      console.error(`Failed to fetch bouchons for requirement with id ${requirementId}:`, error);
      return [];
    }
  }

  /**
   * Récupère une génération (testcase) par son identifiant
   * @param id L'identifiant du testcase
   */
  async getGenerationById(id: string): Promise<Generation | null> {
    try {
      const response = await this.bquery.get(`/testcase/${id}`);
      return response as Generation;
    } catch (error) {
      console.error(`Failed to fetch generation with id ${id}:`, error);
      return null;
    }
  }
}
