---
id: Template ADR
hide_table_of_contents: false
tags:
  - ADR:template
---

Ci-dessous le template pour tracer les décisions d'architecture avec un exemple factice renseigné. 

:::info 

Vous êtes invité à renseigner le statut directement dans les tags docusaurus en choissisant parmi les valeurs suivantes:

| Tag | Description | 
|-|-| 
| `ADR:ouvert`| le sujet est ouvert et en étude | 
| `ADR:validé`| le sujet a été validé et une solution choisie | 
| `ADR:rejeté`| le sujet a été écarté car aucune solution n'était satisfaisante ou la problématique n'est plus d'actualité | 

:::

:::note

Les dates ne sont pas nécessaire puisque l'ADR est intégré au dépôt git, ce qui permet d'avoir l'historique.

:::

## Contexte et problématique rencontré

:::note

Préciser içi le contexte et/ou la problématique rencontrée nécessitant de faire un choix d'architecture.
Le contenu doit permettre de comprendre les enjeux liés à la problématique

:::

La solution de persistance des données (base SQL postgresql) commence à rencontrer des temps de latence importants du à la croissance des utilisateurs et des usages.
Dans un premier temps, une scalabilité verticale a été effectué (augmentation RAM et CPU) pour rétablir la performance mais cela reste une solution temporaire.

Si on reste sur la trajectoire actuelle, on risque de perdre des utilisateurs au fur et à mesure à cause des temps d'attente long dès qu'ils veulent effectuer des recherches.



## Solutions possibles

:::note

Décrire içi, les N solutions possibles avec à chaque fois leur avantages et inconvénients.
Le contenu doit exposer clairement les conséquences qu'auront chaque solutions.

Dans l'exemple factice, 2 solutions sont possibles.

:::


### Archiver les données {#1}

On estime que le scaling vertical reste suffisant pour les volumétries d'utilisateurs et de données générés.
Auquel cas, on va chercher plutôt à archiver les données qui ne sont plus utiles toute en avertissant les utilisateurs concernés.

Ces données archivées seront sorties de la base de données au profit de solution de stockage plus adaptés (ex: S3 Glacier Deep Archive).

<table>
<thead>
<tr>
<th scope="col" style={{width:"50%"}}>Avantages</th>
<th scope="col" style={{width:"50%"}}>Inconvénients</th>
</tr>
</thead>
<tbody>
<tr>
<td>

- Il y aura moins de données inutilisés en base ce qui permettra de retrouver des latences et une expérience utilisateur acceptable

</td>
<td>

- Les traitements nécessitant de travailler sur ces anciennes donnés seront plus long et plus complexes
- Le scaling vertical sera le seul levier

</td>
</tr>
</tbody>
</table>


### Database Sharding

Au lieu de rester sur la base actuelle, on bascule sur un cluster de base données de manière à faire du Database Sharding.
Cela permet d'introduire une scalabilité horizontale et donc de supporter à l'avenir une population utilisateurs plus importante.


<table>
<thead>
<tr>
<th scope="col" style={{width:"50%"}}>Avantages</th>
<th scope="col" style={{width:"50%"}}>Inconvénients</th>
</tr>
</thead>
<tbody>
<tr>
<td>

- Débit globaux en lecture/écriture plus important
- Haute disponibilité des données
- Possibilité de faire du scaling horizontal

</td>
<td>

- coûts d'infrastructure plus importants
- introduit de la complexité supplémentaire (distribution de données dans le cluster)
- overhead dans les opération de lecture/écriture ce qui introduit une latence supplémentaire

</td>
</tr>
</tbody>
</table>


## Solution choisie

:::note

Indiquez içi la solution qui a été retenue et surtout les raisons qui ont poussés à ce choix

:::

Après étude des utilisateurs potentiels, il a été décide de partir sur la solution [d'archivage des données](#1).

En effet, il s'avère que l'on est déjà à 80% de la population adressable et donc la croissance supplémentaire 
de la popultation utilisateur n'est pas suffisament forte pour justifier les coûts et la complexité d'un sharding.

De plus, il parait plus sain d'optimiser les performances de la base actuelle en faisant le tri dans les données utiles plutôt que de stocker à tout va.
