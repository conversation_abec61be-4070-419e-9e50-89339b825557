from pydantic import TypeAdapter
from sqlalchemy import TypeDecorator
from sqlmodel import JSON


class PydanticJson(TypeDecorator):
    """
    Adapter pour permettre de sauvegarder directement un modele pydantic en jsonb côté SQL Alchemy

    s'utilise de la manière suivante:

    class Nested(BaseModel):
        value: str

    class Parent(SQLModel, table=True):
        id: int = Field(primary_key=True, default=None)
        nested: Nested | None = Field(sa_column=Column(PydanticJson(Nested)))
        nested_list: list[Nested] = Field(sa_column=Column(PydanticJson(list[Nested])))

    """

    impl = JSON()
    cache_ok = True

    def __init__(self, pt):
        super().__init__()
        self.pt = TypeAdapter(pt)
        self.coerce_compared_value = self.impl.coerce_compared_value

    def bind_processor(self, dialect):
        return lambda value: self.pt.dump_json(value) if value is not None else None

    def result_processor(self, dialect, coltype):
        return lambda value: self.pt.validate_json(value) if value is not None else None
