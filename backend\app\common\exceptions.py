"""
all the http exception that will be then put into the openapi spec
"""

import uuid

from fastapi import status as statuscode
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field


class ParsingException(Exception):
    """
    erreur rencontrée lors d'un parsing d'un contenu
    """

    def __init__(self, *args, raw_content: str, error_message: str, comment: str | None):
        super().__init__(*args)
        self.raw_content = raw_content
        self.error_message = error_message
        self.comment = comment


class ErreurScraping(BaseModel):
    """
    erreur remonté dans les API lorsqu'un parsing/scrapping a échoué sur du contenu.
    Cette erreur est utilisé pour donner plus de détail afin de faciliter la reproduction du cas
    """

    raw_content: str = Field(description="le contenu brut qui était entrain d'être travaillé")
    error_message: str = Field(description="l'erreur rencontrée")
    comment: str | None = Field(description="permet de préciser à quel étapes/moment clés du scraping du contenu l'erreur s'est produite")


class BaseError(BaseModel):
    message: str = Field(..., description="Error message or description")


class BaseIdentifiedError(BaseError):
    identifier: str | uuid.UUID | int = Field(..., description="Unique identifier which this error references to")


class NotFoundError(BaseIdentifiedError):
    """The entity does not exist"""


class AlreadyExistsError(BaseIdentifiedError):
    """An entity being created already exists"""


class BaseAPIException(Exception):
    """
    Base error for custom API exceptions
    """

    message = "Generic error"
    code = statuscode.HTTP_500_INTERNAL_SERVER_ERROR
    model = BaseError

    def __init__(self, **kwargs):
        kwargs.setdefault("message", self.message)
        self.message = kwargs["message"]
        self.data = self.model(**kwargs)

    def __str__(self):
        return self.message

    def response(self):
        return JSONResponse(content=self.data.model_dump(mode="json"), status_code=self.code)

    @classmethod
    def response_model(cls):
        return {cls.code: {"model": cls.model}}


class BaseIdentifiedException(BaseAPIException):
    """Base error for exceptions related with entities, uniquely identified"""

    message = "Entity error"
    code = statuscode.HTTP_500_INTERNAL_SERVER_ERROR
    model = BaseIdentifiedError

    def __init__(self, identifier, **kwargs):
        super().__init__(identifier=identifier, **kwargs)


class NotFoundException(BaseIdentifiedException):
    """Base error for exceptions raised because an entity does not exist"""

    message = "The entity does not exist"
    code = statuscode.HTTP_404_NOT_FOUND
    model = NotFoundError


class AlreadyExistsException(BaseIdentifiedException):
    """Base error for exceptions raised because an entity already exists"""

    message = "The entity already exists"
    code = statuscode.HTTP_409_CONFLICT
    model = AlreadyExistsError


class InsufficientPermissionsException(BaseAPIException):
    """
    base error when the user doesn't have the necessary rights to use the API
    """

    message = "Insufficient rights"
    code = statuscode.HTTP_403_FORBIDDEN
    model = BaseError


class NotAvailableException(BaseAPIException):
    """
    base error when a service from the server is
    disabled due to the app state
    or removed definitely
    """

    message = "The current service is disabled or removed."
    code = statuscode.HTTP_410_GONE
    model = BaseError


class InvalidContentException(BaseAPIException):
    """
    base error when the client pushed invalid content
    """

    message = "bad request due to the following reason"
    code = statuscode.HTTP_400_BAD_REQUEST
    model = BaseError

    def __init__(self, error_reason: str, **kwargs):
        super().__init__(message=f"{self.message} {error_reason}", **kwargs)


def get_exception_responses(*args: type[BaseAPIException]) -> dict:
    """Given BaseAPIException classes, return a dict of responses used on FastAPI endpoint definition, with the format:
    {statuscode: schema, statuscode: schema, ...}"""
    responses = {}
    for cls in args:
        responses.update(cls.response_model())
    return responses
