import React from 'react'
import { Section, Headline, HeadlineLevel, Title, TitleLevels, Divider } from '@bytel/trilogy-react-ts'

export const TitleScreen = (): JSX.Element => {
  return (
    <Section>
      <Headline level={HeadlineLevel.LEVEL1}>Ceci est une headline 1</Headline>
      <Headline level={2}>Ceci est une headline 2</Headline>
      <Headline level={'3'}>Ceci est une headline 3</Headline>
      <Headline level={4}>Ceci est une headline 4</Headline>
      <Headline level={5}>Ceci est une headline 5</Headline>
      <Headline level={1} inverted>
        Ceci est une headline 1
      </Headline>
      <Headline level={2} inverted>
        Ceci est une headline 2
      </Headline>
      <Headline level={'3'} inverted>
        Ceci est une headline 3
      </Headline>
      <Title level={TitleLevels.ONE}>Ceci titre 1</Title>
      <Title level={TitleLevels.TWO}>Ceci titre 2</Title>
      <Title level={TitleLevels.THREE}>Ceci titre 3</Title>
      <Title>Ceci titre 1</Title>
      <Title level={'ONE'}>Ceci titre one</Title>
      <Title level={'TWO'}>Ceci titre two</Title>
      <Title level={'THREE'}>Ceci titre three</Title>
      <Title subtitle>Ceci est un subtitle</Title>
      <Title suptitle>Ceci est un suptitle</Title>
      <Divider />
    </Section>
  )
}
