import React, { useState, useEffect } from 'react';
import { Box } from '@bytel/trilogy-react-ts';
import WorkspaceTable from './components/WorkspaceTable.tsx';
import { WorkspaceOutput } from '../../types/workspace.ts';
import { useAPIClient } from '../../providers/api';

const WorkspaceManagement: React.FC = () => {
  const [workspaces, setWorkspaces] = useState<WorkspaceOutput[]>([]);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const api = useAPIClient();

  const fetchWorkspaces = async () => {
    setIsLoading(true);
    try {
      const data = await api.adminService.listWorkspaces();
      setWorkspaces(data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des workspaces');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchWorkspaces();
  }, []);

 

  return (
    <Box className="box-admin">
      
      <h3>Liste Workspaces</h3>


      {error && <div className="alert alert-danger">{error}</div>}
      
      <WorkspaceTable 
        workspaces={workspaces} 
        isLoading={isLoading}
      />
      
      
    </Box>
  );
};

export default WorkspaceManagement;