"""
la façade qui fait le lien entre les agents IA et la gestion de la demande d'identification.

Est utilisé directement via les backgrounds tasks de FastAPI
"""

import logging
import uuid

from app.domains.agents.models import (
    AgentIA,
    BouchonsServiceIA,
    CasDeTestIA,
    FormatBouchon,
    FormatExigence,
    FormatTest,
    OptionsIdentification,
    StatistiquesIA,
    TypeExigence,
    WorkflowIdentificationState,
)
from app.domains.bouchons.models import BouchonsApiInput
from app.domains.identification.models import AgentIdentificationInput, AgentIdentificationOutput, IdentificationEchouee, IdentificationReussie
from app.domains.testcases.models import TestCaseInput


class AppAgents:
    langage_naturel: AgentIA
    open_api: AgentIA


class AgentIdentifications:
    """
    la façade qui sépare la partie "classique" de l'application et la partie purement IA générative

    on s'engage à tout faire transiter via le contrat suivant:

        - réception de la demande  (AgentIdentificationInput)
        - génération via IA avec un ou plusieurs agents spécialisés qui ont chacun leur tambouille interne
        - renvoi de l'identification générée (AgentIdentificationOutput)
    """

    def __init__(self, agents: AppAgents):
        self.log = logging.getLogger(__name__)
        self.agents = agents

    def identifier(self, identification: AgentIdentificationInput) -> AgentIdentificationOutput:
        """
        réalise l'identification à partir des exigences fournies
        """
        identifications_generees: list[WorkflowIdentificationState] = []
        en_erreur = False

        self.log.info("prise en compte de la demande %s", identification.demande.id)

        for exigence_fournie in identification.exigences:
            self.log.info("identification des tests pour l'exigence %s de type %s", exigence_fournie.id, exigence_fournie.type)
            try:
                demande = WorkflowIdentificationState(
                    exigence=TypeExigence(type=exigence_fournie.type, identifiant=exigence_fournie.id, data=exigence_fournie.data),
                    documents=identification.documents,
                )

                if exigence_fournie.type in [FormatExigence.TEXTE_LIBRE, FormatExigence.USER_STORY]:
                    identifications_generees.append(self.agents.langage_naturel.run(demande))

                elif exigence_fournie.type == FormatExigence.OPEN_API:
                    demande.options = OptionsIdentification(
                        identifier_bouchons=True,
                        formats_bouchons=[FormatBouchon.WIREMOCK],
                        formats_tests=[FormatTest.MANUAL, FormatTest.POSTMAN],
                    )
                    identifications_generees.append(self.agents.open_api.run(demande))

                else:
                    raise NotImplementedError("Le format de l'exigence n'a pas d'AgentIA correspondant implémenté: %s", exigence_fournie)

            except Exception as error:
                self.log.exception("Erreur rencontrée lors de l'identification sur l'exigence %s: %s", str(exigence_fournie.id), error)
                en_erreur = True

        return self._combine_generations(identifications_generees, en_erreur, identification.demande.id)

    def _combine_generations(
        self, generations: list[WorkflowIdentificationState], en_erreur: bool, demande_id: uuid.UUID
    ) -> AgentIdentificationOutput:
        """
        fonction dédiée à la réconciliations des différentes générations réalisées
        """
        self.log.debug("combinaisons des différentes générations IA réalisées...")
        test_generes: list[CasDeTestIA] = []
        bouchons_generes: list[BouchonsServiceIA] = []
        usages_ia: list[StatistiquesIA] = []

        for generation in generations:
            if generation.tests:
                test_generes.extend(generation.tests)

            if generation.stats:
                usages_ia.append(generation.stats)

            if generation.bouchons:
                bouchons_generes.append(generation.bouchons)

        output = AgentIdentificationOutput(
            demande=IdentificationEchouee(id=demande_id) if en_erreur else IdentificationReussie(id=demande_id, stats=self._combine_stats(usages_ia)),
            tests=[
                TestCaseInput(
                    titre=test.titre,
                    description=test.description,
                    exigence_id=test.reference_exigence,
                    date_generation=test.date_generation,
                    implementations=None if test.implementations is None else [implem.model_dump() for implem in test.implementations],
                )
                for test in test_generes
            ],
            bouchons=[
                BouchonsApiInput(
                    exigence_id=bouchons_service.reference_exigence,
                    date_generation=bouchons_service.date_generation,
                    usecases=bouchons_service.usages,
                    implementations=bouchons_service.implementations,
                )
                for bouchons_service in bouchons_generes
            ],
        )
        return output

    def _combine_stats(self, stats: list[StatistiquesIA]) -> StatistiquesIA:
        """
        combine les statistiques de génération
        """
        self.log.debug("calcul des statistiques globales liés à la demande d'identification...")
        total_stats = StatistiquesIA(models=[], total_tokens=0, prompt_tokens=0, completion_tokens=0, reasoning_tokens=0, total_cost=0.0)

        for stat in stats:
            self.log.debug("prise en compte de: %s", stat)
            total_stats.models.extend(stat.models)
            total_stats.prompt_tokens += stat.prompt_tokens
            total_stats.completion_tokens += stat.completion_tokens
            total_stats.reasoning_tokens += stat.reasoning_tokens if stat.reasoning_tokens else 0
            total_stats.total_tokens += stat.total_tokens
            total_stats.total_cost += stat.total_cost
            self.log.debug("total intermédiaire: %s", stat)

        total_stats.models = list(set(total_stats.models))

        return total_stats
