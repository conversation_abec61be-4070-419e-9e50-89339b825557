from .models import FeedbackInput, FeedbackOutput
from .repository import FeedbackRepositoryInterface


class FeedbackService:
    def __init__(self, repository: FeedbackRepositoryInterface):
        self._repo = repository

    def submit_feedback(self, feedback_input: FeedbackInput) -> FeedbackOutput:
        stored_feedback = self._repo.submit_feedback(feedback_input)
        return FeedbackOutput(**stored_feedback.model_dump())

    def list_feedbacks(self, skip: int = 0, limit: int = 100) -> list[FeedbackOutput]:
        feedbacks = self._repo.list(limit=limit)
        return [FeedbackOutput(**f.model_dump()) for f in feedbacks]
