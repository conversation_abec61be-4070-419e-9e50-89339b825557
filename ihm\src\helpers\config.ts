import JSON5 from "json5";

let promiseConfig: Promise<TypeConfig | void>;

export async function getConfig(): Promise<TypeConfig> {
  if (promiseConfig == null) {
    promiseConfig = fetch("/config.json") //
      .then((res) => res.text())
      .then((json) => JSON5.parse<TypeConfig>(json))
      .catch((err) =>
        console.error("Erreur de récupération de la configuration", err),
      );
  }
  return (await promiseConfig) as TypeConfig;
}

export interface TypeConfig {
  API_URL: string;
  OAUTH2_OIDC_AUTHORITY: string;
  OAUTH2_OIDC_CLIENT_ID: string;
  VERSION: string;
  ENVIRONNEMENT: string;
  PRISME_ENDPOINT: string;
  ST: string;
  PROMPT: string;
}
