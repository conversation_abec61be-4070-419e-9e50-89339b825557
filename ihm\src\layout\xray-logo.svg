<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="540" height="200" viewBox="0 0 540 200" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 270 100)" id="af50450c-1ef6-4ef9-a59c-c361fec8aa73"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-270" y="-100" rx="0" ry="0" width="540" height="200" />
</g>
<g transform="matrix(Infinity NaN NaN Infinity 0 0)" id="c8cbb82a-2b3f-4d52-a9a5-b94b82de8278"  >
</g>
<g transform="matrix(1.55 0 0 1.55 270 100)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 -89.36 23.94)"  >
<path style="stroke: none; stroke-width: 2.5; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(49,179,75); fill-rule: nonzero; opacity: 0.5;" vector-effect="non-scaling-stroke"  transform=" translate(-30.64, -143.94)" d="M 47.5235 120.05625 L 25.0285 125.68 L 13.764650000000001 167.818 L 47.5235 120.05625 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -94.95 -2.73)"  >
<path style="stroke: none; stroke-width: 2.5; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(49,179,75); fill-rule: nonzero; opacity: 0.8;" vector-effect="non-scaling-stroke"  transform=" translate(-25.05, -117.27)" d="M 47.52275 120.05725 L 2.5 131.402 L 2.5 103.13725 L 36.259 103.13725 L 47.60375 120.041 L 47.522749999999995 120.05725 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -100.62 -18.24)"  >
<path style="stroke: none; stroke-width: 2.5; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(49,179,75); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-19.38, -101.76)" d="M 2.5 103.13775 L 2.5 131.33775 L 36.259 72.1825 L 2.5 103.13775 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 29.58 0)"  >
<path style="stroke: none; stroke-width: 2.5; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-149.58, -120)" d="M 72.75575 97.44875 L 83.09575000000001 112.26175 L 93.43575000000001 97.44875 L 103.64625000000001 97.44875 L 88.59 119.02 L 105.04 142.5525 L 94.635 142.5525 L 83.14450000000001 126.44275 L 71.79950000000001 142.5525 L 61.65400000000001 142.5525 L 77.86100000000002 119.42525 L 62.69125000000002 97.44875 L 72.75575000000002 97.44875 Z M 139.69025 142.5525 L 131.92725 129.26275 C 131.3875 129.29525 130.84625 129.29525 130.3065 129.26275 L 120.22575 129.26275 L 120.22575 142.5525 L 111.57125 142.5525 L 111.57125 97.44875 L 130.3065 97.44875 C 141.92675 97.44875 148.5555 103.04 148.5555 112.97500000000001 C 148.5555 120.07350000000001 145.41125 125.14625000000001 139.6255 127.56125 L 149.51174999999998 142.5525 L 139.69025 142.5525 Z M 120.24199999999999 121.62950000000001 L 130.37125 121.62950000000001 C 136.854 121.62950000000001 140.38725 118.87425 140.38725 113.20175 C 140.38725 107.5295 136.78924999999998 104.9525 130.37125 104.9525 L 120.22575 104.9525 L 120.22575 121.6455 L 120.242 121.6295 Z M 188.60275 132.97425 L 166.59375 132.97425 L 162.60675 142.5525 L 153.70925 142.5525 L 173.48175 97.44875000000002 L 182.3955 97.44875000000002 L 201.87625 142.5525 L 192.62199999999999 142.5525 L 188.60275 132.97425 Z M 185.52349999999998 125.5515 L 177.67925 106.5245 L 169.57575 125.50275 L 185.5235 125.5515 Z M 221.17849999999999 127.69075000000001 L 221.17849999999999 142.5525 L 212.57274999999998 142.5525 L 212.57274999999998 128.11225000000002 L 195.78224999999998 97.44875000000002 L 204.51774999999998 97.44875000000002 L 216.77024999999998 118.64725000000001 L 228.84449999999998 97.44875000000002 L 237.499 97.44875000000002 L 221.17849999999999 127.69075000000002 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>