import React, { useState } from 'react';
import { 
  Box, 
  Title, 
  BoxContent, 
  Tabs, 
  TabsItem
} from '@bytel/trilogy-react-ts';

import UserManagement from './UserManagement';
import WorkspaceManagement from './WorkspaceManagement';
import FeedbackManagement from './FeedbackManagement';
import IdentificationManagement from './IdentificationManagement';
import './administration.css';

const AdminPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };

  return (
    <Box>
      <BoxContent>
        <Title level="THREE"  className="mb-4">
          Administration
        </Title>
        <Tabs>
          <TabsItem active={activeTab === 0} onClick={() => handleTabChange(0)}>
            Gestion des Identifications
          </TabsItem>
          <TabsItem active={activeTab === 1} onClick={() => handleTabChange(1)}>
            Gestion des Utilisateurs
          </TabsItem>
          <TabsItem active={activeTab === 2} onClick={() => handleTabChange(2)}>
            Gestion des Workspaces
          </TabsItem>
          <TabsItem active={activeTab === 3} onClick={() => handleTabChange(3)}>
            Gestion des Feedbacks
          </TabsItem>
        </Tabs>
        
        <div className="mt-4">
          {activeTab === 0 && <IdentificationManagement />}
          {activeTab === 1 && <UserManagement />}
          {activeTab === 2 && <WorkspaceManagement />}
          {activeTab === 3 && <FeedbackManagement />}
        </div>
      </BoxContent>
    </Box>
  );
};

export default AdminPage;