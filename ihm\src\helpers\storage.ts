interface StorageKeys {
  stsFavoris: string[];
}

export const AppStorage = {
  setItem: <K extends keyof StorageKeys>(
    key: K,
    value: StorageKeys[K],
  ): void => {
    localStorage.setItem(key, JSON.stringify(value));
  },
  getItem: <K extends keyof StorageKeys>(key: K): StorageKeys[K] | null => {
    const value = localStorage.getItem(key);
    return value ? (JSON.parse(value) as StorageKeys[K]) : null;
  },
  removeItem: (key: keyof StorageKeys) => {
    localStorage.removeItem(key);
  },
};
