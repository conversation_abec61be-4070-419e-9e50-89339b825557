# l'ensemble des secrets attendu depuis la cartographie vault
# si une est manquante, les pods (apiweb principalement) ne démarreront pas
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: vault-main-secret
  namespace: {{ .Values.global.ideploy_st_nom | lower }}-{{ .Values.global.ideploy_cartographie_nom | lower }}
  annotations:
    deploymentDate: {{ now | date "2006-01-02T15:04:05" }}
spec:
  data:
  - remoteRef:
      key: {{ default .Values.global.ideploy_cartographie_nom }}
      property: placeholder
    secretKey: placeholder
  - remoteRef:
      key: {{ default .Values.global.ideploy_cartographie_nom }}
      property: apikey
    secretKey: apikey
  - remoteRef:
      key: {{ default .Values.global.ideploy_cartographie_nom }}
      property: dbuser
    secretKey: dbuser
  - remoteRef:
      key: {{ default .Values.global.ideploy_cartographie_nom }}
      property: dbpassword
    secretKey: dbpassword
  - remoteRef:
      key: {{ default .Values.global.ideploy_cartographie_nom }}
      property: atlassianLogin
    secretKey: atlassianLogin
  - remoteRef:
      key: {{ default .Values.global.ideploy_cartographie_nom }}
      property: atlassianToken
    secretKey: atlassianToken
  refreshInterval: 424242h # Prochain refresh 48 ans après la première synchronisation (en pratique = jamais)
  secretStoreRef:
    kind: SecretStore
    name: vault-backend
  target:
    name: vault-{{ .Values.global.ideploy_st_nom | lower }}-secrets