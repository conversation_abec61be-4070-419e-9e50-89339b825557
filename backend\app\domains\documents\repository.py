import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from .models import DocumentFilter, DocumentStorage


class DocumentRepositoryInterface(ABC):
    """
    Interface pour le repository de documents
    """

    @abstractmethod
    def add(self, dao: DocumentStorage) -> DocumentStorage:
        """Ajoute un document"""
        ...

    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> DocumentStorage | None:
        """Récupère un document par son ID"""
        ...

    @abstractmethod
    def list(self, limit: int = None) -> Iterable[DocumentStorage]:
        """Liste tous les documents avec une limite optionnelle"""
        ...

    @abstractmethod
    def find(self, filtre: DocumentFilter) -> Iterable[DocumentStorage]:
        """Recherche des documents selon des critères"""
        ...

    @abstractmethod
    def delete(self, entity_id: uuid.UUID) -> bool:
        """Supprime un document par son ID"""
        ...

    @abstractmethod
    def update(self, dao: DocumentStorage) -> DocumentStorage:
        """Met à jour un document"""
        ...
