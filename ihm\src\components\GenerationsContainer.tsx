import React, { useState } from 'react';
import { Tag, Tabs, TabsItem } from '@bytel/trilogy-react-ts';
import { Generation } from 'services/interfaces/generationInterfaces';
import { IconName, TagVariant } from '@bytel/trilogy-react-ts';

interface GenerationsContainerProps {
  activeTabIndex: number;
  setActiveTabIndex: React.Dispatch<React.SetStateAction<number>>;
  generations: Generation[];
  filteredGenerations: Generation[];
  handleFilterClick: (status: string | null) => void;
  handleGenerationClick: (generationId: string) => void;
}

const getTagVariant = (status: string): TagVariant => {
  switch (status) {
    case 'créé':
      return TagVariant.SECONDARY;
    case 'validé':
      return TagVariant.SUCCESS;
    case 'rejeté':
      return TagVariant.ERROR;
    default:
      return TagVariant.SECONDARY;
  }
};

const getIconName = (status: string): IconName => {
  switch (status) {
    case 'créé':
      return IconName.INFOS_CIRCLE;
    case 'validé':
      return IconName.CHECK_CIRCLE;
    case 'rejeté':
      return IconName.TIMES_CIRCLE;
    default:
      return IconName.INFOS_CIRCLE;
  }
};

const GenerationsContainer: React.FC<GenerationsContainerProps> = ({
  activeTabIndex,
  setActiveTabIndex,
  generations,
  filteredGenerations,
  handleFilterClick,
  handleGenerationClick,
}) => {
  const countByStatus = (status: string) => generations.filter((gen) => gen.statut === status).length;

  const countTotal = generations.length;
  const countCréé = countByStatus('créé');
  const countValidé = countByStatus('validé');
  const countRejeté = countByStatus('rejeté');

  const [selectedTagId, setSelectedTagId] = useState<string | null>(null);

  const handleTagClick = (id: string) => {
    setSelectedTagId(selectedTagId === id ? null : id);
    handleGenerationClick(id);
  };

  const sortGenerationsByStatus = (generations: Generation[]) => {
    const order: Record<string, number> = { validé: 0, rejeté: 1, créé: 2 };
    return [...generations].sort((a, b) => order[a.statut] - order[b.statut]);
  };

  return (
    <div className="generations-container">
      <Tabs activeIndex={activeTabIndex}>
        <TabsItem
          iconName="tri-equal-circle"
          active={activeTabIndex === 0}
          onClick={() => {
            setActiveTabIndex(0);
            handleFilterClick(null);
          }}
        >
          {`Tout: ${countTotal.toString()}`}
        </TabsItem>
        <TabsItem
          iconName="tri-infos-circle"
          active={activeTabIndex === 1}
          onClick={() => {
            setActiveTabIndex(1);
            handleFilterClick('créé');
          }}
        >
          {`Créé: ${countCréé.toString()}`}
        </TabsItem>
        <TabsItem
          iconName="tri-check-circle"
          active={activeTabIndex === 2}
          onClick={() => {
            setActiveTabIndex(2);
            handleFilterClick('validé');
          }}
        >
          {`Validé: ${countValidé.toString()}`}
        </TabsItem>
        <TabsItem
          iconName="tri-times-circle"
          active={activeTabIndex === 3}
          onClick={() => {
            setActiveTabIndex(3);
            handleFilterClick('rejeté');
          }}
        >
          {`Rejeté: ${countRejeté.toString()}`}
        </TabsItem>
      </Tabs>

      <div className="tab-content">
        {sortGenerationsByStatus(filteredGenerations).map((generation, genIndex) => (
          <div className="tag-container" key={genIndex}>
            <Tag
              iconName={getIconName(generation.statut)}
              variant={getTagVariant(generation.statut)}
              onClick={() => handleTagClick(generation.id)}
              inverted={selectedTagId === generation.id}
            >
              {generation.titre}
            </Tag>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GenerationsContainer;