import React from 'react'
import { Section, Title, TitleLevels, Tile, Box, Text } from '@bytel/trilogy-react-ts'

export const TileScreen = (): JSX.Element => {
  return (
    <Section>
      <Tile className='is-ancestor'>
        <Tile className='is-parent'>
          <Box className='tile is-child'>
            <Title level={TitleLevels.THREE}>Mon mobile</Title>
            <Text><PERSON><PERSON><PERSON>, dépanner</Text>
          </Box>
        </Tile>
        <Tile className='is-parent'>
          <Box className='tile is-child'>
            <Title level={TitleLevels.THREE}>Mon offre</Title>
            <Text>Forfaits & Options</Text>
          </Box>
        </Tile>
        <Tile className='is-parent'>
          <Box className='tile is-child'>
            <Title level={TitleLevels.THREE}>Ma conso</Title>
            <Text>Dépassement forfait</Text>
          </Box>
        </Tile>
      </Tile>
    </Section>
  )
}
