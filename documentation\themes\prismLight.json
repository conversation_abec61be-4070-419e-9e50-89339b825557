{"plain": {"color": "#393A34", "backgroundColor": "#f6f8fa"}, "styles": [{"types": ["comment", "prolog", "doctype", "cdata"], "style": {"color": "#999988", "fontStyle": "italic"}}, {"types": ["namespace"], "style": {"opacity": 0.7}}, {"types": ["string", "attr-value"], "style": {"color": "#e3116c"}}, {"types": ["punctuation", "operator"], "style": {"color": "#393A34"}}, {"types": ["entity", "url", "symbol", "number", "boolean", "variable", "constant", "property", "regex", "inserted"], "style": {"color": "#36acaa"}}, {"types": ["at<PERSON>le", "keyword", "attr-name", "selector"], "style": {"color": "#00a4db"}}, {"types": ["function", "deleted", "tag"], "style": {"color": "#d73a49"}}, {"types": ["function-variable"], "style": {"color": "#6f42c1"}}, {"types": ["tag", "selector", "keyword"], "style": {"color": "#00009f"}}, {"types": ["title"], "style": {"color": "#0550AE", "fontWeight": "bold"}}, {"types": ["parameter"], "style": {"color": "#953800"}}, {"types": ["boolean", "rule", "color", "number", "constant", "property"], "style": {"color": "#005CC5"}}, {"types": ["at<PERSON>le", "tag"], "style": {"color": "#22863A"}}, {"types": ["script"], "style": {"color": "#24292E"}}, {"types": ["operator", "unit", "rule"], "style": {"color": "#D73A49"}}, {"types": ["font-matter", "string", "attr-value"], "style": {"color": "#C6105F"}}, {"types": ["class-name"], "style": {"color": "#116329"}}, {"types": ["attr-name"], "style": {"color": "#0550AE"}}, {"types": ["keyword"], "style": {"color": "#CF222E"}}, {"types": ["function"], "style": {"color": "#8250DF"}}, {"types": ["selector"], "style": {"color": "#6F42C1"}}, {"types": ["variable"], "style": {"color": "#E36209"}}, {"types": ["comment"], "style": {"color": "#6B6B6B"}}]}