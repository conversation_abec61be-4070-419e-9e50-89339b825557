"""
modèles de données commun à l'ensemble des agents IA.
Chaque agent peut ensuite hériter de ces modèles et implémenter ses propres contrôles
"""

import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum

from langchain_community.callbacks import get_openai_callback
from langchain_core.documents import Document
from langchain_openai.chat_models import AzureChatOpenAI
from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, ConfigDict, Field, model_validator
from typing_extensions import Self

from .openapi.models import BouchonsServiceIA, FormatBouchon

# class FormatBouchon(Enum):
#     """
#     enumeration des formats de bouchons pris en charge
#     """

#     WIREMOCK = "Wiremock"
#     VF = "Virtual Front"


# class ImplementationBouchons(BaseModel):
#     """
#     représente l'implémentation d'un ou plusieurs bouchons dans un format spécifique
#     """

#     format: FormatBouchon
#     contenu: str


class FormatExigence(Enum):
    """
    les types d'exigences qui sont supportées par les agents IA
    """

    TEXTE_LIBRE = "langage naturel"
    USER_STORY = "user story"
    OPEN_API = "OpenAPI"


class FormatTest(Enum):
    """
    les type de procédures de tests via IA qui sont disponibles
    """

    MANUAL = "Xray"
    POSTMAN = "Postman"  # TODO: à virer au profit de BRUNO ?
    ROBOT = "Robot Framework"  # TODO: défini
    BRUNO = "Bruno"  # TODO: à définir


class TypeExigence(BaseModel):
    """
    la réprésentation d'une exigence de manière à pouvoir l'adresser vers le bon agent IA
    """

    type: FormatExigence
    identifiant: str | int | uuid.UUID | None = Field(
        None, description="la référence de l'exigence à reprendre dans les tests produits si celle-ci est fournie"
    )
    data: str = Field(description="les données brutes liés au format. à date du str suffit, mais on aura possible des bytes par la suite")


class TestImplementationIA(BaseModel):
    """
    une implémentation d'un test (manuel avec des étapes de test, automatisé dans un langage ou framework défini...)
    qui a été générée via IA.
    """

    date_generation: datetime = Field(default_factory=datetime.now)
    format: FormatTest
    contenu: str


class CasDeTestIA(BaseModel):
    """
    le modèle de données pour un test identifié par IA
    tout les typologies plus spécifiques (ex: TestOpenApi) respectent ce modèle
    mais implémentent des précisions supplémentaires pour orienter la génération
    """

    titre: str = Field(description="")
    reference_exigence: str | int | uuid.UUID | None = Field(None, description="la référence de l'exigence si celle-ci a été fournie")
    description: str
    date_generation: datetime = Field(default_factory=datetime.now)
    implementations: list[TestImplementationIA] | None = None


class StatistiquesIA(BaseModel):
    """
    métadonnées utiles liés à l'utilisation d'un LLM
    """

    models: list = Field(description="la liste des modèles LLM utilisés")
    total_tokens: int = Field(description="le nombre total de tokens utilisés. (somme de prompt + completion)")
    prompt_tokens: int = Field(description="ce qui a été poussé en entrée du LLM")
    completion_tokens: int = Field(description="ce qui a été créé en sortie par le LLM")
    reasoning_tokens: int | None = Field(
        description='les tokens utilisés à "réfléchir". on retrouve ces token sur les modèles qui sont basés sur la notion de reflexion agents'
    )
    total_cost: float = Field(description="le cout total de l'utilisation. il s'agit d'un calcul basé sur le modele et les tokens utilisés")


class OptionsIdentification(BaseModel):
    """
    les options non-obligatoire qui peuvent être demandée lors de l'identification.
    Si elle ne sont pas compatibles avec la demande globale, on les ignore.
    """

    identifier_bouchons: bool = Field(
        False,
        description="Si sélectionnée et que des specs OpenAPI sont présentes,réalisé l'identification des exemples d'appels qui servirait de bouchons",
    )
    formats_bouchons: list[FormatBouchon] | None = Field(None, description="le(s) format(s) d'écriture des bouchons")
    formats_tests: list[FormatTest] | None = Field(None, description="le(s) format(s) d'écriture des tests")


class WorkflowIdentificationState(BaseModel):
    """
    le modèle de données qui stocke l'état workflow principal d'identification.
    il est partagé par tous les agents
    """

    model_config = ConfigDict(extra="allow")
    exigence: TypeExigence
    options: OptionsIdentification | None = None
    documents: list[Document] | None = Field(None, description="la liste des documents contextuels à utiliser")
    tests: list[CasDeTestIA] | None = None
    stats: StatistiquesIA | None = None
    bouchons: BouchonsServiceIA | None = None

    @model_validator(mode="after")
    def validation_des_entrant(self) -> Self:
        """
        vérifie que l'on a au moins un type d'entrant parmi exigences/tests/bouchons.
        """
        print(self)
        no_exigences = self.exigence is None
        no_tests = self.tests is None or len(self.tests) == 0
        no_mocks = self.bouchons is None

        if no_exigences and no_tests and no_mocks:
            raise ValueError("Aucun élément à générer via IA fourni !")

        return self


class AgentIA(ABC):
    """
    class permettant de regrouper et construire automatiqument un agent IA basé sur langgraph.
    Cela à l'avantage notamment de pouvoir regrouper dans un seul point d'entrée tout ce qui concerne langgraph.

    à savoir:

        - la définition du workflow (Stategraph)
        - la construction du graph qui sera utilisé par la suite (CompiledGraph)
        - le LLM qui va être utilisé
        - les tools qui vont utilisés dans certaines étapes
        - tout autre configuration pouvant s'avérer utile (persistance, thread...)
        - le suivi des coûts liés au LLM

    Comment implémenter:

        1. faire un héritage de AgentIABuilder
        2. créer vos méthodes de classe qui vont servir de noeud dans langgraph (add_node)
           en vous assurant de bien utiliser le llm de l'agent (self.llm, self.llm_json)
        3. implémenter la méthode build en utilisant vos méthodes précédemment créé (self.builder.add_node(self.ma_super_methode))

    """

    def __init__(self, llm: AzureChatOpenAI, llm_json: AzureChatOpenAI, state_schema: WorkflowIdentificationState):
        self.log = logging.getLogger(f"{self.__module__}.{self.__class__.__name__}")
        self.state_schema = state_schema
        self.builder = StateGraph(state_schema=self.state_schema)
        self.llm = llm
        self.llm_json = llm_json
        self.graph = self.build()
        self.models_names = self._register_llm_models()

    def _register_llm_models(self):
        """
        récupère les noms exacts des modèles utilisés par l'agent et valide au passage qu'ils fonctionnent
        """
        # TODO: voir pour une meilleure option que celle-ci.
        # Là, en l'état si on n'a N Agent, il y aura 2*N appels réalisés pour connaitre les nom des modèles
        # Idéalement, il faut que ce soit le bootstrap qui alimente cette information au début.
        # Comme ça on est à la fois sûr que les LLM sont accessible au démarrage
        # et que l'on appelle uniquement lors de l'initialisation des modèles au lieu de l'initialisation des Agents
        models_names = []
        models_names.append(self.llm.invoke("").response_metadata.get("model_name"))
        models_names.append(self.llm_json.invoke("json").response_metadata.get("model_name"))
        return list(set(models_names))

    @abstractmethod
    def build(self) -> CompiledStateGraph:
        """
        implémenter ici la construction de votre graph (node, edge, conditional_edge...) puis retourner le graph compilé via self.builder.compile()
        """
        pass

    def run(self, state_in: WorkflowIdentificationState) -> WorkflowIdentificationState:
        """
        lance l'exécution de l'agent IA avec suivi des coûts
        """
        self.log.info("lancement de l'identification...")
        with get_openai_callback() as llms_usage:
            result = self.graph.invoke(state_in)
            self.log.debug("resultat langgraph: %s", result)
            state_out = WorkflowIdentificationState(**result)
            self.log.debug("sérialisation pydantic: %s", state_out)
            self.log.info("identification terminée. récupération des statistiques liées aux LLM...")
            state_out.stats = StatistiquesIA(
                models=self.models_names,
                total_tokens=llms_usage.total_tokens,
                prompt_tokens=llms_usage.prompt_tokens,
                completion_tokens=llms_usage.completion_tokens,
                reasoning_tokens=llms_usage.reasoning_tokens,
                total_cost=llms_usage.total_cost,
            )
            self.log.info("récupération des statistiques terminée.")
            return state_out
