"""
les fonctions de scraping liées aux contenus brut récupéré de confluence
"""

import logging
import re

from bs4 import BeautifulSoup, _typing
from cleantext import clean
from html_to_markdown import convert_to_markdown
from pydantic_core import ValidationError

from app.common.exceptions import ErreurScraping, ParsingException
from app.domains.extraction.models import ErreurExtractionDocumentUnique, ExigenceDocumentUnique

LOGGER = logging.getLogger(__name__)


class PageDocumentUnique:
    def __init__(self, id: str, content: str, title: str, url: str):
        self.id = id
        self.content = content
        self.title = title
        self.url = url


# scrapingRequirements
def exigences_pages_du(pages_du: list[PageDocumentUnique]) -> tuple[list[ExigenceDocumentUnique], list[ErreurExtractionDocumentUnique]]:
    """
    réalise le scraping à partir d'une liste de `pages` fourni.
    le dictionnaire doit obligatoirement contenir, l'identifiant de
    """
    LOGGER.info("extractions des exigences à partir des %i pages trouvées pour le DU", len(pages_du))
    exigences_du: list[ExigenceDocumentUnique] = []
    erreurs_du: list[ErreurExtractionDocumentUnique] = []

    for page_du in pages_du:
        exigences_page, erreurs_page = exigences_page_du(page_du)
        exigences_du.extend(exigences_page)
        erreurs_du.extend(erreurs_page)

    LOGGER.info("extraction globale terminée (%i exigences extraites, %i erreurs rencontrées).", len(exigences_du), len(erreurs_du))
    return exigences_du, erreurs_du


# requirementsByPageId
def exigences_page_du(page_du: PageDocumentUnique) -> tuple[list[ExigenceDocumentUnique], list[ErreurExtractionDocumentUnique]]:
    """
    récupération brute des tableaux d'exigences contenu dans une page d'un DU
    """
    LOGGER.info("recherche d'exigences dans le contenu de la page %s", page_du.id)
    requirements = []
    erreurs_scraping = []
    parser = BeautifulSoup(page_du.content, "html.parser")

    tables = parser.find_all("table", recursive=False)

    LOGGER.debug("%i tableaux présents dans la page", len(tables))

    for index, table in enumerate(tables, start=1):
        LOGGER.debug("iteration sur le tableau %i", index)
        usecase_title = table.find_previous_sibling(re.compile("^h[1-6]$"), string=re.compile("^Use"))

        try:
            found_requirements = exigences_tableau_du(table)
            LOGGER.debug("%i extraites à partir du tableau", len(found_requirements))
        except ParsingException as err:
            LOGGER.error("erreur de scraping rencontrée sur la page %s", page_du.id)
            if LOGGER.getEffectiveLevel() == logging.INFO:
                details = None
            else:
                details = ErreurScraping(raw_content=err.raw_content, error_message=err.error_message, comment=err.comment)
            erreurs_scraping.append(
                ErreurExtractionDocumentUnique(
                    id=page_du.id,
                    url=page_du.url,
                    message=f"Erreur lors de l'extraction du tableau {index} qui est censé contenir des exigences",
                    details=details,
                )
            )

        if usecase_title is not None:
            usecase_title = clean(usecase_title.get_text(), extra_spaces=True, stopwords=True)
            LOGGER.debug("Usecase trouvé pour ce tableau d'exigence: %s", usecase_title)

        if len(found_requirements) > 0:
            LOGGER.debug("ajout des metadonnées aux exigences trouvées (page_id: %s, usecase: %s)", page_du.id, usecase_title)
            for requirement in found_requirements:
                requirement["description"] = f"exigence extraite depuis la page [{page_du.title}]({page_du.url})"
                requirement["metadonnees"] = {"usecase": usecase_title, "source": {"id": page_du.id, "titre": page_du.title, "url": page_du.url}}

        requirements.extend(found_requirements)

    LOGGER.debug("extraction des tableaux terminée")
    LOGGER.debug("convertion en exigences...")
    exigences = []

    for req in requirements:
        LOGGER.debug("sérialisation de l'exigence '%s' (%s)", req["nom"], req["metadonnees"])
        try:
            exigences.append(
                ExigenceDocumentUnique(
                    nom=req["nom"], description=req["description"], data=req["markdown"], porteur=req["responsable"], metadonnees=req["metadonnees"]
                )
            )
        except ValidationError as err:
            messages = ", ".join([error.get("msg") for error in err.errors(include_url=False, include_context=False)])
            erreurs_scraping.append(
                ErreurExtractionDocumentUnique(
                    id=page_du.id,
                    url=page_du.url,
                    message=f"Erreur lors de la sérialisation de l'exigence '{req['nom']}' : {messages}",
                )
            )
    LOGGER.debug("convertion terminée (%i exigences extraites, %i erreurs rencontrées).", len(exigences), len(erreurs_scraping))
    return exigences, erreurs_scraping


def remove_confluence_tags(raw_html: str) -> str:
    """
    retire toute les tags xhtml liés aux macros confluence (ex: <ac:tructured-macro>, <ac:tasks>, <ac:image>...).
    """

    LOGGER.debug("nettoyage du contenu confluence d'une exigence")

    LOGGER.debug("avant retrait macros: %s", raw_html)
    # les commentaires inline peuvent se retirer directement puisque directement dans du texte
    raw_html = re.sub(r"<ac:inline-comment[^>]+>", "", raw_html)
    raw_html = re.sub(r"<\/ac:inline-comment[^>]+>", "", raw_html)

    # les autres macros sont à retirer via beautifull soup
    raw_html = re.sub(r"<ac:[^>]+>", "<toremove>", raw_html)
    raw_html = re.sub(r"<\/ac:[^>]+>", "</toremove>", raw_html)

    LOGGER.debug("avant parsing BeautifulSoup")
    parsed_html = BeautifulSoup(raw_html, "html.parser")
    remove_tags = parsed_html.find_all("toremove", recursive=True)

    for tag in remove_tags:
        tag.decompose()

    LOGGER.debug("après retrait macros: %s", raw_html)
    cleaned_html = f"{parsed_html}"
    return cleaned_html


# getRequirement
def exigences_tableau_du(table: _typing._OneElement) -> list[dict]:
    if table.tbody is None:
        # le tableau est vide donc pas d'exigences à extraire
        return []
    try:
        rows = table.tbody.find_all("tr")
    except Exception as parsing_error:
        # le tableau n'est pas vide mais visiblement on a pas réussi à récupérer les lignes
        raise ParsingException(raw_content=str(table), error_message=f"{parsing_error}", comment="les lignes du tableau n'ont pas été obtenues")

    exigences = []

    for index, row in enumerate(rows, start=1):
        LOGGER.debug("parsing de la ligne %i", index)
        try:
            exigence = parse_exigence(row)
            if exigence is not None:
                LOGGER.debug("exigence extraite")
                exigences.append(exigence)
            else:
                LOGGER.debug("la ligne n'est pas une exigence")
        except Exception as parsing_error:
            raise ParsingException(
                raw_content=str(row), error_message=f"{parsing_error}", comment=f"Erreur de parsing sur la ligne {index} du tableau d'exigences"
            )

    return exigences


def parse_exigence(row: _typing._OneElement) -> dict | None:
    raw_exigence_title = row.find("ac:task-list")

    if raw_exigence_title is not None:
        titre_exigence = raw_exigence_title.find("ac:task-body").get_text()
        titre_exigence = clean(titre_exigence, extra_spaces=True, stopwords=True)
        split_titre = re.split(pattern=r"(–|-)", string=titre_exigence, maxsplit=1)
        porteur = "N/A"
        if split_titre[0] != titre_exigence:
            porteur = split_titre[0].strip().upper()
            titre_exigence = split_titre[2].strip()

        # pour garantir d'avoir le contenu correctement transformé en markdown,
        # on prend la balise td et la renomme de manière à ce que les tableaux imbriqués soient correctement rendus
        contenu_exigence = row.find("td")
        contenu_exigence.name = "div"
        contenu_exigence.attrs = {"id": "exigence"}

        contenu_exigence_html = remove_confluence_tags(str(contenu_exigence))
        contenu_exigence_markdown = convert_to_markdown(contenu_exigence_html)

        return {"nom": titre_exigence, "html": contenu_exigence_html, "responsable": porteur, "markdown": contenu_exigence_markdown}
