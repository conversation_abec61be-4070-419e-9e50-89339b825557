import React from 'react'
import { AutoLayout, Box, BoxContent, Section, Spacer, SpacerSize, Sticker, StickerVariant } from '@bytel/trilogy-react-ts'

export const StickerScreen = (): JSX.Element => {
  return (
    <>
      <AutoLayout>
        <Section>
          <Sticker variant='PRIMARY'>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorem fugit, praesentium corporis veritatis nulla voluptas eos, tenetur ratione, natus totam animi veniam? Iste maiores quidem, id nisi accusamus quos vero.</Sticker>
        </Section>
        <Section>
          <Sticker variant='PRIMARY'>PRIMARY</Sticker>
          <Sticker variant='SECONDARY'>SECONDARY</Sticker>
          <Sticker variant='TERTIARY'>TERTIARY</Sticker>
          <Sticker variant='REFURB'>REFURB</Sticker>
        </Section>

        <Section>
          <Sticker variant='PRIMARY' outlined>
            PRIMARY
          </Sticker>
          <Sticker variant='SECONDARY' outlined>
            SECONDARY
          </Sticker>
          <Sticker variant='TERTIARY' outlined>
            TERTIARY
          </Sticker>
          <Sticker variant='REFURB' outlined>
            REFURB
          </Sticker>
        </Section>

        <Section>
          <Sticker variant='PRIMARY' flag>
            PRIMARY
          </Sticker>
          <Sticker variant='SECONDARY' flag>
            SECONDARY
          </Sticker>
          <Sticker variant='TERTIARY' flag>
            TERTIARY
          </Sticker>
          <Sticker variant='REFURB' flag>
            REFURB
          </Sticker>
        </Section>

        {/* SMALL */}

        <Section>
          <Sticker variant={StickerVariant.PRIMARY} small>
            PRIMARY
          </Sticker>
          <Sticker variant={StickerVariant.SECONDARY} small>
            SECONDARY
          </Sticker>
          <Sticker variant={StickerVariant.TERTIARY} small>
            TERTIARY
          </Sticker>
          <Sticker variant={StickerVariant.REFURB} small>
            REFURB
          </Sticker>
        </Section>

        <Section>
          <Sticker variant='PRIMARY' outlined small>
            PRIMARY
          </Sticker>
          <Sticker variant='SECONDARY' outlined small>
            SECONDARY
          </Sticker>
          <Sticker variant='TERTIARY' outlined small>
            TERTIARY
          </Sticker>
          <Sticker variant='REFURB' outlined small>
            REFURB
          </Sticker>
        </Section>

        <Section>
          <Sticker variant='PRIMARY' flag small>
            PRIMARY
          </Sticker>
          <Sticker variant='SECONDARY' flag small>
            SECONDARY
          </Sticker>
          <Sticker variant='TERTIARY' flag small>
            TERTIARY
          </Sticker>
          <Sticker variant='REFURB' flag small>
            REFURB
          </Sticker>
        </Section>

        <Spacer size={SpacerSize.HUGE} />
        <Box hat>
          <Sticker hat>Labellaa</Sticker>
          <BoxContent></BoxContent>
        </Box>
      </AutoLayout>
    </>
  )
}
