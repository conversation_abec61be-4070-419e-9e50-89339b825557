import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import cat_ia from 'layout/cat_white.png';
import {
  Box,
  BoxContent,  
  Section,
  Text,
  TextLevels,
  Title,
  TitleLevels,
  View
} from "@bytel/trilogy-react-ts";
import {
  TypographyAlign,
  TypographyBold
} from "@bytel/trilogy-react-ts";
interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  fallbackPath?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  fallbackPath = '/',
}) => {
  const { user, isLoading, isAdmin } = useAuth();

  // Logs de débogage
  console.log('ProtectedRoute - Debug:', {
    user,
    isLoading,
    isAdmin,
    requireAdmin
  });

  // Afficher un loader pendant la vérification
  if (isLoading) {
    return (
        <Section>
        <Box>
          <BoxContent>
            <View>
              <Title
                level={TitleLevels.ONE}
                typo={[TypographyAlign.TEXT_CENTERED]}
              >
                <img
                  src={cat_ia}
                  style={{
                    width: "80px",
                    height: "80px",
                    verticalAlign: "middle",
                    marginRight: "20px"
                  }}
                  alt="CatIA icon"
                />
                
              </Title>
              <Text
                level={TextLevels.ONE}
                typo={[
                  TypographyAlign.TEXT_CENTERED,
                  TypographyBold.TEXT_WEIGHT_MEDIUM
                ]}
              >
                Vérification des permissions...</Text>
            </View>
          </BoxContent>
        </Box>
      </Section>
    );
  }

  // Vérifier si l'utilisateur est connecté
  if (!user) {
    console.log('ProtectedRoute - Pas d\'utilisateur connecté');
    return <Navigate to={fallbackPath} replace />;
  }

  // Vérifier les permissions d'admin si requises
  if (requireAdmin && !isAdmin) {
    console.log('ProtectedRoute - Accès refusé, pas admin');
    return (
        <Section>
        <Box>
          <BoxContent>
            <View>
              <Title
                level={TitleLevels.ONE}
                typo={[TypographyAlign.TEXT_CENTERED]}
              >
                <img
                  src={cat_ia}
                  style={{
                    width: "80px",
                    height: "80px",
                    verticalAlign: "middle",
                    marginRight: "20px"
                  }}
                  alt="CatIA icon"
                />
                
              </Title>
              <Text
                level={TextLevels.ONE}
                typo={[
                  TypographyAlign.TEXT_CENTERED,
                  TypographyBold.TEXT_WEIGHT_MEDIUM
                ]}
              >
                Vous n'avez pas les permissions nécessaires pour accéder à cette page.</Text>
            </View>
          </BoxContent>
        </Box>
      </Section>      
    );
  }

  console.log('ProtectedRoute - Accès autorisé');
  return <>{children}</>;
};