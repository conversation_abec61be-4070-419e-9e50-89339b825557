import React, { useState, Fragment } from 'react';
import { Title, TitleLevels, Icon, Tabs, TabsItem, Text, Button,Popover,Tag,TagList,Accordion,AccordionItem,AccordionHeader,AccordionBody } from '@bytel/trilogy-react-ts';
import { Generation } from 'services/interfaces/generationInterfaces';
import { useSnackbar } from 'providers/snackbar';
import { AxiosError } from 'axios';
import { FaRegClipboard  } from 'react-icons/fa'; 
import ActionDropdown from 'components/ActionDropdown';
import { CodeBlock,github,a11yDark} from 'react-code-blocks';
import postmanlogo from '../layout/postman-logo.svg';
interface GenerationDetailContainerProps {
  activeGeneration: Generation | null;
  handleValidate: (generation_id: string) => void;
  handleReject: (generation_id: string) => void;
  handleDownload: (generation_id: string, formatExport: string) => void;
  onClose?: () => void;
}

const GenerationDetailContainer: React.FC<GenerationDetailContainerProps> = ({
  activeGeneration,
  handleValidate,
  handleReject,
  handleDownload,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<number>(0);

  const { showError, showInfo } = useSnackbar();

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'créé':
        return 'status-blue';
      case 'validé':
        return 'status-green';
      case 'rejeté':
        return 'status-red';
      default:
        return '';
    }
  };

  if (!activeGeneration) {
    return null;
  }

  const implementations = activeGeneration.implementations || [];
  const downloadImplementation = (implementation,id) => {
    if (!implementation.contenu) return;
    
    const content = JSON.stringify(JSON.parse(implementation.contenu), null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `implementation-${implementation.format.id}.json`;
    document.body.appendChild(link);
    link.click();
    
    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  const parseXrayContent = (content: string) => {
    try {
      const parsedContent = JSON.parse(content);
      return parsedContent.etapes || [];
    } catch (error) {
      console.error('Error parsing content', error);
      return [];
    }
  };

  const parsePostmanContent = (content: string) => {
    try {
      const parsedContent = JSON.parse(content);
      
      const collectionScripts = parsedContent.event?.length > 0 ? parsedContent.event : []
      
      if (!parsedContent.item || !Array.isArray(parsedContent.item)) {
        return [];
      }
      
      return parsedContent.item.map((item: any) => {
        // Extract the path from the URL if it exists
        let path = '';
        if (item.request && item.request.url) {
          if (typeof item.request.url === 'object' && item.request.url.path) {
            path = '/' + item.request.url.path.join('/');
          } 
          else if (typeof item.request.url === 'object' && item.request.url.raw) {
            const urlParts = item.request.url.raw.split('/');
            // Remove protocol and domain parts
            const pathParts = urlParts.slice(3);
            path = '/' + pathParts.join('/');
          }
          else if (typeof item.request.url === 'string') {
            const urlParts = item.request.url.split('/');
            const pathParts = urlParts.slice(3);
            path = '/' + pathParts.join('/');
          }
        }
        
        return {
          name: item.name || '',
          method: item.request?.method || '',
          path: path,
          request: item.request || {},
          response: item.response || [],
          code: item.response && item.response.length > 0 ? item.response[0].code || '' : '',
          event: item.event || collectionScripts
        };
      });
    } catch (error) {
      console.error('Error parsing Postman content', error);
      return [];
    }
  };
  interface StepFieldProps {
    title: string;
    content: React.ReactNode;
  }

  const StepField: React.FC<StepFieldProps> = ({ title, content }) => {
    const handleCopy = () => {
      const textToCopy =
        content && typeof content === 'object' && 'props' in content && content.props && 'children' in content.props
          ? content.props.children ?? ''
          : '';
      navigator.clipboard.writeText(textToCopy)
        .then(() => {
          showInfo('Copié dans le presse-papiers !');
        })
        .catch(err => {
          showError(err as AxiosError, { message: "Impossible de copier le texte " });
        });
    };

    return (
      <div className="step-fields">
        <div className="field-header">
          <strong>
              <>
                <Popover direction="right" content={`Copier ${title}`}>
                  <FaRegClipboard onClick={handleCopy}/>
                </Popover>
              </>
            {title}
          </strong>

        </div>
        <div className="field-view-mode">
          <div className="renderer-document">
            <Text>{content}</Text>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="generation-detail-container">
      <Title level={TitleLevels.THREE}>{activeGeneration.titre}</Title>
      
      <div className="status-row">
        <div className="status-text">
          <Title level={TitleLevels.THREE} className="status-title">
            Statut :
          </Title>
          <span className={`status-value ${getStatusClass(activeGeneration.statut)}`}>
            {activeGeneration.statut}
          </span>
        </div>
        <div className="status-buttons">
          {activeGeneration.statut === 'créé' && (
            <>
              <Button variant="SUCCESS" className="status-button" onClick={() => handleValidate(activeGeneration.id)}>
                Valider
              </Button>
              <Button variant="DANGER" className="status-button" onClick={() => handleReject(activeGeneration.id)}>
                Rejeter
              </Button>
            </>
          )}

          {activeGeneration.statut === 'validé' && (  
            <Button variant="DANGER" className="status-button" onClick={() => handleReject(activeGeneration.id)}>
              Rejeter
            </Button>               
          )}

          {activeGeneration.statut === 'rejeté' && (
            <Button variant="SUCCESS" className="status-button" onClick={() => handleValidate(activeGeneration.id)}>
              Valider
            </Button>
          )}
        </div>
      </div>

      <Tabs activeIndex={activeTab}>
        <TabsItem active={activeTab === 0} onClick={() => setActiveTab(0)}>
          <Title level={TitleLevels.THREE}>Description</Title>
        </TabsItem>
        {implementations.map((implementation, index) => (
          <TabsItem key={index} active={activeTab === index + 1} onClick={() => setActiveTab(index + 1)}>
            <Title level={TitleLevels.THREE}>{implementation.format}</Title>
          </TabsItem>
        ))}
      </Tabs>

      {activeTab === 0 && (
        <div className="generation-description" style={{ whiteSpace: 'pre-wrap' }}>
          {activeGeneration.description}
        </div>
      )}

      {implementations.map((implementation, index) => 
        activeTab === index + 1 && (
          <div key={index} className="generation-implementation" style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', overflowWrap: 'break-word', overflowY: 'auto', maxWidth: '100%' ,paddingTop: '2%' }}>
            {implementation.format === 'Xray' && (
              // Xray format content
              <>
              {activeGeneration.statut === 'validé' && ( 
              <div className="generation-implementation-header" style={{ display: 'inline', justifyContent: 'space-between', alignItems: 'center' }}>
                <div className="right-content" style={{ marginLeft: 'auto', float: 'inline-end' }}>
                  <ActionDropdown
                    isExtended={true}
                    isActive={true}
                    handleDownload={handleDownload}
                    generationId={activeGeneration.id}
                    typeTelechargement="testcase"
                    NameExport={activeGeneration.titre}
                    formatimplementation={implementation.format}
                  />
                </div>
              </div>
            )}
                {parseXrayContent(String(implementation.contenu)).map((step: any, stepIdx: number) => (
                  <Fragment key={stepIdx}>
                    <StepField title="Action" content={<Text level='ONE'>{step.action}</Text>} />
                    <StepField title="Données" content={<Text level='ONE'>{step.data}</Text>} />
                    <StepField title="Résultat Attendu" content={<Text level='ONE'>{step.result}</Text>} />
                  </Fragment>
                ))}
              </>
            )}
            
            {implementation.format === 'Postman' && (
              
              <div className="postman-content">
              <Title >
                      {/* Bouchon download :   */}
                      <Button 
                        className="button is-secondary" 
                        style={{ backgroundColor: "rgb(228, 230, 232)" }}
                        onClick={() => {          
                            downloadImplementation(implementation,activeGeneration.id);                          
                        }}
                      >
                        <span><img style={{ width: "84px" }} src={postmanlogo} alt="Postman Logo"/></span>
                        <span className="icon">
                              <Icon name="tri-download" size="small" color='blue' style={{ marginLeft: 40 }}/>
                            
                        </span>
                      </Button>
                  </Title>
                <Accordion open={true} id="postman-accordion">
                  {parsePostmanContent(String(implementation.contenu)).map((item: any, itemIdx: number) => (
                    <AccordionItem key={itemIdx} id={`postman-item-${itemIdx}`}>
                      <AccordionHeader>
                        <TagList>
                          <Tag>{item.method}</Tag>                          
                          <Tag>{item.code}</Tag>
                          <Tag>{item.path}</Tag>
                          <Tag>{item.name}</Tag>
                        </TagList>
                      </AccordionHeader>
                      <AccordionBody data-id={`postman-body-${itemIdx}`}>
                        
                        
                        {item.event && item.event.some(evt => evt.listen === "test") && (
                          <div className="postman-tests">
                            {/* <Text>Tests</Text> */}
                            {item.event
                              .filter(evt => evt.listen === "test")
                              .map((evt, evtIdx) => (
                                <CodeBlock 
                                  key={evtIdx}
                                  codeContainerStyle={{ backgroundColor: a11yDark.backgroundColor }}
                                  text={evt.script.exec.join("\n")}
                                  language="javascript"
                                  showLineNumbers={false}
                                  theme={a11yDark}
                                  wrapLongLines={true}
                                />
                              ))}
                          </div>
                        )}
                      </AccordionBody>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            )}
          </div>
        )
      )}
    </div>
  );
};

export default GenerationDetailContainer;
