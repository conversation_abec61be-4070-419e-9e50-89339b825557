"""
les modèles de données utilisés pour les specifications de type Open API
"""

import uuid
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class FormatBouchon(Enum):
    """
    enumeration des formats de bouchons pris en charge
    """

    WIREMOCK = "Wiremock"
    VF = "Virtual Front"


class ImplementationBouchons(BaseModel):
    """
    représente l'implémentation d'un ou plusieurs bouchons dans un format spécifique
    """

    format: FormatBouchon
    contenu: str


class ReponseApi(BaseModel):
    """
    Représente une réponse minimale attendue pour l'utilisation d'un service en se basant uniquement sur la documentation OAS
    """

    service_path: str
    http_method: str
    status_code: int


class CasUsageAPI(BaseModel):
    """
    definition d'un exemple d'appel à une API web
    """

    description: str = Field(..., description="une description du cas d'usage d'appel au service")
    appel: str = Field(..., description="la requête HTTP faite au service pour le cas d'usage décrit")
    reponse: str = Field(..., description="la réponse HTTP obtenu du service pour le cas d'usage décrit")
    service_path: str | None = Field(..., description="la valeur du path dans la spécification OpenAPI pour le cas d'usage décrit")
    http_method: str = Field(..., description="la méthode HTTP utilisée pour la requête faite au service")
    status_code: int = Field(..., description="le code de statut HTTP de la réponse obtenu du service pour le cas d'usage décrit")


class BouchonsServiceIA(BaseModel):
    """
    le modèle de données pour des bouchons générés par IA à partir d'une specification OpenAPI
    """

    date_generation: datetime = Field(default_factory=datetime.now)
    reference_exigence: str | int | uuid.UUID | None = Field(None, description="la référence de l'exigence si celle-ci a été fournie")
    usages: list[CasUsageAPI] | None = Field(None, description="les cas d'usages identifiés de l'API")
    implementations: list[ImplementationBouchons] | None = Field(None, description="les formats générés pour les cas d'usage identifiés de l'API")
