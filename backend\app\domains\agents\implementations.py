"""
Contient l'ensemble des prompts et instructions pour l'écriture des tests.

On utilise du pydantic pour faire des structured_outputs avec les schema_json produit
"""

import json

from pydantic import BaseModel, Field

from app.domains.agents.models import FormatTest


class FormatEtapeTest(BaseModel):
    """
    le format dans lequel écrire une étape de test manuel
    """

    action: str = Field(description="la ou les action à réaliser")
    data: str = Field(description="les données à utiliser pour l'action")
    result: str = Field(description="le ou les résultat attendu de l'action")


class EcritureTest(BaseModel):
    """
    le format dans lequel écrire un test
    """

    titre: str = Field(description="le titre du cas de test. le nom doit contenir les informations essentiels et ne pas contenir le terme 'test'")
    description: str = Field(
        description="""contient une explication du cas de test.
        Cette explication respecte la convention ci-dessous:

        "Ce test a pour objectif de s'assurer que <ETAT INITIAL> <ACTION> alors <RESULTAT ATTENDU>"

        <ETAT INITIAL> correspond à la situation dans laquelle doit être le sujet de test. Une phrase maximum.
        <ACTION> correspond à l'évènement qui va déclencher un changement sur le sujet de test. Une phrase maximum.
        <RESULTAT ATTENDU> correspond au résultat attendu du sujet de test suit à l'action. Une phrase maximum.

        En plus de cette explication on retrouve ensuite une section pré-requis qui indique les données en entrées nécessaires au cas de test.
        cette section démarre par "Pour réaliser ce cas de test, un jeux de données avec les conditions suivantes va être nécessaire"

        """
    )


class EcritureTestManuel(EcritureTest):
    """
    le format dans lequel écrire un test manuel qui contient donc étapes de test
    """

    etapes: list[FormatEtapeTest] = Field(min_length=1, max_length=10)  # A voir si on fait bouger cette limite par la suite


INSTRUCTIONS_ECRITURE: dict[FormatTest, str] = {}
"""les instuctions à fournir au LLM en fonction du format de sortie souhaité du test"""

INSTRUCTIONS_ECRITURE[FormatTest.POSTMAN] = (
    "écris le test pour une intégration dans une collection postman en t'assurant d'inclure les assertions nécessaires"
)
INSTRUCTIONS_ECRITURE[FormatTest.BRUNO] = "écris le test pour une intégration dans Bruno en t'assurant d'inclure les assertions nécessaires"

INSTRUCTIONS_ECRITURE[FormatTest.MANUAL] = f"réponds en respectant le SCHEMA JSON suivant: {json.dumps(EcritureTestManuel.model_json_schema())}"
