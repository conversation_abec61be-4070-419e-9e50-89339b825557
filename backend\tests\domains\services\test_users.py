from datetime import datetime
from uuid import UUID

import pytest

from app.domains.users.models import UserCreateInput, UserOutput, UserStorage
from app.domains.users.service import UserAlreadyExistsException, UserNotFoundException, UserService
from app.repositories.memory import MemoryUserRepository

# Constants pour les tests
FAKE_UUID = UUID("12345678-1234-5678-1234-************")
FAKE_ADMIN_ID = UUID("*************-5432-9876-************")


@pytest.fixture
def repository():
    return MemoryUserRepository()


@pytest.fixture
def service(repository):
    return UserService(repository=repository)


@pytest.fixture
def sample_user():
    return UserStorage(id=FAKE_UUID, login="test_user", actif=True, admin=False, date_inscription=datetime.now())


@pytest.fixture
def admin_user():
    return UserStorage(id=FAKE_ADMIN_ID, login="admin_user", actif=True, admin=True, date_inscription=datetime.now())


class TestUserService:
    def test_creer_utilisateur(self, service, repository):
        # Arrange
        input_data = UserCreateInput(login="new_user")

        # Act
        result = service.register_user(input_data)

        # Assert
        assert isinstance(result, UserOutput)
        assert result.login == input_data.login
        assert not result.admin
        assert len(repository.entities) == 2  # 1 initial + 1 créé

    def test_creer_utilisateur_deja_existant(self, service, repository, sample_user):
        # Arrange
        repository.add(sample_user)
        input_data = UserCreateInput(login=sample_user.login)

        # Act & Assert
        with pytest.raises(UserAlreadyExistsException):
            service.register_user(input_data)

    def test_lister_utilisateurs(self, service, repository, sample_user):
        # Arrange
        repository.add(sample_user)

        # Act
        result = service.list_users()

        # Assert
        assert result.total >= 1
        assert isinstance(result.users[0], UserOutput)
        assert len(result.users) == result.total

    def test_obtenir_utilisateur_par_login_succes(self, service, repository, sample_user):
        # Arrange
        repository.add(sample_user)

        # Act
        result = service.get_user_by_login(sample_user.login)

        # Assert
        assert isinstance(result, UserOutput)
        assert result.login == sample_user.login
        assert result.id == sample_user.id

    def test_obtenir_utilisateur_par_login_non_trouve(self, service):
        # Act & Assert
        with pytest.raises(UserNotFoundException):
            service.get_user_by_login("nonexistent_user")

    def test_rendre_admin_par_admin(self, service, repository, sample_user, admin_user):
        # Arrange
        repository.add(sample_user)
        repository.add(admin_user)

        # Act
        service.make_admin(sample_user.id, admin_user.id)

        # Assert
        updated_user = repository.get(sample_user.id)
        assert updated_user.admin is True

    def test_rendre_admin_utilisateur_non_trouve(self, service, repository, admin_user):
        # Arrange
        repository.add(admin_user)

        # Act & Assert
        with pytest.raises(UserNotFoundException):
            service.make_admin(FAKE_UUID, admin_user.id)

    def test_mise_a_jour_dirty(self, service, repository, sample_user):
        # Arrange
        repository.add(sample_user)

        # Act
        service.dirty_update(sample_user.login)

        # Assert
        updated_user = repository.get(sample_user.id)
        assert updated_user.admin is True

    def test_mise_a_jour_dirty_utilisateur_non_trouve(self, service):
        # Act & Assert
        with pytest.raises(AttributeError):
            service.dirty_update("nonexistent_user")
