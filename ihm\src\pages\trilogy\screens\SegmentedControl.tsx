import React from 'react'
import { Section, SegmentControl, SegmentControlItem } from '@bytel/trilogy-react-ts'

export const SegmentedControlScreen = (): JSX.Element => {
  return (
    <Section>
      <SegmentControl>
        <SegmentControlItem active onClick={() => alert('Appels')}>
          Appels
        </SegmentControlItem>
        <SegmentControlItem onClick={() => alert('SMS')}>SMS</SegmentControlItem>
        <SegmentControlItem onClick={() => alert('Équipements')}>Équipements</SegmentControlItem>
        <SegmentControlItem disabled>Offres</SegmentControlItem>
      </SegmentControl>
    </Section>
  )
}
