# from io import StringIO
import json
import logging
import uuid
from datetime import datetime

# from io import StringIO
import pandas as pd

from app.common.exceptions import NotFoundException
from app.domains.exigences.models import ExigenceFiltre
from app.domains.exigences.repository import ExigenceRepositoryInterface
from app.domains.exigences.service import ExigenceNotFoundException
from app.domains.users.repository import UsersRepositoryInterface
from app.domains.users.service import UserNotFoundException

from .models import StatutTestIA, TestCaseFilter, TestCaseInput, TestCaseOutput, TestCaseStorage, TestCaseUpdate, TestImplementationStorage
from .repository import TestcasesRepositoryInterface


class TestCaseNotFoundException(NotFoundException):
    message = "Le cas de test n'existe pas"


class TestCasesService:
    """
    le service de gestions des cas de tests générés via IA.
    """

    def __init__(
        self,
        testcases: TestcasesRepositoryInterface,
        # implementations: TestImplementationsRepositoryInterface,
        utilisateurs: UsersRepositoryInterface,
        exigences: ExigenceRepositoryInterface,
    ):
        self._repo_testcases = testcases
        # self.implementations = implementations
        self._repo_utilisateurs = utilisateurs
        self._repo_exigences = exigences
        self._logger = logging.getLogger(__name__)

    def get_testcases_by_workspace(self, workspace_id: uuid.UUID) -> list[TestCaseOutput]:
        """
        Permet de récupérer l'ensemble des cas de test liés à un espace de travail.
        """
        exigences_ids = [exigence.id for exigence in self._repo_exigences.find(ExigenceFiltre(ws_id=workspace_id))]
        testcases = self.get_testcases_by_exigences(exigences_ids=exigences_ids)
        return testcases

    def get_testcases_by_exigences(self, exigences_ids: list[uuid.UUID], statut: str = None) -> list[TestCaseStorage]:
        """
        renvoi l'ensemble des cas de tests liés à l'exigence fourni
        """
        if exigences_ids is None or len(exigences_ids) == 0:
            return []
        filter = TestCaseFilter(exigences_ids=exigences_ids, statut=statut)
        found_testcases = self._repo_testcases.find(filter)
        return found_testcases

    def add_generated_testcases(self, testcase: TestCaseInput) -> None | ExigenceNotFoundException:
        """
        persiste un cas de test généré via IA
        """
        exigence_exist = self._repo_exigences.get(testcase.exigence_id)
        if exigence_exist:
            testcase_dao = TestCaseStorage(**testcase.model_dump(exclude={"implementations"}))
            if testcase.implementations:
                testcase_dao.implementations = [
                    TestImplementationStorage(**implementation.model_dump(), testcase_id=testcase_dao.id)
                    for implementation in testcase.implementations
                ]
            self._repo_testcases.add(dao=testcase_dao)
        else:
            raise ExigenceNotFoundException(identifier=testcase.exigence_id)

    def validate_testcase(self, testcase_id: uuid.UUID, validateur_id: uuid.UUID) -> None | UserNotFoundException:
        """
        valide le cas de test généré via IA
        """
        self._update_testcase(TestCaseUpdate(id=testcase_id, validateur_id=validateur_id, statut=StatutTestIA.VALIDATED))
        self._logger.info(f"Testcase with ID: {testcase_id} has been validated by validator ID: {validateur_id}.")

    def reject_testcase(self, testcase_id: uuid.UUID, validateur_id: uuid.UUID) -> None | UserNotFoundException:
        """
        rejète le cas de test généré via IA
        """
        self._update_testcase(TestCaseUpdate(id=testcase_id, validateur_id=validateur_id, statut=StatutTestIA.REJECTED))

        self._logger.debug(f"Testcase with ID: {testcase_id} has been rejected by validator ID: {validateur_id}.")

    def _update_testcase(self, dto: TestCaseUpdate) -> None | UserNotFoundException:
        """
        logique commune au methode de validation/rejet d'un cas de test
        """
        self._logger.debug(f"Updating testcase with ID: {dto.id} by validator ID: {dto.validateur_id}")
        validateur = self._repo_utilisateurs.get(entity_id=dto.validateur_id)
        if validateur is None:
            raise UserNotFoundException(identifier=dto.validateur_id)

        testcase = self._repo_testcases.get(entity_id=dto.id)
        if testcase is None:
            raise TestCaseNotFoundException(identifier=dto.id)
        dto.date_maj_statut = datetime.now()
        self._repo_testcases.update(dto)

    def _export_testcase(self, testcase_id: uuid.UUID, format: str) -> pd.DataFrame | str:
        """
        Export de testcase par ID.
        """
        self._logger.debug(f"Export testcase avec ID: {testcase_id}")
        testcase = self._repo_testcases.get(entity_id=testcase_id)
        if testcase is None:
            raise TestCaseNotFoundException(identifier=testcase_id)

        self._logger.debug(f"Export testcase avec steps: {testcase.implementations}")
        testcase_implementations = [implementation.contenu for implementation in testcase.implementations]

        testcasesteps = []
        # CSV export
        if format.lower() == "csv":
            Issue = {"Issue_id": 1, "Type": "Manual", "summary": f"{testcase.titre}", "description": f"{testcase.description}"}
            Issue_id = Issue["Issue_id"]
            if testcase_implementations:
                for item in testcase_implementations:
                    try:
                        # Parse JSON content
                        implementation_data = json.loads(item)
                        # Extract steps
                        steps = implementation_data.get("etapes", [])
                        for step in steps:
                            test_summary = Issue["summary"]
                            test_description = Issue["description"]
                            test_type = Issue["Type"]
                            action = step.get("action", "")
                            data = step.get("data", "")
                            result = step.get("result", "")
                            testcasesteps.append([Issue_id, test_type, test_summary, test_description, action, data, result])
                            Issue_id = Issue_id + 1
                    except json.JSONDecodeError:
                        self._logger.error(f"Failed to parse JSON content: {item}")
                    continue

                DataFrame = pd.DataFrame(testcasesteps, columns=["Issue id", "Test Type", "Test Summary", "Description", "Action", "Data", "Result"])

                self._logger.info(f"Testcase avec ID: {testcase_id} export success ")
                return DataFrame

            else:
                test_summary = Issue["summary"]
                test_description = Issue["description"]
                test_type = Issue["Type"]
                testcasesteps.append([Issue_id, test_type, test_summary, test_description])
                DataFrame = pd.DataFrame(testcasesteps, columns=["Issue id", "Test Type", "Test Summary", "Description"])
                Issue_id = Issue_id + 1
                return DataFrame

        # JSON export format
        elif format.lower() == "json":
            result = []
            test_data = {"testtype": "Manual", "fields": {"summary": testcase.titre, "description": testcase.description}}

            if testcase_implementations:
                all_steps = []
                for item in testcase_implementations:
                    try:
                        implementation_data = json.loads(item)
                        steps = implementation_data.get("etapes", [])
                        for step in steps:
                            step_data = {"action": step.get("action", ""), "data": step.get("data", ""), "result": step.get("result", "")}
                            all_steps.append(step_data)
                    except json.JSONDecodeError:
                        self._logger.error(f"Failed to parse JSON content: {item}")
                        continue

                test_data["steps"] = all_steps

            result.append(test_data)
            self._logger.info(f"Testcase avec ID: {testcase_id} export success ")
            return json.dumps(result, indent=4, ensure_ascii=False)

    def export_testcases(self, testcase_ids: [uuid.UUID], format: str) -> pd.DataFrame | str:
        """
        Prépare l'export d'un ou plusieurs cas de test.
        """
        self._logger.debug(f"Préparation de l'export des testcases avec IDs: {testcase_ids}")
        results = []
        for testcase_id in testcase_ids:
            result = self._export_testcase(testcase_id, format)
            if isinstance(result, pd.DataFrame) or isinstance(result, str):
                results.append(result)
            else:
                self._logger.error(f"Erreur lors de l'export du testcase avec ID: {testcase_id}")
        if not results:
            self._logger.warning(f"Aucun résultat valide trouvé pour les IDs: {testcase_ids}")
            return "Aucun résultat valide"
        if format.lower() == "csv":
            combined_df = pd.concat(results, ignore_index=True)
            return combined_df
        elif format.lower() == "json":
            # results est une liste de str JSON, on les combine dans une liste JSON globale
            json_objects = []
            for element in results:
                try:
                    obj = json.loads(element)
                    if isinstance(obj, list):
                        json_objects.extend(obj)
                    else:
                        json_objects.append(obj)
                except Exception as e:
                    self._logger.error(f"Erreur lors de la fusion JSON : {e}")
            return json.dumps(json_objects, indent=4, ensure_ascii=False)
        else:
            return "Format non supporté"

    def get_testcase_by_id(self, testcase_id: uuid.UUID) -> TestCaseOutput | None:
        """
        Récupère un testcase par son ID

        Args:
            testcase_id: L'ID du testcase à récupérer

        Returns:
            Le testcase s'il existe, None sinon

        Raises:
            TestCaseNotFoundException: Si le testcase n'est pas trouvé
        """
        self._logger.debug(f"Getting testcase with ID: {testcase_id}")
        testcase = self._repo_testcases.get(entity_id=testcase_id)

        if testcase is None:
            raise TestCaseNotFoundException(identifier=testcase_id)

        return testcase
