import React from 'react'
import {
  Accordion,
  AccordionBody,
  AccordionHeader,
  AccordionItem,
  Divider,
  Section,
  Spacer,
  Text,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'

export const AccordionScreen = (): JSX.Element => {
  const [testActive, setTestActive] = React.useState(false)
  return (
    <Section>
      <Title level={TitleLevels.TWO}>simple </Title>
      <Spacer size={10} />
      <Accordion>
        <AccordionItem
          paddingless
          id='UN'
          forceControl
          active={testActive}
          onClick={() => {
            setTestActive(!testActive)
          }}
        >
          <AccordionHeader toggle>
            <Text>Test Active</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>

        <AccordionItem active={true} id='DEUX'>
          <AccordionHeader toggle>
            <Text>Hello World 2</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>

        <AccordionItem disabled id='TROIS'>
          <AccordionHeader toggle>
            <Text>Hello World 3</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
      </Accordion>

      <Accordion inverted>
        <AccordionItem>
          <AccordionHeader toggle>
            <Text>Hello World 4</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>

        <AccordionItem active={true}>
          <AccordionHeader toggle>
            <Text>Hello World 5</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
        <AccordionItem disabled>
          <AccordionHeader toggle>
            <Text>Hello World 6</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
        <AccordionItem>
          <AccordionHeader toggle>
            <Text>Hello World 7</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Active = False Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
      </Accordion>
      <Divider />

      <Title level={TitleLevels.THREE}>Accordion avec content paddingless et fond blanc</Title>
      <Accordion boxed>
        <AccordionItem active={true}>
          <AccordionHeader toggle>
            <Text>Hello World 1</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
        <AccordionItem>
          <AccordionHeader toggle>
            <Text>Hello World 2</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
        <AccordionItem>
          <AccordionHeader toggle>
            <Text>Hello World 3</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
      </Accordion>
      <Accordion boxed inverted>
        <AccordionItem active={true}>
          <AccordionHeader toggle>
            <Text>Hello World 4</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
        <AccordionItem>
          <AccordionHeader toggle>
            <Text>Hello World 5</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
          </AccordionBody>
        </AccordionItem>
        <AccordionItem>
          <AccordionHeader toggle>
            <Text>Hello World 6 with subaccordion</Text>
          </AccordionHeader>
          <AccordionBody>
            <Text>Lorem ipsum dolor sit amet</Text>
            <Accordion>
              <AccordionItem paddingless active={true}>
                <AccordionHeader>
                  <Text>Hello World 1</Text>
                </AccordionHeader>
                <AccordionBody>
                  <Text>Lorem ipsum dolor sit amet</Text>
                </AccordionBody>
              </AccordionItem>
              <AccordionItem>
                <AccordionHeader toggle>
                  <Text>Hello World 2</Text>
                </AccordionHeader>
                <AccordionBody>
                  <Text>Lorem ipsum dolor sit amet</Text>
                </AccordionBody>
              </AccordionItem>
              <AccordionItem disabled>
                <AccordionHeader toggle>
                  <Text>Hello World 3</Text>
                </AccordionHeader>
                <AccordionBody>
                  <Text>Lorem ipsum dolor sit amet</Text>
                </AccordionBody>
              </AccordionItem>
            </Accordion>
          </AccordionBody>
        </AccordionItem>
      </Accordion>
    </Section>
  )
}
