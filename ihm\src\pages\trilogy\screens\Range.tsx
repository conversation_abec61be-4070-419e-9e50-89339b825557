import React from 'react'
import { Section, Range } from '@bytel/trilogy-react-ts'

export const RangeScreen = (): JSX.Element => {
  return (
    <Section>
      <Range
        min={0}
        max={100}
        labelValueCursorMin={'€'}
        labelValueCursorMax={'€'}
        valueCursorMin={0}
        valueCursorMax={10}
        label='Ceci est un label'
        idMin='min'
        idMax='max'
        nameMax='max'
        nameMin='min'
        onChangeMin={(e) => console.log(e)}
        onChangeMax={(e) => console.log(e)}
        gap={0}
      />
    </Section>
  )
}
