import React from 'react'
import {
  Section,
  TextLevels,
  Columns,
  ColumnsItem,
  ButtonList,
  ButtonColor,
  Box,
  BoxContent,
  Text,
  Button,
} from '@bytel/trilogy-react-ts'

export const TextScreen = (): JSX.Element => {
  return (
    <Section>
      <Text numberOfLines={3}>
        Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolore obcaecati quia voluptatum! Assumenda blanditiis
        dignissimos eligendi excepturi facere facilis, fuga incidunt necessitatibus non obcaecati praesentium quam quia
        soluta veniam vero. Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusamus aliquam amet asperiores
        autem dicta esse et id, incidunt ipsa magni molestiae nesciunt non pariatur quasi, quia reiciendis repudiandae
        tempora voluptatibus.
      </Text>
      <Text level={TextLevels.ONE}>Mon super text 1</Text>
      <Text level={TextLevels.TWO}>Mon super text 2</Text>
      <Text level={TextLevels.THREE}>Mon super text 3</Text>
      <Text level={TextLevels.FOUR}>Mon super text 4</Text>
      <Text level={TextLevels.TWO}>
        Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aspernatur at deleniti doloremque ea, earum explicabo
        illo inventore minima optio pariatur ratione similique suscipit vero. Accusamus dicta impedit laudantium neque
        nulla!
        <Text link onClick={(e) => console.log(e)}>
          toto et un super lien webbbb
        </Text>
        Lorem ipsum dolor sit amet, consectetur adipisicing elit. Autem culpa, dignissimos distinctio, eum in ipsum
        magni nihil officia pariatur perferendis, recusandae rem reprehenderit. Aliquid eius expedita quasi quia
        reiciendis, sint.
      </Text>
      <Columns>
        <ColumnsItem centered>
          <Text>Mon texte</Text>
          <ButtonList>
            <Button variant={ButtonColor.PRIMARY}>
              <Text>Button centré</Text>
            </Button>
          </ButtonList>
          <Box>
            <BoxContent>
              <Text>test box centré par le columnItem</Text>
            </BoxContent>
          </Box>
        </ColumnsItem>
      </Columns>
    </Section>
  )
}
