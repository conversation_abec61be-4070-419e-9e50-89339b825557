import * as React from 'react'
import { Section, Select, SelectOption, ModalT<PERSON>le, Button, Modal, Spacer } from '@bytel/trilogy-react-ts'

export const SelectView = (): JSX.Element => {
  const [selectedOption, setSelectedOption] = React.useState<string | undefined>(undefined)
  const [selectedOption2, setSelectedOption2] = React.useState<string | undefined>('opt_3')
  const [selectedOption3, setSelectedOption3] = React.useState<string | undefined>('opt_2')

  return (
    <Section>
      <Select
        name='option'
        label='label'
        id='id'
        onFocus={(e) => console.log('OUVERT', e)}
        onBlur={(e) => console.log('FERMÉ', e)}
        onChange={(e) => {
          console.log(e)
          const value: string | undefined =
            typeof e === 'string' || typeof e === 'number'
              ? String(e)
              : e.selectValue
              ? String(e.selectValue)
              : undefined
          setSelectedOption(value)
        }}
        iconName='tri-advisor'
        selected={selectedOption}
      >
        <SelectOption id='id_one' value='opt_one' label='Virgile'></SelectOption>
        <SelectOption id='id_two' value='opt_two' label='Toto'></SelectOption>
        <SelectOption id='id_three' value='Venus' label='Venus'></SelectOption>
        <SelectOption id='id_three' value='Mars' label='Mars'></SelectOption>
        <SelectOption id='id_three' value='VEVE' label='VEVE'></SelectOption>
        <SelectOption id='id_three' value='volvo' label='volvo'></SelectOption>
      </Select>

      <Select
        name='name'
        id='id'
        disabled
        onFocus={(e) => console.log('OUVERT', e)}
        onBlur={(e) => console.log('FERMÉ', e)}
        onChange={(e) => console.log(e)}
        iconName='tri-advisor'
      >
        <SelectOption selected iconName='tri-advisor' id='id_one' value='opt_one' label='option1'>
          option 1
        </SelectOption>
        <SelectOption iconName='tri-advisor' id='id_two' value='opt_two' label='option2'>
          option 2
        </SelectOption>
        <SelectOption iconName='tri-advisor' disabled id='id_three' value='opt_three' label='option3'>
          option 3
        </SelectOption>
      </Select>

      <Select
        native
        onFocus={(e) => console.log('OUVERT', e)}
        onBlur={(e) => console.log('FERMÉ', e)}
        onChange={(e) => {
          console.log(e)
          const value: string | undefined =
            typeof e === 'string' || typeof e === 'number'
              ? String(e)
              : e.selectValue
              ? String(e.selectValue)
              : undefined
          setSelectedOption(value)
        }}
        selected={selectedOption}
        name='ceci est le name du select'
        nullable
        id='id'
        label='Choisir une option'
        iconName='tri-advisor'
      >
        <SelectOption iconName='tri-advisor' id='id_one' value='opt_one' label='option1'>
          option 1
        </SelectOption>
        <SelectOption iconName='tri-advisor' id='id_two' value='opt_two' label='option2'>
          option 2
        </SelectOption>
        <SelectOption iconName='tri-advisor' disabled id='id_three' value='opt_three' label='option3'>
          option 3
        </SelectOption>
        <SelectOption iconName='tri-advisor' id='id_four' value='opt_four' label='option4'>
          option 4
        </SelectOption>
        <SelectOption iconName='tri-advisor' id='id_five' value='opt_five' label='option5'>
          option 5
        </SelectOption>
        <SelectOption iconName='tri-advisor' id='id_six' value='opt_six' label='option6'>
          option 6
        </SelectOption>
      </Select>
      <Button
        onClick={() => {
          setSelectedOption('opt_one')
        }}
        variant={'PRIMARY'}
      >
        Selectionner option 1
      </Button>

      <Spacer size={100} />
      <Spacer size={100} />

      <Select
        onFocus={(e) => {
          console.log(e)
        }}
        onBlur={(e) => console.log('FERMÉ', e)}
        selected={selectedOption2}
        label=' Choisir une option'
        onChange={(e) => {
          const value: string | undefined =
            typeof e === 'string' || typeof e === 'number'
              ? String(e)
              : e.selectValue
              ? String(e.selectValue)
              : undefined
          setSelectedOption2(value)
        }}
        iconName='tri-advisor'
      >
        {[...Array(5)].map((item, i) => {
          const value = `opt_${i}`
          return (
            <SelectOption key={i} iconName='tri-advisor' id={value} value={value} label={`opt_${i}`}>
              {`option ${i}`}
            </SelectOption>
          )
        })}
        <SelectOption iconName='tri-advisor' id='id_6' value='opt_6' label='option6'>
          option 6
        </SelectOption>
      </Select>
      <Button
        onClick={() => {
          setSelectedOption2('opt_2')
        }}
        variant={'PRIMARY'}
      >
        Selectionner option 2
      </Button>
      <Spacer size={100} />
      <Spacer size={100} />
      <Select
        label='Choisir une option'
        iconName='tri-advisor'
        nullable
        onFocus={(e) => {
          console.log(e)
        }}
        onBlur={(e) => console.log('FERMÉ', e)}
        onChange={(e) => {
          console.log(e)
          const value: string | undefined =
            typeof e === 'string' || typeof e === 'number'
              ? String(e)
              : e.selectValue
              ? String(e.selectValue)
              : undefined
          setSelectedOption3(value)
        }}
      >
        {[...Array(5)].map((item, i) => {
          const value = `opt_${i}`
          return (
            <SelectOption
              key={i}
              selected={selectedOption3 === value}
              iconName='tri-advisor'
              id={value}
              value={value}
              label={`opt_${i}`}
            >
              {`option ${i}`}
            </SelectOption>
          )
        })}
        <SelectOption iconName='tri-advisor' id='id_6' value='opt_6' label='option6'>
          option 6
        </SelectOption>
      </Select>
      <Button
        onClick={() => {
          setSelectedOption3('opt_1')
        }}
        variant={'PRIMARY'}
      >
        Selectionner option 1
      </Button>
      <Button
        onClick={() => {
          setSelectedOption3(undefined)
        }}
        variant={'PRIMARY'}
      >
        Deselectionner
      </Button>

      <Spacer size={100} />

      <Modal triggerClassNames='button is-primary' triggerContent='Open modal' closeIcon>
        <ModalTitle>Custom title</ModalTitle>

        <Select label='Choisir une option'>
          <SelectOption selected id='id_one' value='opt_one' label='option1'>
            option 1
          </SelectOption>
          <SelectOption id='id_two' value='opt_two' label='option2'>
            option 2
          </SelectOption>
          <SelectOption id='id_three' value='opt_three' label='option3'>
            option 3
          </SelectOption>
        </Select>
      </Modal>
    </Section>
  )
}
