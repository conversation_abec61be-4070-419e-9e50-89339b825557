import logging
from datetime import datetime
from uuid import UUID

from app.common.exceptions import NotFoundException

from .models import ExigenceCreateInput, ExigenceFiltre, ExigenceOutput, ExigenceStorage
from .repository import ExigenceRepositoryInterface


class ExigenceNotFoundException(NotFoundException):
    """Exception levée lorsqu'une exigence n'est pas trouvée."""

    message = "L'exigence n'existe pas"


class WorkspaceExigenceNotFoundException(NotFoundException):
    """Exception levée lorsqu'une exigence d'espace de travail n'est pas trouvée."""

    message = "L'exigence d'espace de travail n'existe pas"


class ExigenceService:
    """Service de gestion des exigences.

    Ce service gère le cycle de vie des exigences incluant :
    - La création d'exigences
    - La récupération d'exigences (individuelle ou en liste)
    - La recherche d'exigences par utilisateur ou espace de travail
    """

    def __init__(self, repository: ExigenceRepositoryInterface):
        """Initialise le service avec un repository d'exigences.

        Args:
            repository (ExigenceRepositoryInterface): Le repository pour la persistance des exigences
        """
        self._repo = repository
        self._logger = logging.getLogger(__name__)

    def create_exigence(self, dto: ExigenceCreateInput) -> ExigenceOutput:
        """Crée une nouvelle exigence.

        Args:
            dto (ExigenceCreateInput): Les données de l'exigence à créer

        Returns:
            ExigenceOutput: L'exigence créée avec ses données complètes
        """
        self._logger.info(f"Création d'une nouvelle exigence: {dto}")
        try:
            dao = ExigenceStorage(**dto.model_dump(), date_creation=datetime.now())
            created = self._repo.add(dao)
            self._logger.info(f"Exigence créée avec succès: {created.id}")
            return ExigenceOutput(**created.model_dump())
        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'exigence: {str(e)}")
            raise

    def get_exigence_by_id(self, id_exigence: UUID) -> ExigenceOutput:
        """Récupère une exigence par son identifiant.

        Args:
            id_exigence (UUID): L'identifiant unique de l'exigence

        Raises:
            ExigenceNotFoundException: Si l'exigence n'est pas trouvée

        Returns:
            ExigenceOutput: L'exigence trouvée
        """
        self._logger.debug(f"Recherche de l'exigence avec l'ID: {id_exigence}")
        exigence = self._repo.get(id_exigence)
        if not exigence:
            self._logger.warning(f"Exigence non trouvée avec l'ID: {id_exigence}")
            raise ExigenceNotFoundException(identifier=str(id_exigence))
        self._logger.debug(f"Exigence trouvée : {exigence}")
        return ExigenceOutput(**exigence.model_dump())

    def get_exigences(self, limit: int = None) -> list[ExigenceOutput]:
        """Liste toutes les exigences avec une limite optionnelle.

        Args:
            limit (int, optional): Nombre maximum d'exigences à retourner. Defaults to None.

        Returns:
            list[ExigenceOutput]: Liste des exigences
        """
        self._logger.debug(f"Récupération de la liste des exigences avec limite={limit}")
        exigences = self._repo.list(limit)
        self._logger.info(f"Nombre d'exigences récupérées: {len(exigences)}")
        return [ExigenceOutput(**exigence.model_dump()) for exigence in exigences]

    def get_exigence_by_user(self, owner_id: UUID) -> list[ExigenceOutput]:
        """Récupère toutes les exigences d'un utilisateur.

        Args:
            owner_id (UUID): L'identifiant de l'utilisateur propriétaire

        Returns:
            list[ExigenceOutput]: Liste des exigences de l'utilisateur
        """
        self._logger.debug(f"Recherche des exigences pour l'utilisateur: {owner_id}")
        found_exigences = self._repo.find(ExigenceFiltre(owner_id=owner_id))
        if not found_exigences:
            self._logger.warning(f"Aucune exigence trouvée pour l'utilisateur: {owner_id}")
            raise ExigenceNotFoundException(identifier=str(owner_id))
        self._logger.info(f"Exigences trouvées pour l'utilisateur {owner_id}: {len(found_exigences)}")
        return [ExigenceOutput(**exigence.model_dump()) for exigence in found_exigences]

    def get_exigence_by_workspace(self, ws_id: UUID) -> list[ExigenceOutput]:
        """Récupère toutes les exigences d'un espace de travail.

        Args:
            ws_id (UUID): L'identifiant de l'espace de travail

        Returns:
            list[ExigenceOutput]: Liste des exigences de l'espace de travail
        """
        self._logger.debug(f"Recherche des exigences pour l'espace de travail: {ws_id}")
        try:
            found_exigences = self._repo.find(filtre=ExigenceFiltre(ws_id=ws_id))
            self._logger.info(f"Nombre d'exigences trouvées: {len(found_exigences)}")
            return [ExigenceOutput(**exigence.model_dump()) for exigence in found_exigences]
        except Exception as e:
            self._logger.error(f"Erreur lors de la recherche des exigences: {str(e)}")
            raise

    def get_user_workspace_exigences(self, owner_id: UUID, ws_id: UUID) -> list[ExigenceOutput]:
        """Récupère les exigences d'un utilisateur dans un espace de travail spécifique.

        Args:
            owner_id (UUID): L'identifiant de l'utilisateur
            ws_id (UUID): L'identifiant de l'espace de travail

        Returns:
            list[ExigenceOutput]: Liste des exigences correspondant aux critères
        """
        found_exigences = self._repo.find(ExigenceFiltre(ws_id=ws_id, owner_id=owner_id))
        if not found_exigences:
            raise WorkspaceExigenceNotFoundException(identifier=str(owner_id))
        self._logger.info(f"Exigences trouvées pour l'utilisateur {owner_id} dans l'espace de travail {ws_id}: {len(found_exigences)}")
        return [ExigenceOutput(**exigence.model_dump()) for exigence in found_exigences]
