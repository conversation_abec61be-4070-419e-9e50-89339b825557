import {
  Accordion,
  AccordionBody,
  AccordionHeader,
  AccordionI<PERSON>,
  Box,
  BoxContent,
  BoxHeader,
  Button,
  Card,
  CardContent,
  CardImage,
  Divider,
  ScrollView,
  Section,
  Spacer,
  Text,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'
import React from 'react'
import { VariantState } from '@bytel/trilogy-react-ts'

const Separator = () => {
  return (
    <>
      <Spacer size={10} />
      <Divider />
      <Spacer size={50} />
    </>
  )
}

export const CardScreen = (): JSX.Element => {
  const [skeleton, setSkeleton] = React.useState<boolean>(false)

  return (
    <Section>
      <Title level={TitleLevels.TWO}>Active Card</Title>
      <Spacer size={10} />
      <Box background={'GREY_LIGHT'}>
        <BoxHeader>Vertical</BoxHeader>
        <BoxContent background={'GREY_LIGHT'}>
          <Card active>
            <CardImage src='https://i.etsystatic.com/10951167/r/il/df66c4/1860902191/il_570xN.1860902191_kuoj.jpg' />
            <CardContent>
              <Title suptitle>Desktop Card Vertical Markup A</Title>
              <Title level={TitleLevels.ONE}>Card Title</Title>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ligula ex, aliquam at neque eu, vulputate
                vera.
              </Text>
              <Button variant={VariantState.PRIMARY} onClick={() => setSkeleton(skeleton)}>
                Skeleton toogle click
              </Button>
            </CardContent>
          </Card>
        </BoxContent>
      </Box>
      <Title level={TitleLevels.TWO}>cardImage & markup </Title>
      <Spacer size={10} />
      <Box background={'GREY_LIGHT'}>
        <BoxHeader>Vertical</BoxHeader>
        <BoxContent background={'GREY_LIGHT'}>
          <Card markup={"A"}>
            <CardImage src='https://i.etsystatic.com/10951167/r/il/df66c4/1860902191/il_570xN.1860902191_kuoj.jpg' />
            <CardContent>
              <Title suptitle>Desktop Card Vertical Markup A</Title>
              <Title level={TitleLevels.ONE}>Card Title</Title>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ligula ex, aliquam at neque eu, vulputate
                vera.
              </Text>
              <Button variant={VariantState.PRIMARY} onClick={() => setSkeleton(skeleton)}>
                Skeleton toogle click
              </Button>
            </CardContent>
          </Card>
        </BoxContent>
      </Box>
      <Separator />

      <Title level={TitleLevels.TWO}>reversed </Title>
      <Spacer size={10} />
      <Box background={'GREY_LIGHT'}>
        <BoxHeader>Vertical reversed</BoxHeader>
        <BoxContent background={'GREY_LIGHT'}>
          <Card reversed>
            <CardImage src='https://i.etsystatic.com/10951167/r/il/df66c4/1860902191/il_570xN.1860902191_kuoj.jpg' />
            <CardContent>
              <Title suptitle>Desktop Card Vertical Reversed</Title>
              <Title level={TitleLevels.ONE}>Card Title</Title>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ligula ex, aliquam at neque eu, vulputate
                vera.
              </Text>
              <Button
                variant={VariantState.PRIMARY}
                onClick={() => window.open('https://fr.wikipedia.org/wiki/Shiba_(chien)', '_blank')}
              >
                Enabled
              </Button>
            </CardContent>
          </Card>
        </BoxContent>
      </Box>
      <Separator />

      <Title level={TitleLevels.TWO}>Flat </Title>
      <Spacer size={10} />
      <Box background={'GREY_LIGHT'}>
        <BoxHeader>Flat</BoxHeader>
        <BoxContent background={'GREY_LIGHT'}>
          <Card flat>
            <CardImage src='https://i.etsystatic.com/10951167/r/il/df66c4/1860902191/il_570xN.1860902191_kuoj.jpg' />
            <CardContent>
              <Title suptitle>Flat Card</Title>
              <Title level={TitleLevels.ONE}>Card Title</Title>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ligula ex, aliquam at neque eu, vulputate
                vera.
              </Text>
              <Button
                variant={VariantState.PRIMARY}
                onClick={() => window.open('https://fr.wikipedia.org/wiki/Shiba_(chien)', '_blank')}
              >
                Enabled
              </Button>
            </CardContent>
          </Card>
        </BoxContent>
      </Box>
      <Separator />

      <Title level={TitleLevels.TWO}>Horizontal </Title>
      <Spacer size={10} />
      <ScrollView scrollDirection='HORIZONTAL'>
        <Box background={'GREY_LIGHT'}>
          <BoxHeader>Horizontal</BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Card horizontal>
              <CardImage
                size={'3'}
                src='https://i.etsystatic.com/10951167/r/il/df66c4/1860902191/il_570xN.1860902191_kuoj.jpg'
              />
              <CardContent>
                <Title suptitle>Desktop Card Horizontal</Title>
                <Title level={TitleLevels.ONE}>Card Title</Title>
                <Text>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ligula ex, aliquam at neque eu, vulputate
                  vera.
                </Text>
                <Button
                  variant={VariantState.PRIMARY}
                  onClick={() => window.open('https://fr.wikipedia.org/wiki/Shiba_(chien)', '_blank')}
                >
                  Enabled
                </Button>
              </CardContent>
            </Card>
          </BoxContent>
        </Box>
      </ScrollView>
      <Separator />

      <Title level={TitleLevels.TWO}>Floating (inside component)</Title>
      <Spacer size={10} />
      <Box>
        <BoxHeader>Floating (inside component)</BoxHeader>
        <BoxContent>
          <Accordion className='is-marginless'>
            <AccordionItem active>
              <AccordionHeader toggle>
                <Text level={'ONE'}>Card floating (inside component)</Text>
              </AccordionHeader>
              <AccordionBody>
                <Card floating>
                  <CardImage src='https://i.etsystatic.com/10951167/r/il/df66c4/1860902191/il_570xN.1860902191_kuoj.jpg' />
                  <CardContent>
                    <Text>
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ligula ex, aliquam at neque eu,
                      vulputate vera.
                    </Text>
                    <Button
                      variant={VariantState.PRIMARY}
                      onClick={() => window.open('https://fr.wikipedia.org/wiki/Shiba_(chien)', '_blank')}
                    >
                      Enabled
                    </Button>
                  </CardContent>
                </Card>
              </AccordionBody>
            </AccordionItem>
          </Accordion>
        </BoxContent>
      </Box>
      <Separator />
    </Section>
  )
}
