import React, { useEffect, useState } from "react";
import { Tree } from "primereact/tree";
import { TreeNode } from "primereact/treenode";
import { ScrollPanel } from "primereact/scrollpanel";
import { Checkbox } from "primereact/checkbox";
import { Button, Icon, IconName, AlertState, Text } from "@bytel/trilogy-react-ts";
import { DocumentOutput } from "types/document";
import { FaFile, FaFolder } from "react-icons/fa";
import { classNames } from 'primereact/utils';
import "primereact/resources/primereact.min.css";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primeicons/primeicons.css";

interface DocumentTreeSelectorProps {
  documents: DocumentOutput[];
  selectedDocumentIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  isLoading?: boolean;
}

// Configuration des seuils et couleurs (valeurs par défaut)
const DOCUMENT_SIZE_LIMIT_1 = 1048576; // 1 MB
const DOCUMENT_SIZE_LIMIT_2 = 2097152; // 2 MB
const DOCUMENT_SIZE_COLOR_GREEN = '#4CAF50';
const DOCUMENT_SIZE_COLOR_ORANGE = '#FF9800';
const DOCUMENT_SIZE_COLOR_RED = '#F44336';

// Fonction pour formater la taille en Mo
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Mo';
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(2)} Mo`;
};

// Fonction pour obtenir la couleur selon la taille
const getSizeColor = (totalSize: number): string => {
  if (totalSize <= DOCUMENT_SIZE_LIMIT_1) return DOCUMENT_SIZE_COLOR_GREEN;
  if (totalSize <= DOCUMENT_SIZE_LIMIT_2) return DOCUMENT_SIZE_COLOR_ORANGE;
  return DOCUMENT_SIZE_COLOR_RED;
};

// Fonction pour organiser les documents dans un seul dossier
const organizeDocumentsInSingleFolder = (documents: DocumentOutput[]): TreeNode[] => {
  return [{
    key: 'mes-documents',
    label: `Mes documents (${documents.length})`,
    selectable: false, // Le dossier n'est pas sélectionnable
    children: documents.map(doc => ({
      key: doc.id,
      label: doc.filename,
      data: doc,
      selectable: false // Géré manuellement avec le checkbox
    }))
  }];
};

const DocumentTreeSelector: React.FC<DocumentTreeSelectorProps> = ({ 
  documents, 
  selectedDocumentIds,
  onSelectionChange,
  isLoading = false
}) => {
  const [treeNodes, setTreeNodes] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<{ [key: string]: boolean }>({});

  // Calculer la taille totale des documents sélectionnés
  const selectedDocuments = documents.filter(doc => selectedDocumentIds.includes(doc.id));
  const totalSize = selectedDocuments.reduce((sum, doc) => {
    return sum + (doc.size || 0);
  }, 0);

  // Debug: afficher la taille totale calculée
  console.log('Selected documents:', selectedDocuments);
  console.log('Total size:', totalSize);

  useEffect(() => {
    const nodes = organizeDocumentsInSingleFolder(documents);
    setTreeNodes(nodes);
    
    // Expand the folder by default
    const expanded: { [key: string]: boolean } = {};
    if (nodes.length > 0 && nodes[0].key) {
      expanded[nodes[0].key] = true;
    }
    setExpandedKeys(expanded);
  }, [documents]);

  const handleDocumentSelection = (documentId: string) => {
    // Sélection multiple : ajouter/retirer de la sélection actuelle
    if (selectedDocumentIds.includes(documentId)) {
      onSelectionChange(selectedDocumentIds.filter(id => id !== documentId));
    } else {
      onSelectionChange([...selectedDocumentIds, documentId]);
    }
  };

  const nodeTemplate = (node: TreeNode) => {
    const isDocument = node.data && typeof node.data === 'object';
    const document = node.data as DocumentOutput;

    if (isDocument) {
      const isSelected = selectedDocumentIds.includes(document.id);
      
      return (
        <div 
          style={{ 
            display: "flex", 
            justifyContent: "space-between", 
            cursor: "pointer",
            padding: "4px 0",
            backgroundColor: isSelected ? "#e3f2fd" : "transparent",
            borderRadius: "4px"
          }}
          onClick={() => handleDocumentSelection(document.id)}
        >
          <div style={{ display: "flex", alignItems: "center", flex: 1 }}>
            <Checkbox
              inputId={document.id}
              checked={isSelected}
              onChange={() => handleDocumentSelection(document.id)}
              style={{ marginRight: "8px" }}
            />
            <FaFile style={{ marginRight: "8px", color: "#666", fontSize: "14px" }} />
            <div style={{ flex: 1 }}>
              <Text style={{ fontSize: "1.1rem", fontWeight: isSelected ? "600" : "400" }}>
                {document.filename} Uploadé: {new Date(document.date_upload).toLocaleDateString()} {document.date_maj && ` • Modifié: ${new Date(document.date_maj).toLocaleDateString()}`}
              </Text>              
            </div>
          </div>
        </div>
      );
    }

    return (
      <div style={{ display: "flex", alignItems: "center" }}>
        <FaFolder style={{ marginRight: "8px", color: "#4a90e2", fontSize: "16px" }} />
        <Text style={{ fontSize: "1.2rem", fontWeight: "600" }}>
          {node.label}
        </Text>
      </div>
    );
  };

  if (isLoading) {
    return <Text>Chargement des documents...</Text>;
  }

  if (documents.length === 0) {
    return <Text className="mt-3">Aucun document disponible.</Text>;
  }

  return (
    <>
      <div style={{ marginBottom: "15px", display: "flex", gap: "10px", alignItems: "center" }}>
        
        <div style={{ marginLeft: "auto", display: "flex", flexDirection: "column", alignItems: "flex-end" }}>
          <Text style={{ fontSize: "0.9rem", color: "#666" }}>
            {selectedDocumentIds.length > 0 ? `${selectedDocumentIds.length} document${selectedDocumentIds.length > 1 ? 's' : ''} sélectionné${selectedDocumentIds.length > 1 ? 's' : ''}` : "Aucun document sélectionné"}
          </Text>
          {selectedDocumentIds.length > 0 && (
            <Text style={{ 
              fontSize: "0.9rem", 
              color: getSizeColor(totalSize),
              fontWeight: "600"
            }}>
              Taille totale: {formatFileSize(totalSize)}
              {/* Debug: afficher la taille en bytes aussi */}
              {totalSize === 0 && <span style={{ color: 'red', fontSize: '0.8rem' }}> (Taille non disponible)</span>}
            </Text>
          )}
        </div>
      </div>

      <ScrollPanel 
        className="custom-scrollpanel">
        <Tree
          value={treeNodes}
          expandedKeys={expandedKeys}
          onToggle={(e) => setExpandedKeys(e.value)}
          nodeTemplate={nodeTemplate}
          className="w-full custom-tree"
          filter
          filterMode="lenient"
          filterPlaceholder="Rechercher un document..."
          selectionMode={null}
        />
      </ScrollPanel>
    </>
  );
};

export default DocumentTreeSelector;