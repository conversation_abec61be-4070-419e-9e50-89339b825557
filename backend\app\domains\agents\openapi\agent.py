import json
from typing import Literal

import openapi_parser
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from pydantic import BaseModel, model_validator
from typing_extensions import Self

from ..implementations import INSTRUCTIONS_ECRITURE, EcritureTest
from ..models import AgentIA, CasDeTestIA, FormatExigence, TestImplementationIA, WorkflowIdentificationState
from .implementations import INSTRUCTIONS_FORMATS_BOUCHONS
from .models import BouchonsServiceIA, CasUsageAPI, ImplementationBouchons, ReponseApi
from .prompts import (
    COMPLETER_BOUCHONS_PROMPT,
    GENERER_FORMAT_BOUCHON_PROMPT,
    IDENTIFIER_BOUCHONS_PROMPT,
    PROMPT_ECRITURE_TEST,
    PROMPT_IDENTIFICATION_TEST,
)


class WorkflowOpenAPI(WorkflowIdentificationState):
    essais_restants: int = 2
    bouchons_manquants: list | None = None

    @model_validator(mode="after")
    def est_exigence_open_api(self) -> Self:
        if self.exigence.type != FormatExigence.OPEN_API:
            raise ValueError(f"réception d'un type d'exigence qui n'est pas une spécification openapi mais de type {self.exigence.type}")
        return self


class IdentificationCasUsagesAPI(BaseModel):
    usages: list[CasUsageAPI]


class IdentificationTestcasesAPI(BaseModel):
    tests: list[EcritureTest]


class AgentOpenAPI(AgentIA):
    """
    l'agent dédié à l'identification de test et/ou bouchons à partir d'une exigence de type OpenAPI
    """

    def __init__(self, llm, llm_json):
        super().__init__(llm, llm_json, state_schema=WorkflowOpenAPI)

    # region: generation des cas de test

    def identifier_tests(self, state: WorkflowOpenAPI):
        """
        réalise l'identification des tests à partir de la spec OAS
        """
        self.log.info("identification des tests à partir de la spécification Open API...")
        chain = PROMPT_IDENTIFICATION_TEST | self.llm_json.with_structured_output(IdentificationTestcasesAPI, strict=True, method="function_calling")
        if state.documents is None or len(state.documents) == 0:
            self.log.warning(
                "Aucun document supplémentaire n'a été fourni ! Les cas de test ne seront généré qu'en se basant sur la specification Open API..."
            )
        resultat: IdentificationTestcasesAPI = chain.invoke({"open_api_specification": state.exigence.data, "documentation": state.documents})
        self.log.info("%i cas de test identifiés", len(resultat.tests))

        identification_tests = [CasDeTestIA(**testcase.model_dump(), reference_exigence=state.exigence.identifiant) for testcase in resultat.tests]

        return {"tests": identification_tests}

    def implementer_tests(self, state: WorkflowOpenAPI):
        """
        réalise l'écriture dans le format demandé des tests du service décrit par la spécification OpenAPI
        En effet, pour cette typologie d'exigence, on a les informations nécessaires pour écrire les étapes du tests.
        """
        self.log.info("Implémentation des formats de test fournis...")
        implementation_tests = state.tests

        chain = PROMPT_ECRITURE_TEST | self.llm | StrOutputParser()

        for test in implementation_tests:
            if test.implementations is None:
                test.implementations = []

            for format_demande in state.options.formats_tests:
                self.log.info("écriture du test %s au format %s", test.titre, format_demande.value)
                instructions = INSTRUCTIONS_ECRITURE[format_demande]
                output = chain.invoke({"open_api_specification": state.exigence.data, "test": test.model_dump_json(), "instructions": instructions})
                test.implementations.append(TestImplementationIA(format=format_demande, contenu=output))

        self.log.info("implémentation terminée.")

        return {"tests": implementation_tests}

    # endregion

    # region: generation des bouchons

    def identifier_bouchons(self, state: WorkflowOpenAPI):
        """
        fonction en charge de générer les exemples d'appels à partir de la spécification Open API et de la documentation associée
        """
        self.log.info("Identification initiale des bouchons")
        chain = IDENTIFIER_BOUCHONS_PROMPT | self.llm_json.with_structured_output(IdentificationCasUsagesAPI, strict=True, method="function_calling")
        resultat: IdentificationCasUsagesAPI = chain.invoke({"open_api_specification": state.exigence.data, "documentation": state.documents})
        self.log.info("%i bouchons identifiés", len(resultat.usages))

        self.log.debug("mise à jour de l'état du workflow avec la 1ère identification...")
        essais_restants = state.essais_restants - 1
        identification_initiale = BouchonsServiceIA(reference_exigence=state.exigence.identifiant, usages=resultat.usages, implementations=None)

        return {"bouchons": identification_initiale, "essais_restants": essais_restants}

    def verifier_bouchons(self, state: WorkflowOpenAPI):
        """
        fonction en charge d'évaluer si le LLM a identifié à minima autant de cas d'usage à bouchonner
        que ce qui aurait été obtenu via un générateur statique.

        L'état du graph est mis à jour avec la liste des cas d'usage manquants (liste vide si aucun manque constaté)
        """
        self.log.info("Contrôle de la cohérence des bouchons identifiés par le LLM par rapport à un générateur statique...")
        missing_usages = []

        expectations = self._get_expected_mocks(state.exigence.data)

        generated_usecases = state.bouchons.usages

        for service_path, expected_usecase in expectations.items():
            self.log.info("Vérification des cas d'usage générés pour le service %s", service_path)

            for expected_example in expected_usecase:
                # TODO: pas super performant comme méthode vu que l'on reparcours la liste complète à chaque fois
                matching_generated_usecases = [usecase for usecase in generated_usecases if self._is_matching(expected_example, usecase)]

                log_message = f"{len(matching_generated_usecases)} exemple(s) généré(s) pour "
                log_message += f"{expected_example.service_path} - {expected_example.http_method} - {expected_example.status_code}"

                if len(matching_generated_usecases) == 0:
                    # on a trouvé aucun cas d'usage correspondant dans l'ensemble des cas générés via IA
                    self.log.warning(log_message)
                    missing_usages.append(expected_example)
                else:
                    # on a trouvé au moins un cas d'usage parmi les cas générés via IA
                    self.log.info(log_message)

        return {"bouchons_manquants": missing_usages}

    def completer_bouchons(self, state: WorkflowOpenAPI):
        """
        Durant cette étape, on va essayer générer les exemples d'appels manquants par rapport à ce qui a été généré via IA.
        """
        self.log.info("Tentative de rattrapage des appels manquants...")
        essais_restants = state.essais_restants - 1
        usecases_manquants: list[ReponseApi] = state.bouchons_manquants
        usages_generes: list[CasUsageAPI] = state.bouchons.usages

        chain = COMPLETER_BOUCHONS_PROMPT | self.llm.with_structured_output(IdentificationCasUsagesAPI, strict=True, method="function_calling")
        resultat: IdentificationCasUsagesAPI = chain.invoke({"open_api_specification": state.exigence.data, "attendus": usecases_manquants})
        self.log.info("%i bouchons identifiés", len(resultat.usages))

        if len(resultat.usages) > 0:
            usages_generes.extend(resultat.usages)

        identification_completee = BouchonsServiceIA(
            date_generation=state.bouchons.date_generation,
            reference_exigence=state.bouchons.reference_exigence,
            implementations=None,
            usages=usages_generes,
        )

        return {"bouchons": identification_completee, "essais_restants": essais_restants}

    def implementer_bouchons(self, state: WorkflowOpenAPI):
        """
        Chaine de traitement responsable de l'écriture des formats de mocks à partir des cas d'usage du service précédemment identifiés.
        """
        self.log.info("Implémentation des cas d'usage identifiés dans les formats de bouchons demandés...")
        # TODO: possiblement moyen de faire du parallelisme pour être rapide
        # https://langchain-ai.github.io/langgraph/how-tos/map-reduce/#define-the-graph
        # pour l'instant on va garder en l'état (2 formats de sortie max)

        implementations = []

        # on serialise en pydantic pour faire un model_dump en json lors du prompt vu que l'état interne de langgraph est du TypedDict
        # pour plus de détails: https://langchain-ai.github.io/langgraph/how-tos/state-model/#serialization-behavior
        exemples = IdentificationCasUsagesAPI(usages=state.bouchons.usages)

        chain = GENERER_FORMAT_BOUCHON_PROMPT | self.llm | JsonOutputParser()
        for format_bouchon in state.options.formats_bouchons:
            self.log.info("Génération des bouchons au fomat %s", format_bouchon.value)
            self.log.debug("récupérations des instructions d'écriture liées au format demandée...")
            format_instructions = INSTRUCTIONS_FORMATS_BOUCHONS[format_bouchon]
            self.log.debug("instructions récupérées.")
            generated = chain.invoke({"exemples": exemples.model_dump_json(), "instructions": format_instructions})
            if type(generated) is not list:
                self.log.warning("Le format généré n'est pas garanti car il ne s'agit pas d'une liste")
                output = str(generated)
            else:
                self.log.info("%i bouchons générés", len(generated))
                output = json.dumps(generated)

            implementations.append(ImplementationBouchons(format=format_bouchon, contenu=output))

        self.log.debug("création du statut final des bouchons implémentés...")
        implementation_terminee = BouchonsServiceIA(
            date_generation=state.bouchons.date_generation,
            reference_exigence=state.bouchons.reference_exigence,
            usages=state.bouchons.usages,
            implementations=implementations,
        )

        return {"bouchons": implementation_terminee}

    def _is_matching(self, expected_example: ReponseApi, generated_example: CasUsageAPI) -> bool:
        """
        réalise la comparaison entre l'exemple généré par IA et l'attendu d'un générateur statique.

        La comparaison s'appuie sur les caractéristiques suivantes:
            - code retour HTTP (ex: 404)
            - methode HTTP (ex: POST)
            - service_path (ex: /communications/{idComm})

        """
        self.log.debug("Attendu: %s", expected_example)
        self.log.debug("Généré: %s", generated_example)

        match_found = (
            expected_example.status_code == generated_example.status_code
            and expected_example.http_method.upper() == generated_example.http_method.upper()
            and expected_example.service_path.lower() == generated_example.service_path.lower()
        )

        self.log.debug("correspondance: %s", match_found)

        return match_found

    def _get_expected_mocks(self, open_api_spec: str) -> dict[str, list[ReponseApi]]:
        """
        méthode qui permet d'obtenir la liste minimum de bouchons qu'aurait produit un générateur statique.
        Le retour est un dictionnaire où les clés correspond aux chemins des différents services.
        """
        self.log.debug("sérialisation de la specification Open API")
        services = openapi_parser.parse(spec_string=open_api_spec).paths
        expectations = {}

        # meh en terme de boucles imbriqués mais probable pas mieux
        self.log.info("Génération statique du minimum d'exemples d'appels attendus")
        for service in services:
            expected_examples = []

            for operation in service.operations:
                current_method = operation.method.value
                for response in operation.responses:
                    expected_examples.append(ReponseApi(service_path=service.url, http_method=current_method, status_code=response.code))

            expectations[service.url] = expected_examples

        return expectations

    # endregion

    # region: sequencement des noeuds

    # TODO: voir par la suite pour faire du parallelisme des tests et des bouchons
    # Dans un premier on va se contenter de faire du séquentiel pour ne pas sursolliciter les API des LLM

    def _apres_identification_tests(self, state: WorkflowOpenAPI) -> Literal["implémenter tests", "identifier bouchons", "__end__"]:
        """
        indique quelle est la prochaine etape du graph lorsque l'on a terminé l'identification des tests
        """
        if state.options is None:
            self.log.debug("aucune option d'identification fournie. renvoi des tests identifiés")
            return "__end__"

        if state.options.formats_tests and len(state.options.formats_tests) > 0:
            self.log.debug("routage vers l'implementation des tests")
            return "implémenter tests"

        if state.options.identifier_bouchons:
            self.log.debug("routage vers l'identification des bouchons")
            return "identifier bouchons"

    def _apres_implementation_tests(self, state: WorkflowOpenAPI) -> Literal["identifier bouchons", "__end__"]:
        """
        indique quelle est la prochaine etape du graph lorsque l'on a terminé l'implémentation des tests
        """

        if state.options and state.options.identifier_bouchons:
            self.log.debug("routage vers l'identification des bouchons")
            return "identifier bouchons"

        self.log.debug("renvoi des tests identifiés et implémentés")
        return "__end__"

    def _apres_verification_bouchons(self, state: WorkflowOpenAPI) -> Literal["compléter bouchons", "implémenter bouchons", "__end__"]:
        """
        indique quelle est la prochaine etape du graph lorsque l'on a terminé l'identification initiale des bouchons.

        Si l'identification est partielle, on va alors boucler sur la complétion des bouchons
        pour rattrapper les manques, jusqu'à la limite maximum autorisée d'essais.
        """

        if state.bouchons_manquants is not None and len(state.bouchons_manquants) > 0:
            self.log.warning("Il manque %i cas d'usage du service par rapport à un générateur statique", len(state.bouchons_manquants))

            if state.essais_restants > 0:
                self.log.debug("routage vers le rattrape des cas d'usage manquants")
                return "compléter bouchons"
            else:
                self.log.error(
                    "nombre d'essai maximum atteint. %i cas d'usages n'ont pas réussi à être identifiés par l'agent", len(state.bouchons_manquants)
                )

        if state.options and state.options.formats_bouchons and len(state.options.formats_bouchons) > 0:
            self.log.debug("routage vers l'implémentation des bouchons")
            return "implémenter bouchons"

        self.log.debug("renvoi des bouchons identifiés en plus des tests")
        return "__end__"

    # endregion

    def build(self):
        # definition des noeud

        # ce qui concerne les tests
        self.builder.add_node("identifier tests", self.identifier_tests)
        self.builder.add_node("implémenter tests", self.implementer_tests)
        # ce qui concerne les bouchons
        self.builder.add_node("identifier bouchons", self.identifier_bouchons)
        self.builder.add_node("compléter bouchons", self.completer_bouchons)
        self.builder.add_node("vérifier bouchons", self.verifier_bouchons)
        self.builder.add_node("implémenter bouchons", self.implementer_bouchons)

        # definition du séquencement du graph
        self.builder.set_entry_point("identifier tests")
        self.builder.add_conditional_edges("identifier tests", self._apres_identification_tests)
        self.builder.add_conditional_edges("implémenter tests", self._apres_implementation_tests)

        self.builder.add_edge("identifier bouchons", "vérifier bouchons")
        self.builder.add_edge("compléter bouchons", "vérifier bouchons")
        self.builder.add_conditional_edges("vérifier bouchons", self._apres_verification_bouchons)

        self.builder.set_finish_point("implémenter bouchons")

        return self.builder.compile()
