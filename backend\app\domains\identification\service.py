import logging
import uuid

from app.common.exceptions import AlreadyExistsException, InvalidContentException, NotFoundException
from app.domains.bouchons.service import BouchonsOpenApiService

# from app.domains.documents.models import DocumentFilter
# from app.domains.documents.repository import DocumentRepositoryInterface
from app.domains.documents.service import Document<PERSON>ilter, DocumentRepositoryInterface, DocumentService
from app.domains.exigences.models import ExigenceFiltre, ExigenceOutput
from app.domains.exigences.repository import ExigenceRepositoryInterface
from app.domains.identification.agent import AgentIdentifications
from app.domains.identification.models import (
    AgentIdentificationInput,
    AgentIdentificationOutput,
    DemandeIdentificationFiltre,
    DemandeIdentificationInput,
    DemandeIdentificationOutput,
    DemandeIdentificationOutputEnrichi,
    DemandeIdentificationStorage,
    DemandeIdentificationUpdate,
    IdentificationEchouee,
    IdentificationEnCours,
    IdentificationReussie,
    PhotoEntrants,
    StatutsDemande,
)
from app.domains.identification.repository import IdentificationsRepositoryInterface
from app.domains.testcases.service import TestCasesService
from app.domains.users.service import UsersRepositoryInterface
from app.domains.workspace.service import WorkspaceRepositoryInterface


class DemandeInvalideException(InvalidContentException):
    message = "la demande d'identification n'a pas pu être prise en compte car elle est invalide."


class IdentificationEnCoursException(AlreadyExistsException):
    message = "Une identification est déjà en cours pour cet espace de travail"


class IdentificationService:
    """
    Le service d'identification à partir d'exigence via IA.
    Pour des raisons évidentes de latences liés à la durée des traitements, le service se veut asynchrone.
    En effet, les identifications peuvent durer plus de 8 min.

    On a donc un système de gestion de la demande qui sera exposé par API et l'identification en tâche de fond.
    Pour l'instant l'implémentation utilise les BackGroundTasks de FastAPI mais selon la performance,
    il est possible que l'on passe sur un système plus complexe avec un broker
    """

    def __init__(
        self,
        identifications: IdentificationsRepositoryInterface,
        agent: AgentIdentifications,
        exigences: ExigenceRepositoryInterface,
        documents: DocumentRepositoryInterface,  # Ajout du repository documents
        testcases: TestCasesService,
        bouchons: BouchonsOpenApiService,
        utilisateurs: UsersRepositoryInterface,
        workspaces: WorkspaceRepositoryInterface,
        service_documents: DocumentService,
    ):
        self._repo_identifications = identifications
        self._repo_exigences = exigences
        self._repo_documents = documents  # Stockage du repository
        self._service_testcases = testcases
        self._service_bouchons = bouchons
        self._agent = agent
        self._repo_utilisateurs = utilisateurs
        self._repo_workspaces = workspaces
        self._service_documents = service_documents
        self.log = logging.getLogger(__name__)

    def creer_demande(
        self, dto: DemandeIdentificationInput, demandeur_id: uuid.UUID
    ) -> DemandeIdentificationOutput | IdentificationEnCoursException | DemandeInvalideException:  # noqa: E501
        """
        valide la demande utilisateur avant de la persister pour qu'elle soit réalisée.
        Pour l'instant on bride à une seule demande d'identification par workspace.
        Par la suite, on pourra envisager un filtre plus fin qui compare la demande avec les demandes en cours
        pour voir si on peut la traiter ou pas.
        """

        # on vérifie qu'il n'y a pas de demande en cours sur le workspace pour éviter un spam utilisateur
        self.log.info("Réception d'une demande d'identification: Demandeur(%s) Demande(%s)", demandeur_id, dto)
        self.log.debug(dto)
        self.log.debug(demandeur_id)
        demandes_en_cours = self._repo_identifications.find(
            DemandeIdentificationFiltre(workspace_id=dto.workspace_id, statuts=[StatutsDemande.EN_COURS, StatutsDemande.CREE])
        )
        if demandes_en_cours:
            self.log.error("Une demande d'identification est déjà en cours pour l'espace de travail %s", dto.workspace_id)
            self.log.debug("Demandes en cours: %s", demandes_en_cours)
            raise IdentificationEnCoursException(identifier=dto.workspace_id)

        # on vérifie que l'ensemble des exigences demandées appartiennent bien à l'espace de travail associé
        # et que le demandeur est bien le propriétaire de ces exigences
        exigences_obtenues = self._repo_exigences.find(ExigenceFiltre(owner_id=demandeur_id, ws_id=dto.workspace_id, exigences_ids=dto.exigences))

        self.log.debug("exigences obtenues: %i, exigences attendues: %i", len(exigences_obtenues), len(dto.exigences))

        if len(exigences_obtenues) != len(dto.exigences):
            exigences_obtenues_ids = [exigence.id for exigence in exigences_obtenues]
            exigences_manquantes = [str(id) for id in dto.exigences if id not in exigences_obtenues_ids]
            error_reason = f"les exigences suivantes sont inexistantes ou non-autorisées : {', '.join(exigences_manquantes)}"
            self.log.error(error_reason)
            raise DemandeInvalideException(error_reason=error_reason)

        # Initialize documents_parsed with a default value
        documents_parsed = None

        if dto.documents is not None and len(dto.documents) > 0:
            # Vérifier que les documents appartiennent à l'utilisateur
            documents_obtenus = self._repo_documents.find(DocumentFilter(owner_id=demandeur_id, document_ids=dto.documents))
            documents_obtenus_ids = [document.id for document in documents_obtenus]

            self.log.debug("documents obtenus: %i, documents attendus: %i", len(documents_obtenus), len(dto.documents))

            if len(documents_obtenus) != len(dto.documents):
                documents_manquants = [str(id) for id in dto.documents if id not in documents_obtenus_ids]
                error_reason = f"les documents suivants sont inexistants ou non-autorisés : {', '.join(documents_manquants)}"
                self.log.error(error_reason)
                raise DemandeInvalideException(error_reason=error_reason)

            documents_obtenus_ids = [document.id for document in documents_obtenus]
            if documents_obtenus_ids:
                documents_parsed = self._service_documents.get_parse_documents(documents_obtenus_ids)

        demande_a_persister = DemandeIdentificationStorage(
            workspace_id=dto.workspace_id,
            demandeur_id=demandeur_id,
            statut=StatutsDemande.CREE,
            entrants=PhotoEntrants(
                exigences=[ExigenceOutput(**exigence.model_dump()) for exigence in exigences_obtenues],
                documents=documents_parsed,
            ),
        )
        demande_cree = self._repo_identifications.add(demande_a_persister)
        return DemandeIdentificationOutput(**demande_cree.model_dump())

    def status_demande(self, demande_id: uuid.UUID) -> DemandeIdentificationOutput | NotFoundException:
        """
        renvoie le statut de la demande
        """
        demande = self._repo_identifications.get(demande_id)
        if demande:
            return DemandeIdentificationOutput(**demande.model_dump())
        else:
            return NotFoundException(identifier=demande_id)

    def maj_statut_demande(self, evenement: IdentificationEnCours | IdentificationEchouee | IdentificationReussie) -> None:
        """
        permet de mettre à jour le statut de la demande en fonction des évênement reçus lors du traitement de la demande
        """
        update_model = DemandeIdentificationUpdate(**evenement.model_dump())
        demande_modifiee = self._repo_identifications.update(update_model)
        print(demande_modifiee)
        return DemandeIdentificationOutput(**demande_modifiee.model_dump())

    def identifier(self, demande_id: uuid.UUID) -> None:
        """
        lance l'identification d'une demande traitée directement par le backend lui-même via des tâches asynchrones.
        """
        self.log.info("prise en compte de la demande d'identification %s", demande_id)
        demande_identification = self._repo_identifications.get(demande_id)

        if demande_identification is None:
            self.log.fatal("lancement d'une demande d'identification (%s) alors que celle-ci en'existe pas ! ", demande_id)
            return

        if demande_identification.statut != StatutsDemande.CREE:
            self.log.fatal("lancement d'une demande d'identification alors que celle-ci est dans un état final : %s", demande_identification)
            return

        identification_input = AgentIdentificationInput(
            demande=IdentificationEnCours(id=demande_id),
            exigences=demande_identification.entrants.exigences,
            documents=demande_identification.entrants.documents,
        )
        self.log.info("lancement de l'identification (%s)", identification_input.demande)
        self.maj_statut_demande(identification_input.demande)
        try:
            identification_output = self._agent.identifier(identification=identification_input)
            self.log.info("identification terminée (%s)", identification_output.demande)

            if identification_output.demande.statut == StatutsDemande.REUSSIE:
                self.log.info("sauvegarde des tests/bouchons générés pour la demande %s", demande_id)
                try:
                    if identification_output.tests is not None:
                        self.log.debug("persistance des testcases générées...")
                        for testcase in identification_output.tests:
                            self._service_testcases.add_generated_testcases(testcase)

                    if identification_output.bouchons is not None:
                        self.log.debug("persistance des bouchons générés...")
                        for bouchons_service in identification_output.bouchons:
                            self._service_bouchons.ajouter_bouchons(bouchons_service)

                    self.log.info("sauvegarde réussie (demande: %s)", demande_id)
                except Exception as error:
                    self.log.exception("erreur lors de la sauvegarde de la demande %s : %s", demande_id, error)
                    identification_output.demande.statut = StatutsDemande.EN_ERREUR
        except Exception as error:
            self.log.exception("erreur lors de l'identification de la demande %s : %s", demande_id, error)
            identification_output = AgentIdentificationOutput(demande=IdentificationEchouee(id=demande_id))

        self.log.debug("mise à jour du statut final de l'identification...")
        self.maj_statut_demande(identification_output.demande)
        self.log.info("identification terminée (%s - %s)", identification_output.demande.id, identification_output.demande.statut)

    def trouver_demandes(self, Filtredemande: DemandeIdentificationFiltre, limit: int = None) -> list[DemandeIdentificationOutputEnrichi]:
        """
        Récupère une liste de demandes d'identification filtrées et limite le nombre de résultats si nécessaire.
        Les demandes sont triées par date de création (de la plus récente à la plus ancienne).

        Args:
            Filtredemande (DemandeIdentificationFiltre): Les critères de filtrage pour les demandes d'identification.
            limit (int, optional): Le nombre maximum de demandes à retourner. Par défaut, aucune limite.
        Returns:
            list[DemandeIdentificationOutput]: Une liste des demandes d'identification sous forme de DTO.
        """
        self.log.info("Récupération de la liste des demandes d'identification triées par date de création")

        # Récupérer toutes les demandes depuis le repository
        demandes = self._repo_identifications.filtre(Filtredemande, limit)

        # Trier les demandes par date de création (plus récentes en premier)
        demandes = sorted(demandes, key=lambda d: d.date_creation, reverse=True)

        # Convertir les entités en DTO pour l'API, avec enrichissement des données
        resultats = []
        for demande in demandes:
            dto = DemandeIdentificationOutputEnrichi(**demande.model_dump())

            # Enrichir avec le nom du workspace
            try:
                workspace = self._repo_workspaces.get(demande.workspace_id)
                if workspace:
                    dto.workspace_name = workspace.nom
            except Exception as e:
                self.log.warning(f"Impossible de récupérer le nom du workspace {demande.workspace_id}: {e}")

            # Enrichir avec le nom du demandeur
            try:
                user = self._repo_utilisateurs.get(demande.demandeur_id)
                if user:
                    dto.demandeur_name = user.login
            except Exception as e:
                self.log.warning(f"Impossible de récupérer le nom du demandeur {demande.demandeur_id}: {e}")

            resultats.append(dto)

        return resultats
