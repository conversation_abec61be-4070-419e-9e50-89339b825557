export interface Generation {
    id: string;
    exigence_id: string;
    titre: string;
    description: string;
    statut: string;
    date_generation:Date;
    date_creation:Date;
    date_maj_statut:Date;
    implementations: TestImplementation[];
}
  
interface TestImplementation {
    contenu(contenu: any): unknown;
    format: string;
    id: string;
    details: string;
}

export interface Bouchon {
    id: string;    
    date_creation:Date;
    date_maj_statut:Date;
    usecases: BouchonUsecase[];
    implementations: BouchonImplementation[];
}

interface BouchonUsecase {
        description: string;
        appel: string;
        reponse: string;
        service_path: string;
        http_method: string;
        status_code: number;
        id: string;
        date_creation: Date;
        date_maj_statut: Date;
    }

interface BouchonImplementation {
    contenu(contenu: any): unknown;
    format: string;
    id: string;
    date_creation: Date;
    date_maj_statut: Date;
}