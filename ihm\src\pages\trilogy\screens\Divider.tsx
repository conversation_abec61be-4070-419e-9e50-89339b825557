import React from 'react'
import { Section, Divider, Box, BoxContent, IconName } from '@bytel/trilogy-react-ts'
import { TrilogyColor } from '@bytel/trilogy-react-ts'

export const DividerScreen = (): JSX.Element => {
  return (
    <Section>
      <Divider
        color={TrilogyColor.TERTIARY}
        backgroundColor={TrilogyColor.TERTIARY}
        textColor={TrilogyColor.WHITE}
        iconName={IconName.PLUS}
      />
      <Divider
        color={TrilogyColor.TERTIARY}
        textColor={TrilogyColor.TERTIARY}
        backgroundColor={TrilogyColor.WHITE}
        content={'Nouveau message'}
      />

      <Box>
        <BoxContent background={TrilogyColor.GREY_LIGHT}>
          <Divider unboxed />
        </BoxContent>
      </Box>
      <Divider />
    </Section>
  )
}
