import React from 'react';
import { Title, TitleLevels, Text } from '@bytel/trilogy-react-ts';

export interface IdentificationSectionProps {
  isIdentificationInProgress: boolean;
  lastIdentificationData: {
    demandeur_login: string;
    date_creation: string;
    date_debut: string | null;
    date_fin: string | null;
    statut: string;
  } | null;
  calculateElapsedTime: (startDate: Date) => string;
  calculateDuration: (startDate: Date, endDate: Date) => string;
}

const IdentificationSection: React.FC<IdentificationSectionProps> = ({
  isIdentificationInProgress,
  lastIdentificationData,
  calculateElapsedTime,
  calculateDuration
}) => {
  return (
    <div className="identification-container">
      <Title level={TitleLevels.THREE} className="identification-title">
        {isIdentificationInProgress
          ? 'Identification en cours...'
          : lastIdentificationData
            ? 'Dernière identification'
            : 'Aucune identification'}
      </Title>
      {lastIdentificationData ? (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
          fontSize: '13px',
        }}>
          {isIdentificationInProgress && lastIdentificationData.date_debut && (
            <div>
              <Text style={{ fontWeight: 'bold', display: 'block' }}>Durée écoulée:</Text>
              <Text>
                {calculateElapsedTime(new Date(lastIdentificationData.date_debut))}
              </Text>
            </div>
          )}

          {!isIdentificationInProgress && lastIdentificationData.date_fin && (
            <>
              <div>
                <Text style={{ fontWeight: 'bold', display: 'block' }}>Terminée le:</Text>
                <Text>{new Date(lastIdentificationData.date_fin).toLocaleString('fr-FR')}</Text>
              </div>

              {lastIdentificationData.date_debut && (
                <div>
                  <Text style={{ fontWeight: 'bold', display: 'block' }}>Durée totale:</Text>
                  <Text>
                    {calculateDuration(
                      new Date(lastIdentificationData.date_debut),
                      new Date(lastIdentificationData.date_fin)
                    )}
                  </Text>
                </div>
              )}

              <div>
                <Text style={{ fontWeight: 'bold', display: 'block' }}>Résultat:</Text>
                <Text style={{
                  color: lastIdentificationData.statut === 'succès' ? '#28a745' : '#dc3545',
                  fontWeight: 'bold'
                }}>
                  {lastIdentificationData.statut === 'succès' ? '✅ Succès' : '❌ Échec'}
                </Text>
              </div>
            </>
          )}
        </div>
      ) : (
        <Text>Veuillez générer votre première identification sur cet espace de travail.</Text>
      )}
    </div>
  );
};

export default IdentificationSection;
