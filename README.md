# 🐈 CATIA 

CATIA (Conception Accélérer des Tests avec IA) est une application dédiée à l'identification de tests à partir d'exigences qu'importe leur format (swagger, pdf, vidéo...).
Elle vous permet d'établir un cahier de tests tout en étant accompagné grâce à de l'IA générative.

Fini le syndrome de la page blanche, obtenez rapidement des suggestions pour vos tests à réaliser.

[📝 Documentation de l'application](https://outils-topsi.pages.gitlab.int.nbyt.io/CATIA/CATIA)

## TODO LIST 

- [ ] Initialiser les spécifications
    - Définir les besoins utilisateurs
    - Etablir l'architecture en accord avec ces besoins
        - essayer du C4 model avec markdown (mermaid potentiellement supporté ?)
    - définir un backlog
        - Faut-il créér une unité jira ou s'intégrer dans une unité existante ?
    - définir la stratégie de tests
        - bien évidemment dans Xray
        - voir une intégration cucumber/bdd pour Playwright/TestLibrary

- [ ] Demander la création du ST
    - faire une liste de proposition de nom (j'aime bien Cerno mais à décider en équipe)
    - mettre en place un poll
    - demander la création une fois le nom choisi

- [X] Valider avec les équipes craft (@CI-CD-Fameworks) la solution cible pour le back -> Sortie de Python si équivalent côté écosystème JAVA de:
    - [Pydantic](https://docs.pydantic.dev/latest/)
        - Indispensable pour la validation des données reçus du LLM
    - [FastAPI](https://fastapi.tiangolo.com/)
        - Surtout pour accélérer la mise en place d'API à partir de pydantic
    - [SQLModel](https://sqlmodel.tiangolo.com/)
        - ORM basé sur pydantic
        - Pas indispensable mais pas envie de réinventer la roue non plus
        - possiblement trop intégré et auquel cas on resterait juste sur du [SQLAlchemy](https://github.com/sqlalchemy/alembic)
    - [Langgraph/Langchain](https://github.com/langchain-ai/langgraph) 
        - Maestro très bon candidat pour l'ochestration à la place de langgraph
        - c'est surtout les besoins en [Checkpointers/Memory](https://langchain-ai.github.io/langgraph/concepts/persistence/) où j'ai des doutes
    - à noter qu'il existe [langchain4j](https://github.com/langchain4j/langchain4j) et [langgraph4j](https://github.com/bsorrentino/langgraph4j) mais pas sûr du niveau de maturité. Idem pour [spring-ai](https://spring.io/projects/spring-ai) qui propose du structured output mais ne gère pas encore le [multivector retrievial pattern](https://github.com/spring-projects/spring-ai/pull/2349)

- [ ] Compléter la CI selon les choix précédent
    - voir pour utiliser du [testcontainers](https://github.com/testcontainers/testcontainers-python) sur les integration
- [ ] Rediger la partie installation
- [ ] Réfléchir à un Onboarding directement dans l'IHM
    - l'application cible énormément de population différentes
    - l'expérience utilisateurs doit être une valeur fondamentale de l'application
 

### Autres

#### Feedback IHM pour les traitements pouvant prendre un certain temps

On va avoir des situations où les utilisateurs sur l'IHM vont uploader du contenu qu'il faudra traiter ou encore lancer des générations via IA qui peuvent prendre du temps (30s-5min).

Il faut donc voir comment communiquer en terme de feedback sur le fait que les traitements sont en cours sans pour autant forcer l'utilisateur à raffraichir sa page pour être notifié que le traitement est terminé.

Possibilité de faire

- polling 
- long-polling 
- websocket
- server-sent events

mais pas sûr de savoir quelle solution va être la plus efficace.

#### Utilisation de Storybook

il semblerait qu'il soit possible d'intégrer storybook à docusaurus [via des iframe](https://github.com/facebook/docusaurus/discussions/7713).
Pas idéal, mais envisageable car cela permettrait d'inclure directement les story comme maquettes des écrans/pages.

Après cela pose la question de comment s'organise la conception des maquettes si elles passent par du storybook.


#### Hexcode couleurs Bytel

#EA5B0F
#0055A4
#50BFDA 
#25465F


#### Mise en place documentation

Voir avec @bytel/quick-doc si c'est possible de paramétrer des thèmes du react-prism-render via un mapping dédié ou non.

Ou à minima changer de thème pour un qui rend le gherking mieux que celui-par défaut (palenight met juste en italic, c'est illisible).

Là j'ai contourné en désérialisant les objets en json, mais faut pas regarder le fichier docusaurus.json 🙈


#### Idée front si on a le temps

##### View Transitions pour rendre plus dynamique 

**Nécessite react 19**

- https://developer.mozilla.org/en-US/docs/Web/API/View_Transition_API/Using#basic_spa_view_transition 
- https://react.dev/reference/react/ViewTransition
- https://react.dev/blog/2025/04/23/react-labs-view-transitions-activity-and-more 
