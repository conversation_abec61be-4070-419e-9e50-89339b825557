import uuid
from datetime import datetime

from pydantic import BaseModel
from sqlmodel import Field, SQLModel

# Objet métier


class UserBase(BaseModel):
    """
    le modèle de données pour les utilisateurs
    """

    login: str
    actif: bool = True
    admin: bool = False
    date_inscription: datetime


# ce qui est persisté (Data Access Object)


class UserStorage(SQLModel, UserBase, table=True):
    """
    le modèle de données de la table utilisateurs
    """

    __tablename__ = "utilisateurs"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    login: str = Field(unique=True, index=True, max_length=30)


# ce qui est exposé vers l'extérieur (Data Transfert Objects)


class UserCreateInput(BaseModel):
    """
    le modèle de données pour ajouter un utilisateur lorsque celui-ci se connecte pour la 1ère fois à l'application
    """

    login: str = Field(max_length=30)


class UserUpdateInput(BaseModel):
    """
    le modèle de données lorsque l'on souhaite mettre à jour des informations
    """

    id: uuid.UUID
    actif: bool | None = None
    admin: bool | None = None


class UserOutput(BaseModel):
    """
    Comment un utilisateur est vu depuis l'API
    """

    id: uuid.UUID
    login: str
    date_inscription: datetime
    admin: bool | None = None
    actif: bool | None = None


class UsersOutput(BaseModel):
    """
    Comment les utilisateurs sont vus depuis l'API
    """

    total: int
    users: list[UserOutput] = []
