import React from 'react';
import { Link } from 'react-router-dom';

interface BreadcrumbNavProps {
  workspaceName: string;
}

const BreadcrumbNav: React.FC<BreadcrumbNavProps> = ({ workspaceName }) => (
  <nav className="breadcrumb-nav" aria-label="breadcrumb">
    <ol className="breadcrumb">
      <li className="breadcrumb-item">
        <Link to="/workspace">Espaces de travail</Link>
      </li>
      <li className="breadcrumb-item active" aria-current="page">
        {"> " + workspaceName}
      </li>
    </ol>
  </nav>
);

export default BreadcrumbNav;