"""
module pour gérer le démarrage de l'application
"""

import logging

from atlassian import Confluence
from pydantic.types import SecretStr

from app.common.config import (
    AppAgents,
    AppExternals,
    Application,
    AppRepos,
    AppServices,
    AppSettings,
    settings,
)
from app.common.llms import get_llm
from app.domains.agents.langage_naturel import AgentLangageNaturel
from app.domains.agents.openapi.agent import AgentOpenAPI
from app.domains.bouchons.service import BouchonsOpenApiService
from app.domains.documents.service import DocumentService
from app.domains.exigences.service import ExigenceService
from app.domains.extraction.service import ExtractionService
from app.domains.feedbacks.service import FeedbackService
from app.domains.identification.agent import AgentIdentifications
from app.domains.identification.service import IdentificationService
from app.domains.testcases.service import TestCasesService
from app.domains.users.service import UserService
from app.domains.workspace.service import WorkspaceService
from app.externals.atlassian.confluence import ConfluenceDocumentsUniques
from app.repositories.database import init_db_storage
from app.repositories.memory import init_memory_storage

LOGGER = logging.getLogger(__name__)


def init_storage(app: Application):
    if settings.STORAGE.type == "memory":
        LOGGER.warning("utilisation directe de la RAM. les données ne seront pas persistée")
        init_memory_storage(app.repos)
        return

    if settings.STORAGE.type == "memsql":
        LOGGER.warning("utilisation d'une base de donnée en RAM. les données ne seront pas persistée")
        # on profile d'avoir du python pour utiliser une spécificité de sqlite
        # qui a un mode permettant d'avoir une BDD mis en mémoire et non persistée.
        # c'est un entre deux potentiellement intéressant pour créer un environnement éphémère
        # tout en restant dans de la déclaration de schema et de l'ORM au niveau du code
        settings.DB.dialect = "sqlite"
        settings.DB.url = SecretStr("sqlite://")
        LOGGER.debug("created secret string for in memory sqlite")
        init_db_storage(app.repos, settings=settings)
        return

    if settings.STORAGE.type == "database":
        init_db_storage(app.repos, settings=settings)
        return

    raise NotImplementedError(f"Storage method not handled: {settings.STORAGE.type}")


def init_services(app: Application):
    """
    services qui sont basé uniquement sur notre persistence de données
    """
    app.services.users = UserService(app.repos.users)
    app.services.workspaces = WorkspaceService(app.repos.workspaces, app.repos.identifications, app.repos.users)
    app.services.exigences = ExigenceService(app.repos.exigences)
    app.services.testcases = TestCasesService(
        testcases=app.repos.testcases,
        utilisateurs=app.repos.users,
        exigences=app.repos.exigences,
    )
    app.services.bouchons = BouchonsOpenApiService(exigences=app.repos.exigences, bouchons=app.repos.bouchons)
    app.services.documents = DocumentService(documents=app.repos.documents, users=app.repos.users)
    app.services.feedbacks = FeedbackService(app.repos.feedbacks)


def init_external_services(app: Application):
    """
    services liés à des sources de données externe et donc pouvant être sujet à des indisponibilités
    """
    app.externals = AppExternals()

    if app.settings.LLM.enabled:
        LOGGER.info("vérification de la connexion aux LLM pour les différents Agents")
        app.externals.llm = get_llm(llm_config=app.settings.LLM, temperature=0)
        app.externals.llm_json = get_llm(
            llm_config=app.settings.LLM,
            temperature=0,
            model_kwargs={"response_format": {"type": "json_object"}},
        )

        app.agents = AppAgents()
        app.agents.langage_naturel = AgentLangageNaturel(llm=app.externals.llm, llm_json=app.externals.llm_json)
        app.agents.open_api = AgentOpenAPI(llm=app.externals.llm, llm_json=app.externals.llm_json)

        LOGGER.debug("intialisation du service d'identification")
        app.services.identifications = IdentificationService(
            exigences=app.repos.exigences,
            identifications=app.repos.identifications,
            agent=AgentIdentifications(app.agents),
            testcases=app.services.testcases,
            bouchons=app.services.bouchons,
            utilisateurs=app.repos.users,
            workspaces=app.repos.workspaces,
            documents=app.repos.documents,
            service_documents=app.services.documents,
        )
    else:
        LOGGER.warning("LLM désactivé.")

    if app.settings.ATLASSIAN.enabled:
        LOGGER.info("vérification de la connexion à confluence pour l'extraction d'exigence")
        if app.settings.ATLASSIAN.verify is False:
            LOGGER.error("vérification des certificats désactivée!")
        app.externals.confluence = Confluence(
            url=app.settings.ATLASSIAN.url,
            username=app.settings.ATLASSIAN.login,
            password=app.settings.ATLASSIAN.token.get_secret_value(),
            verify_ssl=app.settings.ATLASSIAN.verify,
            cloud=True,
        )
        LOGGER.debug("intialisation du service d'extraction")
        app.services.extraction = ExtractionService(
            repo_docs_uniques=ConfluenceDocumentsUniques(app.externals.confluence, app.settings.ATLASSIAN.official_url)
        )
    else:
        LOGGER.warning("Confluence désactivé.")


def init_app(app: Application, settings: AppSettings):
    """
    routine d'initialisation de l'application
    """
    app.settings = settings
    app.repos = AppRepos()
    app.services = AppServices()
    LOGGER.info("Initialisation du mode de persistance")
    init_storage(app)
    LOGGER.info("Initialisation des services applicatifs")
    init_services(app)

    if settings.DEGRADED:
        LOGGER.warning("Lancement de l'application en mode dégradé ! les fonctionnalités liés à des appels externes ne seront pas disponible")
        app.agents = None
        app.externals = None
    else:
        LOGGER.info("Initialisation des services liés à des sources de données externes")
        init_external_services(app)


APPLICATION = Application()
