// définition de nom ou variables pouvant être amené à changer dans le temps
import { Annoter, Paragraphe } from './components'

export const ApplicationName = 'App ???'


// function detailsIdentificationsTests() {
//     return (<>
//         <p>l'identification des tests correspond au moment où on lit une exigence et cherche à déterminer des tests qui serait à réaliser pour valider cette exigence</p>
//         <p>Durant cette phase, on cheche à savoir ce qu'il faut tester. On va donc définir les objectifs de test sans s'intéresser à comment (étapes de test)</p>
//     </>)
// }
const detailsIdentificationsTests = "l'identification des tests correspond au moment où on lit une exigence \
et cherche à déterminer des tests qui serait à réaliser pour valider cette exigence\n \
Durant cette phase, on cherche à savoir ce qu'il faut tester.\n\
On va donc définir les objectif de test et les vérifications qui serait à faire sans s'intéresser à comment ces vérifications sont faites exactement.\n\
C'est lors de la phase de conception que l'on détaille le \"comment\" via les étapes de tests et les jeux de données associés"

// const detailsIdentificationsTests = <Paragraphe text={"l'identification des tests correspond au moment où on lit une exigence et cherche à déterminer des tests qui serait à réaliser pour valider cette exigence\nDurant cette phase, on cherche à savoir ce qu'il faut tester.\nOn va donc définir les objectifs de test sans s'intéresser à comment (étapes de test)"}/>

export const identificationTests = <Annoter details={<Paragraphe text={detailsIdentificationsTests}/>}>identification des tests</Annoter>

const detailsC4model = "La modélisation des 4C (Context, Containers, Components, Code) \
se veut une alternative située entre le modèle UML et le modèle « boiboite » (également connu sous le nom de « à l'arrache »).\n\
Elle cherche à garder une partie du formalisme de l'UML sans imposer à l'ensemble des parties prenantes de connaitre la notation UML sur le bout des doigts"

export const C4model = <Annoter details={<Paragraphe text={detailsC4model}/>}>modélisation des 4C</Annoter>
