import React, { useEffect, useState } from "react";
import { Tree } from "primereact/tree";
import { TreeNode } from "primereact/treenode";
import { ScrollPanel } from "primereact/scrollpanel";
import { Button, Icon, IconName, AlertState, Text, TextLevels, TypographyBold, IconColor, Divider } from "@bytel/trilogy-react-ts";
import { DocumentOutput } from "types/document";
import { FaCloudDownloadAlt, FaEdit, FaRegTrashAlt, FaFile } from "react-icons/fa";

import "primereact/resources/primereact.min.css";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primeicons/primeicons.css";

interface DocumentTreeProps {
  documents: DocumentOutput[];
  isLoading: boolean;
  onDownload: (doc: DocumentOutput) => void;
  onUpdate: (doc: DocumentOutput) => void;
  onDelete: (documentId: string) => void;
}

// Fonction pour organiser les documents directement sans dossier
const organizeDocumentsDirectly = (documents: DocumentOutput[]): TreeNode[] => {
  return documents.map(doc => ({
    key: doc.id,
    label: doc.filename,
    data: doc,
    selectable: false
  }));
};

const DocumentTree: React.FC<DocumentTreeProps> = ({ 
  documents, 
  isLoading, 
  onDownload, 
  onUpdate, 
  onDelete 
}) => {
  const [treeNodes, setTreeNodes] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const nodes = organizeDocumentsDirectly(documents);
    setTreeNodes(nodes);
    
    // Pas besoin d'expansion car les documents sont directement à la racine
    setExpandedKeys({});
  }, [documents]);



  const nodeTemplate = (node: TreeNode) => {
    const isDocument = node.data && typeof node.data === 'object';
    const document = node.data as DocumentOutput;

    if (isDocument) {
      return (
        <>
        <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
          <div style={{ display: "flex", alignItems: "center" }}>
            <FaFile style={{ marginRight: "8px", color: "#666" ,height:"15px", width:"15px" }} />
            <div>
              <Text style={{ fontSize: "1.2rem", fontWeight: "500" }}>
                {document.filename}
              </Text>
              <div style={{ fontSize: "0.9rem", color: "#666" }}>
                Uploadé: <b>{new Date(document.date_upload).toLocaleDateString()}</b>
                {document.date_maj && (
                  <> • Modifié: <b>{new Date(document.date_maj).toLocaleDateString()}</b></>
                )}
              </div>
            </div>
          </div>
          
          <div style={{ display: "flex", gap: "5px" }}>
            <Button 
              onClick={() => onDownload(document)} 
              variant="NEUTRAL" 
              title="Télécharger"
              size="small"
            >
              <FaCloudDownloadAlt />
            </Button>
            <Button 
              onClick={() => onUpdate(document)} 
              variant="NEUTRAL" 
              title="Mettre à jour"
              size="small"
            >
              <FaEdit />
            </Button>
            <Button 
              onClick={() => onDelete(document.id)} 
              className="profile-btn-red" 
              title="Supprimer"             
              size="small"
            >
              <FaRegTrashAlt />
            </Button>
          </div>
          
        </div>
        
        </>
      );
    }

    // Ce template ne sera plus utilisé car il n'y a plus de dossiers
    return null;
  };

  if (isLoading) {
    return <Text>Chargement des documents...</Text>;
  }

  if (documents.length === 0) {
    return <Text className="mt-3">Vous n'avez aucun document.</Text>;
  }

  return (
    <>
      <ScrollPanel 
       className="custom-scrollpanel">
        <Tree 
          value={treeNodes}
          expandedKeys={expandedKeys}
          onToggle={(e) => setExpandedKeys(e.value)}
          nodeTemplate={nodeTemplate}
          className="w-full md:w-30rem"
          filter
          filterMode="lenient"
          filterPlaceholder="Rechercher un document..."
        />
      </ScrollPanel>
      
    </>
  );
};

export default DocumentTree;