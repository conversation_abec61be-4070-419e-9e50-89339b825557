import { Service } from "./commonService";
import { BQuery } from "@bytel/query";
import { WorkspaceBase, WorkspaceOutput } from '../types/workspace';

// Définition des contextes d'URL possibles
export type ApiContext = 'default' | 'workspace' | 'admin';

// Définition des types de Workspace
export interface Workspace {
  id: string;
  nom: string;
  description: string;
  date_creation: Date;
}

export interface IdentificationResponse {
  statut: string;
  message: string;
}

export interface WorkspaceStatusResponse {
  is_busy: boolean;
  last_identification_request: {
    demandeur_login: string;
    date_creation: string;
    date_debut: string | null;
    date_fin: string | null;
    statut: string;
  } | null;
}

export interface LastIdentificationData {
  demandeur_login: string;
  date_creation: string;
  date_debut: string | null;
  date_fin: string | null;
  statut: string;
}

/**
 * Service WorkspaceService utilisant BQuery
 * @class WorkspaceService
 * @extends Service
 */
export class WorkspaceService extends Service {
  private baseUrls = {
    default: '',
    workspace: '/workspace',
    admin: '/administration'
  };

  constructor(bquery: BQuery) {
    super(bquery);
  }

  // Méthode pour obtenir l'URL de base en fonction du contexte
  private getBaseUrl(context: ApiContext = 'default'): string {
    return this.baseUrls[context];
  }

  /**
   * Récupère un workspace par son ID
   * @param {string} id ID du workspace
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<Workspace | null>} Le workspace ou null si non trouvé
   */
  async getWorkspace(id: string, context: ApiContext = 'default'): Promise<Workspace | null> {
    try {
      const baseUrl = this.getBaseUrl(context);
      const response = await this.bquery.get(`${baseUrl}/workspaces/${id}`);
      return response as Workspace;
    } catch (error) {
      console.error(`Failed to fetch workspace with id ${id}:`, error);
      return null;
    }
  }

  /**
   * Récupère la liste des workspaces de l'utilisateur
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<Workspace[]>} Liste des workspaces
   */
  async searchWorkspaces(context: ApiContext = 'default'): Promise<Workspace[]> {
    const baseUrl = this.getBaseUrl(context);
    return this.bquery.get(`${baseUrl}/me/workspaces`).then(response => response as Workspace[]);
  }

  /**
   * Récupère tous les workspaces (pour administration)
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<WorkspaceOutput[]>} Liste des workspaces
   */
  async listWorkspaces(context: ApiContext = 'default'): Promise<WorkspaceOutput[]> {
    const baseUrl = this.getBaseUrl(context);
    return this.bquery.get<WorkspaceOutput[]>(`${baseUrl}/workspaces`);
  }

  /**
   * Crée un nouveau workspace
   * @param {WorkspaceBase} workspace Données du workspace à créer
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<Workspace>} Le workspace créé
   */
  async createWorkspace(data: WorkspaceBase, context: ApiContext = 'default'): Promise<Workspace> {
    const baseUrl = this.getBaseUrl(context);
    const response = await this.bquery.post(`${baseUrl}/workspaces`, data);
    return response as Workspace;
  }

  /**
   * Poste une identification
   * @param {Object} data Données d'identification
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<IdentificationResponse>} Réponse de l'identification
   */
  async postIdentification(data: { demandeur_id: string; workspace_id: string; exigences: string[] }, context: ApiContext = 'default'): Promise<IdentificationResponse> {
    const baseUrl = this.getBaseUrl(context);
    const response = await this.bquery.post(`${baseUrl}/identifications`, data);
    return response as IdentificationResponse;
  }
  /**
   * Supprime un workspace
   * @param {string} id ID du workspace à supprimer
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<void>}
   */
  async deleteWorkspace(id: string, context: ApiContext = 'default'): Promise<void> {
    const baseUrl = this.getBaseUrl(context);
    await this.bquery.delete(`${baseUrl}/workspaces/${id}`);
  }
  /**
   * Verifier si il y a une identification en cours
   * @param {string} id ID du workspace
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<boolean>} true si une identification est en cours, false sinon
   */
  async isIdentificationInProgress(id: string, context: ApiContext = 'default'): Promise<boolean> {
    const status = await this.getWorkspaceStatus(id, context);
    return status.is_busy;
  }

  /**
   * Récupère le statut complet du workspace incluant les informations de la dernière demande d'identification
   * @param {string} id ID du workspace
   * @param {ApiContext} context Le contexte de l'API
   * @returns {Promise<WorkspaceStatusResponse>} Statut complet du workspace
   */
  async getWorkspaceStatus(id: string, context: ApiContext = 'default'): Promise<WorkspaceStatusResponse> {
    const baseUrl = this.getBaseUrl(context);
    const response = await this.bquery.get(`${baseUrl}/workspaces/${id}/isbusy`);
    return response as WorkspaceStatusResponse;
  }
}