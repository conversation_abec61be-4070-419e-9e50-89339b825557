import React, { useState, useEffect } from 'react';
import { Box, Button, Table } from '@bytel/trilogy-react-ts';
import { useAPIClient } from '../../../providers/api';
import { FeedbackOutput } from '../../../services/feedbackService';
import { Workspace } from '../../../services/workspaceService';
import { Requirement } from '../../../services/interfaces/requirementInterfaces';
import { Generation } from '../../../services/interfaces/generationInterfaces';
import styled from 'styled-components';
import { UserOutput } from '../../../types/user';
import FeedbackDetailsModal from './FeedbackDetailsModal';
import { FeedbackWithDetails } from '../../../types/feedback';

const StyledRating = styled.span<{ rating: number }>`
  color: ${props => {
    switch (props.rating) {
      case 1: return 'red';
      case 2: return 'orange';
      case 3: return 'gold';
      case 4: return 'yellowgreen';
      case 5: return 'green';
      default: return 'gold';
    }
  }};
  font-size: 20px;
`;

interface FeedbackTableProps {
  feedbacks: FeedbackOutput[];
  isLoading: boolean;
}

const FeedbackTable: React.FC<FeedbackTableProps> = ({ feedbacks, isLoading }) => {
  const [feedbacksWithDetails, setFeedbacksWithDetails] = useState<FeedbackWithDetails[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackWithDetails | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const { workspaceService, requirementService, generationService, userService } = useAPIClient();

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        // Fetch all users once
        const { users } = await userService.listUsers();
        
        const enrichedFeedbacks = await Promise.all(
          feedbacks.map(async (feedback) => {
            try {
              const [workspace, exigence, generation] = await Promise.all([
                workspaceService.getWorkspace(feedback.workspace_id),
                requirementService.getRequirement(feedback.exigence_id),
                generationService.getGenerationById(feedback.generation_id)
              ]);
              
              // Find user in the users list
              const user = users.find(u => u.id === feedback.user_id);
              
              return { 
                ...feedback, 
                workspace, 
                exigence, 
                generation,
                user
              };
            } catch (error) {
              console.error(`Error fetching details for feedback ${feedback.id}:`, error);
              return { 
                ...feedback, 
                workspace: null, 
                exigence: null, 
                generation: null,
                user: null 
              };
            }
          })
        );
        setFeedbacksWithDetails(enrichedFeedbacks);
      } catch (error) {
        console.error('Error fetching users:', error);
      }
    };

    fetchDetails();
  }, [feedbacks]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getRatingDisplay = (rating: number) => {
    return (
      <StyledRating rating={rating}>
        {'★'.repeat(rating)}
        <span style={{ color: '#e0e0e0' }}>{'★'.repeat(5 - rating)}</span>
        {` (${rating}/5)`}
      </StyledRating>
    );
  };

  const handleShowDetails = (feedback: FeedbackWithDetails) => {
    setSelectedFeedback(feedback);
    setShowDetailsModal(true);
  };

  if (isLoading) {
    return (
      <Box className="d-flex justify-content-center p-3">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Chargement des Feedbacks...</span>
        </div>
      </Box>
    );
  }

  if (feedbacks.length === 0) {
    return (
      <Box className="p-2">
        <p>Aucun feedback trouvé</p>
      </Box>
    );
  }

  return (
    <>
      <Table fullwidth bordered>
        <thead>
          <tr>
            <th>Date</th>
            <th>Utilisateur</th>
            <th>Workspace</th>
            <th>Note</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {feedbacksWithDetails.map((feedback) => (
            <tr key={feedback.id}>
              <td>{formatDate(feedback.created_at)}</td>
              <td>{feedback.user?.login || 'Utilisateur inconnu'}</td>
              <td>{feedback.workspace?.nom || 'Workspace inconnu'}</td>
              <td>{getRatingDisplay(feedback.rating)}</td>
              <td>
                <Button 
                  variant="TERTIARY" 
                  onClick={() => handleShowDetails(feedback)}
                >
                  Voir les détails
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      <FeedbackDetailsModal
        feedback={selectedFeedback}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
      />
    </>
  );
};

export default FeedbackTable;