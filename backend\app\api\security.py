"""
middlewares de l'API qui sont liés à l'authentification et la sécurité de l'API
"""

import logging
from typing import Annotated

import jwt
from fastapi import Depends, FastAPI, HTTPException, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2Authorization<PERSON>ode<PERSON>earer
from jwt import PyJWKClient

from app.common.bootstrap import APPLICATION, settings

# from .repository import ExigenceRepositoryInterface
from app.common.config import AppSettings
from app.domains.users.models import UserOutput

logger = logging.getLogger(__name__)


def init_api_security(api: FastAPI, settings: AppSettings):
    logger.info(f"Initializing API security for environment: {settings.ENVIRONMENT}")

    if settings.ENVIRONMENT in ["dev", "local"]:
        logger.info("Configuring CORS for local environment")
        api.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    else:
        logger.error("CORS configuration not implemented for non-local environments")
        raise NotImplementedError("Initialisation des CORS à implémenter")


oauth_2_scheme = OAuth2AuthorizationCodeBearer(
    tokenUrl=f"{settings.OIDC.realm_url}/protocol/openid-connect/token",
    authorizationUrl=f"{settings.OIDC.realm_url}/protocol/openid-connect/auth",
    refreshUrl=f"{settings.OIDC.realm_url}/protocol/openid-connect/token",
)


async def validate_access_token(access_token: Annotated[str, Depends(oauth_2_scheme)]):
    url = f"{settings.OIDC.realm_url}/protocol/openid-connect/certs"

    logger.info("Starting token validation")

    jwks_client = PyJWKClient(url)
    logger.debug("Verifying JWK token")

    try:
        signing_key = jwks_client.get_signing_key_from_jwt(access_token)
        logger.debug("Successfully retrieved signing key")

        data = jwt.decode(jwt=access_token, key=signing_key, options={"verify_aud": False})
        logger.info("Token successfully validated")
        # print(data)
        return data

    except jwt.exceptions.InvalidTokenError as error:
        logger.error(f"Token validation failed: {str(error)}", exc_info=True)
        raise HTTPException(status_code=401, detail="Not authenticated")


async def validate_admin_token(decrypted_token: Annotated[dict, Depends(validate_access_token)]):
    logger.info("Validating admin privileges")
    if not decrypted_token:
        logger.error("Token data is empty or invalid")
        raise HTTPException(status_code=401, detail="Invalid authentication token")

    login = decrypted_token.get("preferred_username")
    logger.debug(f"Checking admin privileges for user: {login}")
    if not APPLICATION.services.users.is_admin(login):
        logger.error(f"User {login} does not have admin privileges")
        raise HTTPException(status_code=403, detail="User does not have admin privileges")


async def get_current_user(token_data: Annotated[dict, Depends(validate_access_token)]) -> UserOutput:
    """
    Récupère l'utilisateur actuellement authentifié depuis la base de données

    Args:
        token_data: Les données du token JWT décodé et validé

    Returns:
        UserOutput: L'utilisateur courant avec toutes ses informations
    """
    if not token_data:
        logger.error("Token data is empty or invalid")
        raise HTTPException(status_code=401, detail="Invalid authentication token")

    login = token_data.get("preferred_username")
    if not login:
        logger.error("Invalid token: missing preferred_username")
        raise HTTPException(status_code=401, detail="Invalid token: missing preferred_username")

    logger.debug(f"Getting current user with login: {login}")
    try:
        user = APPLICATION.services.users.handle_first_connection(login)
        logger.info(f"Current user retrieved: (ID: {user.id})")
        return user
    except Exception as e:
        logger.error(f"Error retrieving current user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")


USER_AUTHENTIFICATION: Security = Security(validate_access_token, scopes=["openid email profile"])

ADMIN_AUTHENTIFICATION: Security = Security(validate_admin_token)

CURRENT_USER: Security = Security(get_current_user)
