import React, { useState, useEffect } from 'react';
import { Text, Button, Tabs, TabsItem } from '@bytel/trilogy-react-ts';
import { AiOutlineAlignLeft, AiOutlineHourglass } from 'react-icons/ai'; 
import { FaUserAlt } from 'react-icons/fa'; 
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LuFileText } from "react-icons/lu";
import { IoPlay } from "react-icons/io5";

interface Requirement {
  id: string;
  nom: string;
  type: string;
  ws_id: string;
  owner_id: string;
  description: string;
  data: string;
}

interface RequirementsContainerProps {
  filteredRequirements: Requirement[];
  handleRequirementClick: (index: number) => void;
  handleDetailsClick?: () => void;
  handleGenerateClick?: () => void;
  isIdentificationInProgress?: boolean; // Ajout de cette prop
  setTestCasesActiveTabIndex: React.Dispatch<React.SetStateAction<number>>;
  getGenerationsForRequirement?: (requirementId: string) => Promise<any[]>;
  identificationCompletedTrigger?: number;
}

const truncateText = (text: string, maxLength: number) => {
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
};

const RequirementsContainer: React.FC<RequirementsContainerProps> = ({
  filteredRequirements,
  handleRequirementClick,
  handleDetailsClick,
  handleGenerateClick,
  isIdentificationInProgress = false, // Valeur par défaut
  setTestCasesActiveTabIndex,
  getGenerationsForRequirement,
  identificationCompletedTrigger
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [requirementGenerations, setRequirementGenerations] = useState<{ [key: string]: any[] }>({});

  // Charger les générations pour une exigence spécifique
  const loadGenerationsForRequirement = async (requirementId: string, forceReload: boolean = false) => {
    if (!getGenerationsForRequirement || (!forceReload && requirementGenerations[requirementId])) return;
    
    try {
      const generations = await getGenerationsForRequirement(requirementId);
      setRequirementGenerations(prev => ({
        ...prev,
        [requirementId]: generations
      }));
    } catch (error) {
      console.error('Erreur lors du chargement des générations:', error);
      setRequirementGenerations(prev => ({
        ...prev,
        [requirementId]: []
      }));
    }
  };

  // Fonction pour forcer le rechargement des générations pour toutes les exigences visibles
  const refreshAllGenerations = async () => {
    if (!getGenerationsForRequirement) return;
    
    const refreshPromises = filteredRequirements.map(req => 
      loadGenerationsForRequirement(req.id, true)
    );
    
    await Promise.all(refreshPromises);
  };

  // Effect pour rafraîchir les générations quand une identification se termine
  useEffect(() => {
    if (identificationCompletedTrigger && identificationCompletedTrigger > 0) {
      // Une identification vient de se terminer, rafraîchir les générations
      console.log("Identification terminée, rafraîchissement des générations...");
      refreshAllGenerations();
    }
  }, [identificationCompletedTrigger, filteredRequirements, getGenerationsForRequirement]);

  // Effect pour charger les générations au montage du composant
  useEffect(() => {
    // Charger les générations pour toutes les exigences visibles au démarrage
    filteredRequirements.forEach(req => {
      if (req.id) {
        loadGenerationsForRequirement(req.id);
      }
    });
  }, [filteredRequirements]);

  const handleRowClick = (index: number) => {
    const newIndex = activeIndex === index ? null : index;
    setActiveIndex(newIndex);
    handleRequirementClick(index);
    setTestCasesActiveTabIndex(0);
  };

  const getIcon = (type: string, isActive: boolean) => {
    const iconColor = isActive ? '#fff' : '#25465F';
    const iconStyle = { marginRight: '8px' };

    switch(type) {
      case 'langage naturel':
        return <LuFileText size={20} color={iconColor} style={iconStyle} />;
      case 'OpenAPI':
        return <LuFileJson size={20} color={iconColor} style={iconStyle} />;
      case 'userStory':
        return <FaUserAlt size={20} color={iconColor} style={iconStyle} />;
      default:
        return null;
    }
  };

  return (
    <div className="requirements-container">
      {filteredRequirements.map((req, index) => {
        const isActive = activeIndex === index;

        return (
          <div
            key={index}
            className={`requirement-row${isActive ? ' active' : ''}`}
            style={{ display: 'flex', alignItems: 'center', width: '100%' }}
            onClick={() => handleRowClick(index)}
          >
            <div 
              className="flex-align-center requirement-row-content"
              style={{ width: '60%' }}
            >
              {getIcon(req.type, isActive)}
              <Text className={`requirement-text${isActive ? ' color-white' : ' color-25465f'}`}>{truncateText(req.nom, 40)}</Text>
            </div>
            {isActive ? (
              <div className="requirements-buttons flex-align-center ml-10 gap-5">
                <Button variant="SECONDARY"
                onClick={handleDetailsClick}>
                  <AiOutlineAlignLeft size={20} color={isActive ? '#fff' : '#25465F'} />
                </Button>
                <Button 
                  variant="PRIMARY"
                  onClick={handleGenerateClick}
                  disabled={isIdentificationInProgress}
                >
                  {isIdentificationInProgress ? 
                  <AiOutlineHourglass size={20} color={isIdentificationInProgress ? '#999' : undefined} /> : 
                  <IoPlay size={20} color={isIdentificationInProgress ? '#999' : undefined} />}
                </Button>
              </div>
            ) : (
              <div style={{ width: '40%', display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                {/* Progress bar compacte */}
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {(() => {
                    const generations = requirementGenerations[req.id] || [];
                    const total = generations.length;
                    const created = generations.filter(g => g.statut === 'créé').length;
                    const validated = generations.filter(g => g.statut === 'validé').length;
                    const rejected = generations.filter(g => g.statut === 'rejeté' || g.statut === 're-identifié').length;
                    // Pourcentage par statut
                    const createdPct = total ? (created / total) * 100 : 0;
                    const validatedPct = total ? (validated / total) * 100 : 0;
                    const rejectedPct = total ? (rejected / total) * 100 : 0;
                    const otherPct = 100 - createdPct - validatedPct - rejectedPct;
                    return (
                      <>
                        <div style={{ width: 140, height: 6, borderRadius: 3, background: '#e5e7eb', overflow: 'hidden', display: 'flex' }}>
                          {createdPct > 0 && (
                            <div
                              style={{ width: `${createdPct}%`, background: '#17a2b8' }}
                              title={`Créé : ${created}`}
                            />
                          )}
                          {validatedPct > 0 && (
                            <div
                              style={{ width: `${validatedPct}%`, background: '#28a745' }}
                              title={`Validé : ${validated}`}
                            />
                          )}
                          {rejectedPct > 0 && (
                            <div
                              style={{ width: `${rejectedPct}%`, background: '#dc3545' }}
                              title={`Rejeté : ${rejected}`}
                            />
                          )}
                          {otherPct > 0 && (
                            <div
                              style={{ width: `${otherPct}%`, background: '#adb5bd' }}
                              title={`Autre : ${Math.round((otherPct * total) / 100)}`}
                            />
                          )}
                        </div>
                        <span style={{ fontSize: 11, color: '#888', marginLeft: 8, whiteSpace: 'nowrap' }}>{`Total: ${requirementGenerations[req.id]?.length || 0}`}</span>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default RequirementsContainer;
