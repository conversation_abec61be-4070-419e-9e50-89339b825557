from fastapi import APIRouter
from fastapi.responses import RedirectResponse, Response

from app.api.routes import bouchons, debug, exigences, extraction, feedbacks, identification, testscases, users, workspaces
from app.common.config import settings

api_router = APIRouter()


@api_router.get("/", include_in_schema=False)
def read_root():
    return RedirectResponse("/documentation")


@api_router.get("/healthy", include_in_schema=False)
def app_is_healthy():
    # TODO : voir si besoin d'une méthode précise par la suite
    return Response(content="app is healthy", status_code=200)


@api_router.get("/alive", include_in_schema=False)
def app_is_ready():
    return True


api_router.include_router(users.router)
api_router.include_router(workspaces.router)
api_router.include_router(exigences.router)
api_router.include_router(testscases.router)
api_router.include_router(identification.router)
api_router.include_router(bouchons.router)
api_router.include_router(extraction.router)
api_router.include_router(feedbacks.router)
if settings.ENVIRONMENT == "dev":
    api_router.include_router(debug.router)
