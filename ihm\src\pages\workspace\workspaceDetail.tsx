import React, { useState, useEffect } from 'react';
import { Box, BoxContent, BoxHeader, Container, Text, InputChangeEvent, Popover } from '@bytel/trilogy-react-ts';
import { TextareaChangeEvent } from '@bytel/trilogy-react-ts/lib/components/textarea/TextareaProps';
import { useParams } from 'react-router-dom';
import { useAPIClient } from 'providers/api';
import { useSnackbar } from 'providers/snackbar';
import { useQuery } from 'react-query';
import { AxiosError } from 'axios';
import { Requirement } from "services/interfaces/requirementInterfaces";
import { Generation,Bouchon } from "services/interfaces/generationInterfaces";
import { DocumentOutput } from 'types/document'; // Assurez-vous que le chemin est correct

import BreadcrumbNav from 'components/BreadcrumbNav';
import Banner from 'components/Banner';
import TestCasesHeader from 'components/TestCasesHeader';
import DetailsContainer from 'components/DetailsContainer';
import RequirementsHeader from 'components/RequirementsHeader';
import ChatAnimation, { chatAnimationMessageSystem } from 'layout/ChatAnimation';
import RequirementsContainer from 'components/RequirementsContainer';

import './workspaceAll.css';
import { SelectChangeEvent } from '@bytel/trilogy-react-ts/lib/components/select/SelectProps';

const WorkspaceDetailPage: React.FC = () => {
  const { id: workspaceId } = useParams<{ id: string }>();
  const { showError, showInfo } = useSnackbar();
  const { workspaceService, requirementService, generationService, documentService } = useAPIClient();

  const { data: workspace, error, isLoading } = useQuery(
    ["workspace", workspaceId],
    () => workspaceId ? workspaceService.getWorkspace(workspaceId) : Promise.reject("Workspace ID is undefined")
  );

  const { data: requirements, error: reqError, isLoading: reqLoading } = useQuery(
    ["requirements", workspaceId],
    () => workspaceId ? requirementService.getRequirementsByWorkspaceId(workspaceId) : Promise.reject("Workspace ID is undefined")
  );

  // Hook pour récupérer le statut du workspace
  const { data: workspaceStatus, isLoading: statusLoading, refetch: refetchWorkspaceStatus } = useQuery(
    ["workspaceStatus", workspaceId],
    () => workspaceId ? workspaceService.getWorkspaceStatus(workspaceId) : Promise.reject("Workspace ID is undefined"),
    {
      refetchInterval: 30000, // Rafraîchir toutes les 30 secondes
      refetchIntervalInBackground: false,
    }
  );

  // Tous vos états existants
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filteredRequirements, setFilteredRequirements] = useState<Requirement[]>([]);
  const [activeRequirement, setActiveRequirement] = useState<Requirement | null>(null);
  const [activeGeneration, setActiveGeneration] = useState<Generation | null>(null);
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [isModalActive, setIsModalActive] = useState<boolean>(false);
  const [isGenerateModalActive, setIsGenerateModalActive] = useState<boolean>(false);
  const [selectedExigencesIds, setSelectedExigencesIds] = useState<string[]>([]);
  const [TestCasesactiveTabIndex, setTestCasesActiveTabIndex] = useState<number>(0);
  const [bouchons, setBouchons] = useState<Bouchon[]>([]);
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0);
  const [isDetailsModalActive, setIsDetailsModalActive] = useState<boolean>(false);
  const [isSimpleGenerateModalActive, setIsSimpleGenerateModalActive] = useState<boolean>(false);
  const [bannerKey, setBannerKey] = useState<number>(0);
  
  // Ajoutez ces deux lignes ici
  const [availableDocuments, setAvailableDocuments] = useState<DocumentOutput[]>([]);
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  
  const [newRequirement, setNewRequirement] = useState<Requirement>({
    id: '',
    nom: '',
    description: '',
    type: '',
    data: '',
    owner_id: '',
    ws_id: workspaceId ?? '',
  });

  // Calculer si une identification est en cours à partir des données du workspace
  const isIdentificationInProgress = workspaceStatus?.is_busy || false;
  // État pour notifier la fin d'identification
  const [identificationJustCompleted, setIdentificationJustCompleted] = useState<number>(0);

  useEffect(() => {
    if (requirements) {
      setFilteredRequirements(requirements);
    }
  }, [requirements]);

  useEffect(() => {
    if (activeRequirement !== null) {
      const requirementId = filteredRequirements[activeRequirement]?.id;
      if (requirementId) {
        loadGenerations(requirementId);
      }
    }
  }, [activeRequirement, filteredRequirements]);

  const loadGenerations = async (requirementId: string) => {
    try {
      const gens = await generationService.getGenerationsByRequirementId(requirementId);
      setGenerations(gens);
      const bouchons = await generationService.getBouchonsByRequirementId(requirementId);
      setBouchons(bouchons);
    } catch (error) {
      showError(error as AxiosError, {
        message: "Erreur lors de la récupération des générations"
      });
    }
  };

  // Fonction helper pour récupérer les générations d'une exigence
  const getGenerationsForRequirement = async (requirementId: string) => {
    try {
      const gens = await generationService.getGenerationsByRequirementId(requirementId);
      return gens || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des générations:', error);
      return [];
    }
  };

  if (error || reqError) {
    showError((error || reqError) as AxiosError, {
      message: "Erreur lors de la récupération des détails du workspace"
    });
  }

  if (isLoading || reqLoading) {
    return <div>Chargement...</div>;
  }

  if (!workspace || !requirements) {
    return <div>Workspace or Requirements not found</div>;
  }

  const handleInputChange = (event: InputChangeEvent) => {
    const { inputValue, inputName } = event;
    setNewRequirement(prevState => ({
      ...prevState,
      [inputName]: inputValue,
    }));
  };
  
  const handleTextareaChange = (event: TextareaChangeEvent) => {
    const { textareaValue, textareaName } = event;
    setNewRequirement(prevState => ({
      ...prevState,
      [textareaName]: textareaValue,
    }));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = event.target.value;
    setSearchTerm(searchValue);
  
    if (!requirements) {
      setFilteredRequirements([]);
      return;
    }
  
    const filtered = requirements
      .filter((req: Requirement) => req && req.nom.toLowerCase().includes(searchValue.toLowerCase()));
  
    setFilteredRequirements(filtered);
  };

  const handleSelectChange = (value: string | number | SelectChangeEvent) => {
    if (typeof value === 'object') {
      const { selectValue, selectName } = value;
      setNewRequirement((prevState) => ({
        ...prevState,
        [selectName as string]: selectValue,
      }));
    }
  };

  const handleCreateRequirement = async () => {
    try {
      console.log(newRequirement)
      await requirementService.createRequirement(
        newRequirement.nom,
        newRequirement.description,
        newRequirement.type,
        newRequirement.data,
        newRequirement.ws_id
      );
      //nouvelle version de banner pour vider le formulaire  
      setBannerKey(prevKey => prevKey + 1);
      setNewRequirement({
        id: '',
        nom: '',
        description: '',
        type: '',
        data: '',
        owner_id: '',
        ws_id: workspaceId ?? '',
      });
     
      setIsModalActive(false);
     
      const updatedRequirements = await requirementService.getRequirementsByWorkspaceId(workspaceId ?? '');
      setFilteredRequirements(updatedRequirements);
    } catch (error) {
      showError(error as AxiosError, { message: "Erreur lors de la création de l'exigence" });
    }
  };

  const handleRequirementClick = async (index: number) => {
    setActiveRequirement(index === activeRequirement ? null : index);
    setActiveGeneration(null);

    if (index !== activeRequirement) {
      const requirementId = filteredRequirements[index]?.id;
      if (requirementId) {
        loadGenerations(requirementId);
      }
    }
  };

  const handleGenerationClick = (generationId: string) => {
    setActiveGeneration(prev => (prev?.id === generationId ? null : generations.find(gen => gen.id === generationId) || null));
  };

  const handleFilterClick = (status: string | null) => {
    setFilterStatus(filterStatus === status ? null : status);
  };

  const filteredGenerations = filterStatus ? generations.filter(gen => gen.statut === filterStatus) : generations;

  const toggleSelectAllExigences = () => {
    if (selectedExigencesIds.length === requirements.length) {
      setSelectedExigencesIds([]);
    } else {
      const allExigenceIds = requirements.map(req => req.id);
      setSelectedExigencesIds(allExigenceIds);
    }
  };

  const handleGenerateClickForActiveRequirement = async (documentsIds: string[] = []) => {
    if (!workspaceId || activeRequirement === null) {
      showError(new Error(), { message: "Workspace ID ou aucune exigence active sélectionnée" });
      return;
    }

    try {
      // Check if identification is in progress before proceeding
      const isIdentificationBusy = await workspaceService.isIdentificationInProgress(workspaceId);
      if (isIdentificationBusy) {
        chatAnimationMessageSystem.showMessage("Une identification est déjà en cours. Veuillez réessayer plus tard.");
        setIsSimpleGenerateModalActive(false);
        return;
      }

      const updatedRequirements = await workspaceService.postIdentification({
        demandeur_id: 'e793dcbc-68e4-4d7d-883b-4434df9ba180',
        workspace_id: workspaceId,
        exigences: [filteredRequirements[activeRequirement].id],
        documents: documentsIds  // Ajoutez cette ligne
      });
      chatAnimationMessageSystem.showMessage(updatedRequirements.statut);
      chatAnimationMessageSystem.showMessage("Identification démarrée avec succès");
      setIsSimpleGenerateModalActive(false);
      
      // Ajouter cette ligne pour surveiller la fin de l'identification et actualiser les générations
      const requirementId = filteredRequirements[activeRequirement].id;
      watchIdentificationCompletion(requirementId);

      // Rafraîchir le statut du workspace immédiatement après le lancement de l'identification
      refetchWorkspaceStatus();
      
    } catch (error) {
      showError(error as AxiosError, {
        message: "Erreur lors de la génération"
      });
    }
  };

  const handleValidate = async (generation_id: string) => {
    try {
      await generationService.validateGeneration(generation_id);
      chatAnimationMessageSystem.showMessage('Le statut a été modifié en validé');
      if (activeRequirement !== null) {
        const requirementId = filteredRequirements[activeRequirement]?.id;
        if (requirementId) await loadGenerations(requirementId);
        setActiveGeneration(null);
      }
    } catch (error) {
      showError(error as AxiosError, { message: "Une erreur est survenue lors de la validation" });
    }
  };
  const handleDownload = 
  async (
    generation_id: string, 
    formatExport: string = 'csv', 
    type: string = 'testcase',
    NameExport: string,
    formatimplementation: string = 'xray'
  ) => {
    console.log("Téléchargement en cours...",  generation_id);
    try {
      const response = await generationService.downloadGeneration(generation_id, formatExport, type);
      // Process the response and trigger the download
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.target = '_blank';
      const now = new Date();
      const timestamp = now.toISOString().replace(/[-:T]/g, '').slice(2, 14); // YYMMDDHHMMSS format
      const testWord = type.toLowerCase().includes('testcase') ? 'test' : 'tests'; // Determine singular or plural
      const nameFormatted = NameExport
        .substring(0, 60) // first 60 characters
        .replace(/ /g, '_') // Replace spaces with underscores
        .replace(/[^A-Za-z0-9_]/g, ''); // Keep only alphanumeric chars
      a.download = `[${timestamp}][${formatimplementation}][${testWord}]-${type}-${nameFormatted}.${formatExport}`;
      // a.download = `testcase_${generation_id}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      chatAnimationMessageSystem.showMessage('Téléchargement réussi !');
    } catch (error) {
      showError(error as AxiosError, { message: "Une erreur est survenue lors du téléchargement" });
    }
  };
  
  const handleReject = async (generation_id: string) => {
    try {
      await generationService.rejectGeneration(generation_id);
      chatAnimationMessageSystem.showMessage('Le statut a été modifié en rejeté');
      if (activeRequirement !== null) {
        const requirementId = filteredRequirements[activeRequirement]?.id;
        if (requirementId) await loadGenerations(requirementId);
        setActiveGeneration(null);
      }
    } catch (error) {
      showError(error as AxiosError, { message: "Une erreur est survenue lors du rejet" });
    }
  };

  const handleGenerateGlobal = async () => {
    if (!workspaceId || !selectedExigencesIds.length) {
      showError(new Error("Workspace ID ou aucune exigence sélectionnée"));
      return;
    }
    
    try {
      // Check if identification is in progress before proceeding
      const isIdentificationBusy = await workspaceService.isIdentificationInProgress(workspaceId);
      if (isIdentificationBusy) {
        chatAnimationMessageSystem.showMessage("Une identification est déjà en cours. Veuillez réessayer plus tard.");
        setIsGenerateModalActive(false);
        return;
      }
      
      const response = await workspaceService.postIdentification({
        demandeur_id: 'e793dcbc-68e4-4d7d-883b-4434df9ba180',
        workspace_id: workspaceId,
        exigences: selectedExigencesIds,
      });
      
      chatAnimationMessageSystem.showMessage(response.statut);
      chatAnimationMessageSystem.showMessage("Identification démarrée avec succès");
      setIsGenerateModalActive(false);
      setSelectedExigencesIds([]); // Réinitialiser après génération

      // Rafraîchir le statut du workspace immédiatement après le lancement de l'identification
      refetchWorkspaceStatus();

      // Si une exigence est actuellement sélectionnée, on met en place la vérification pour actualiser ses générations
      if (activeRequirement !== null) {
        const requirementId = filteredRequirements[activeRequirement].id;
        watchIdentificationCompletion(requirementId);
      }
      
    } catch (error) {
      showError(error as AxiosError, {
        message: "Erreur lors de l'identification"
      });
    }
  };

  const handleDetailsClick = (requirementId?: number) => {
    setIsDetailsModalActive(true);
  };

  const handleGenerateClick = async () => {
    try {
      const docsResponse = await documentService.listUserDocuments();
      setAvailableDocuments(docsResponse.documents || []);
      setSelectedDocumentIds([]); // reset selection
    } catch (error) {
      console.error("Erreur lors du chargement des documents", error);
      setAvailableDocuments([]);
    }
    setIsSimpleGenerateModalActive(true);
  };

// Fonction réutilisable pour surveiller la fin d'une identification et actualiser les générations
const watchIdentificationCompletion = (requirementId: string) => {
  if (!workspaceId) {
    console.warn("workspaceId est non défini.");
    return null;
  }

  const refreshInterval = setInterval(async () => {
    try {
      // Vérifier si l'identification est toujours en cours
      const stillInProgress = await workspaceService.isIdentificationInProgress(workspaceId);

      if (!stillInProgress) {
        clearInterval(refreshInterval);

        // Actualiser la liste des générations
        const newGenerations = await generationService.getGenerationsByRequirementId(requirementId);
        setGenerations(newGenerations);

        // Rafraîchir le statut du workspace maintenant que l'identification est terminée
        refetchWorkspaceStatus();

        // Notifier la fin d'identification pour que RequirementsContainer rafraîchisse ses boutons
        setIdentificationJustCompleted(prev => prev + 1);

        chatAnimationMessageSystem.showMessage("Identification terminée. Résultats mis à jour.");
      }
    } catch (error) {
      console.error("Erreur lors de la vérification de l'état de l'identification", error);
      clearInterval(refreshInterval);
    }
  }, 3000); // Vérifier toutes les 3 secondes

  // Arrêter l'intervalle après un certain temps pour éviter qu'il tourne indéfiniment
  setTimeout(() => clearInterval(refreshInterval), 10 * 60 * 1000); // 10 minutes maximum

  return refreshInterval;
};

  // Composant pour l'indicateur de statut du workspace
  const WorkspaceStatusIndicator = () => {
    if (statusLoading || workspaceStatus === undefined) {
      return null; // Ou un spinner discret
    }

    const isBusy = workspaceStatus.is_busy;

    // Déterminer la couleur et le texte du badge
    const getBadgeProps = () => {
      if (isBusy) {
        return {
          text: 'Occupé',
          color: '#dc3545'
        };
      } else {
        return {
          text: 'Disponible',
          color: '#28a745'
        };
      }
    };

    const badgeProps = getBadgeProps();

    return (
      <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
        <div style={{
          padding: '4px 8px',
          borderRadius: '12px',
          backgroundColor: badgeProps.color,
          color: 'white',
          fontSize: '11px',
          fontWeight: 'bold',
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          marginRight: '10px'
        }}>
          <span>●</span> {badgeProps.text}
        </div>
      </div>
    );
  };

  // Récupérer les vraies données de la dernière identification
  const lastIdentificationData = workspaceStatus?.last_identification_request || null;

  return (
    <Container className="page-container" fullwidth>
      <BreadcrumbNav workspaceName={workspace.nom} />

      <Box background={'GREY_LIGHT'}>
        <BoxHeader>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <Text><Popover content={workspace.id} direction='right'>{workspace.nom}</Popover> - Détails</Text>
            <WorkspaceStatusIndicator />
          </div>
        </BoxHeader>
        <BoxContent>

          <Banner
            key={bannerKey}
            workspace={workspace}
            setIsModalActive={setIsModalActive}
            setIsGenerateModalActive={setIsGenerateModalActive}
            isModalActive={isModalActive}
            isGenerateModalActive={isGenerateModalActive}
            newRequirement={newRequirement}
            handleInputChange={handleInputChange}
            handleTextareaChange={handleTextareaChange}
            handleSelectChange={handleSelectChange}
            handleCreateRequirement={handleCreateRequirement}
            requirements={requirements}
            selectedExigencesIds={selectedExigencesIds}
            setSelectedExigencesIds={setSelectedExigencesIds}
            handleGenerateGlobal={handleGenerateGlobal}
            toggleSelectAllExigences={toggleSelectAllExigences}
            isIdentificationInProgress={isIdentificationInProgress} // Passer l'état à Banner
            handleDownload={handleDownload}
            lastIdentificationData={lastIdentificationData}
          />

          <RequirementsHeader
            filteredRequirements={filteredRequirements}
            activeRequirement={activeRequirement}
            handleDetailsClick={() => setIsDetailsModalActive(true)}
            handleGenerateClickForActiveRequirement={handleGenerateClickForActiveRequirement}
            isDetailsModalActive={isDetailsModalActive}
            setIsDetailsModalActive={setIsDetailsModalActive}
            isSimpleGenerateModalActive={isSimpleGenerateModalActive}
            setIsSimpleGenerateModalActive={setIsSimpleGenerateModalActive}
            availableDocuments={availableDocuments}
            selectedDocumentIds={selectedDocumentIds}
            setSelectedDocumentIds={setSelectedDocumentIds}
          />

          <TestCasesHeader
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            activeRequirement={activeRequirement}
            generationsLength={generations.length}
            handleSearchChange={handleSearchChange}
            handleDownload={handleDownload}
            filteredRequirements={filteredRequirements}
            TestCasesactiveTabIndex={TestCasesactiveTabIndex}
            setTestCasesActiveTabIndex={setTestCasesActiveTabIndex}
          />
        <div>
          <div className="details-container">
          <div className="top-row">
            <RequirementsContainer 
            filteredRequirements={filteredRequirements}
            handleRequirementClick={handleRequirementClick}
            handleDetailsClick={() => handleDetailsClick(activeRequirement || undefined)}
            handleGenerateClick={() => handleGenerateClick(activeRequirement || undefined)}
            isIdentificationInProgress={isIdentificationInProgress} 
            setTestCasesActiveTabIndex={setTestCasesActiveTabIndex}
            getGenerationsForRequirement={getGenerationsForRequirement}
            identificationCompletedTrigger={identificationJustCompleted}
            />
            <DetailsContainer
              TestCasesactiveTabIndex={TestCasesactiveTabIndex}
              filteredRequirements={filteredRequirements}
              setActiveTabIndex={setActiveTabIndex}
              handleRequirementClick={handleRequirementClick}
              activeRequirement={activeRequirement}
              generations={generations}
              bouchons={bouchons}
              filteredGenerations={filteredGenerations}
              activeTabIndex={activeTabIndex}
              handleFilterClick={handleFilterClick}
              handleGenerationClick={handleGenerationClick}
              activeGeneration={activeGeneration}
              handleValidate={handleValidate}
              handleReject={handleReject}
              handleDownload={handleDownload}
              handleDetailsClick={handleDetailsClick}
              handleGenerateClick={handleGenerateClick}
              isIdentificationInProgress={isIdentificationInProgress}
              
            />           
            </div>
          </div>
        </div>
      </BoxContent>
    </Box>
    <ChatAnimation
        showDialog={false}
        isClickable={true}
        exigenceId={activeGeneration?.exigence_id || ''}
        generationId={activeGeneration?.id || ''}
        workspaceId={workspaceId || ''}
      />
  </Container>
);
};

export default WorkspaceDetailPage;
