"""
identifications basées sur des exigences en langage naturel
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel, <PERSON>, model_validator
from typing_extensions import Self

from .implementations import EcritureTest
from .models import AgentIA, CasDeTestIA, FormatExigence, WorkflowIdentificationState

PROMPT_TEXTE_LIBRE = PromptTemplate(
    template="Tu es un assistant spécialisé dans les tests logiciels. Propose une liste de cas de test en te basant sur l'EXIGENCE ci-dessous de manière à garantir une couverture optimale.\nPour réaliser cette couverture, détermine d'abord les entrées et sorties et base toi sur celles-ci pour déterminer les combinaisons à tester. \n\nRépond uniquement sous forme de tableau avec les colonnes : id du test, objectif du test, entrée(s), sortie attendue(s) , cas passant/ non-passant\n\nEXIGENCE:\n{exigence}",
    input_variables=["exigence"],
)

PROMPT_USER_STORY = PromptTemplate(
    template="User story: {exigence}\n\nDonne des tests pour vérifier les besoins métier\nUtilise la user story pour écrire les tests\nRésultat doit avoir : \n- Action, \n- Données \n- Résultat attendu\n\n",
    input_variables=["exigence"],
)

PROMPT_FORMAT_TEST = PromptTemplate(
    template="""Voici ci-dessous une PROPOSITION de cas de test. réponds dans le demandé.

    PROPOSITION: {tests}

    """,
    input_variables=["tests"],
)


class EcritureDesTests(BaseModel):
    tests: list[EcritureTest]


class WorkflowLangageNaturel(WorkflowIdentificationState):
    identifications_brutes: list[str] | None = Field(None, description="contient l'identification réalisée et non-mise en forme des tests")

    @model_validator(mode="after")
    def est_du_langage_naturel(self) -> Self:
        if self.exigence.type not in [FormatExigence.TEXTE_LIBRE, FormatExigence.USER_STORY]:
            raise ValueError(f"Réception d'un type d'exigence qui n'est pas du langage naturel : {self}")
        return self


class AgentLangageNaturel(AgentIA):
    """
    Agent spécialisé dans l'identification de tests à partir d'exigences écrites

    Pour éviter des pics d'appel API et donc des rate limit, on itère une à une sur les exigences.
    """

    def __init__(self, llm, llm_json):
        super().__init__(llm, llm_json, state_schema=WorkflowIdentificationState)

    def identifier_tests(self, state: WorkflowLangageNaturel):
        """
        génère l'identification des tests à partir d'une liste d'exigences en langage naturel.
        Ce type d'exigence étant libre, on ne fait pas d'écriture des étapes de tests car cela pourrait donner n'importe quoi.

        Par la suite, il est possible que l'on permette de faire des implementations
        """
        identifications = []
        identifications_brutes = []
        exigence = state.exigence

        self.log.info("identification des tests pour une exigence de type %s", exigence.type)

        if exigence.type == FormatExigence.TEXTE_LIBRE:
            promt_template = PROMPT_TEXTE_LIBRE
        elif exigence.type == FormatExigence.USER_STORY:
            promt_template = PROMPT_USER_STORY
        else:
            promt_template = None
            self.log.error("réception d'une exigence non supportée !")

        if promt_template:
            identifier = promt_template | self.llm | StrOutputParser()
            # Spécifier la méthode function_calling
            ecrire = PROMPT_FORMAT_TEST | self.llm.with_structured_output(EcritureDesTests, method="function_calling")

            self.log.info("indentification des cas de test")
            identification_brute = identifier.invoke({"exigence": exigence.data})
            self.log.info("écriture des cas de test dans le format cible")
            self.log.debug("Identification brute:")
            self.log.debug(identification_brute)
            ecriture: EcritureDesTests = ecrire.invoke({"tests": identification_brute})
            self.log.info("%i tests générés", len(ecriture.tests))

            identifications_brutes.append(identification_brute)
            identification = [CasDeTestIA(**test.model_dump(), reference_exigence=exigence.identifiant).model_dump() for test in ecriture.tests]
            identifications.extend(identification)

        return {"tests": identifications, "identifications_brutes": identifications_brutes}

    def build(self):
        self.builder.add_node("identification", self.identifier_tests)
        self.builder.set_entry_point("identification")
        self.builder.set_finish_point("identification")
        return self.builder.compile()
