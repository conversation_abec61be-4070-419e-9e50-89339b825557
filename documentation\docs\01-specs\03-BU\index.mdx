---
title: Besoins Utilisateurs
hide_table_of_contents: true
tags:
  - specs
  - sommaire
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

Dans cette arborescence, vous retrouverez l'ensemble des besoins [utilisateurs](../01-introduction.mdx#utilisateurs-de-lapplication) qui ont été réalisés. Cette page contient également le template qui doit être utilisé dans les tickets Jira lorsqu'il s'agit de spécifier les besoins utilisateur. 

:::info

Ce template est partagé entre l'instance JIRA et la documentation produit:

- Dans la partie JIRA, il s'agit encore d'une expression de besoin en cours de défintion qui doit priorisé puis engagé si valide
- Dans cette documentation, il s'agit de la version finalisé du besoin qui a été developpé et pour lequel des BL et STBL ont étés écrites/mises à jour.

:::

:::note

le nom de la fonctionnalité correspond au titre du ticket. Toutes les autres informations sont renseignés directement dans la description.

:::

## Nom de la fonctionnalité

### Description 

Définir içi la fonctionnalité demandée. Pour cela,  vous pouvez vous appuyer sur les deux templates ci-dessous en fonction de ce qui est le plus approprié.

<Tabs>
<TabItem value="Feature">

:::note

à utiliser lorsqu'il s'agit de décrire une capacité ou un comportement de l'application qui est difficilement rattachable à un utilisateur mais plutôt à des objets/entités métier

:::

> Quand `<contexte/évènement>` (_optionnel_)
> 
> L'application doit `<action>` le(s) `<entité>` `<etat>` 
>
> Afin de `<bénéfice>` 
>
> Pour cela, `<fonctionnalité détaillée>`

Exemple:

> Quand `un utilisateur est inactif depuis 6 mois`
> 
> L'application doit `archiver` les `workspaces` qui `appartiennent à cet utilisateur`
>
> Afin de `ne pas dégrader les performances de l'application en conservant des données inutiles`
> 
> Pour cela, `une tâche planifiée doit être exécutée tout les...`


</TabItem>
<TabItem value="Job Story">

:::note

à utiliser lorsqu'il s'agit de décrire un contexte d'usage d'un utilisateur et comment on lui permettra de "faire le job"

:::

> Quand `<contexte>`, 
>
> j'ai besoin `<fonctionnalité détaillée>` 
>
> pour `<bénéfice>`

Exemple 

> Quand `j'ai plusieurs exigences à importer qui concerne un même projet`
>
> j'ai besoin `d'une fonctionnalité d'import en masses des exigences capable de ...`
>
> pour `gagner en efficacité et ne pas être forcé d'importer les exigences une par une`


</TabItem>
</Tabs>


### Bénéficaires

Mettre içi les utilisateurs/responsables qui bénéficient directement de cette fonctionnalité et qui ont donc intérêt à ce qu'elle soit réalisée.

Si il s'agit d'un groupe plus précis d'utilisateurs, définir ce groupe et sa taille.


### Objectifs

Quels sont les gains espérés vis à vis de cette fonctionnalité. 
Comment allons savoir que ces gains ont été atteint ou non ?


### exigences non-fonctionnels

Si applicable, quelles sont les exigences non-fonctionnelles liées aux besoin.

Ci-dessous les typologies courantes à prendre en compte:

- sécurité
- performances
- capacités d'interactions

La liste complète issue de l'iso 25010.

![Image](@site/static/iso25010.png)


### Métrologie 

Il y a t'il des indicateurs à suivre en lien avec cette fonctionnalité.
Si oui, détailler les différents évênements à mesurer en lien avec ces indicateurs et comment ils seront mis à disposition.


### Cas d'usages

Maintenant que les sections précédentes ont étés définis, écrire dans cette section les différents cas d'usage liés à la fonctionnalité.

Qu'il s'agit des cas fonctionnels (ex: `import en masses d'exigences - formats valides`) 

Aussi bien que des cas non-fonctionnels (ex: `import en masses d'exigences - formats invalides`)
