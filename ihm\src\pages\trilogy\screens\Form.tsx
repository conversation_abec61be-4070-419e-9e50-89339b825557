import { AutoLayout, Container, Input, Select, SelectOption, Textarea } from '@bytel/trilogy-react-ts'
import React from 'react'

export const FormScreen = (): JSX.Element => {
  return (
    <Container>
      <AutoLayout>
        <Select label='placeholder'>
          <SelectOption>L option</SelectOption>
        </Select>

        <Input placeholder='placeholder' status='default'></Input>

        <Input status='danger'></Input>

        <Input status='error'></Input>

        <Textarea label='placeholder' placeholder='placeholder' />

        <Input status='success'></Input>

        <Input status='warning'></Input>
      </AutoLayout>
    </Container>
  )
}
