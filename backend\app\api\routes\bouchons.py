import logging
import uuid

from fastapi import APIRouter

from app.common.bootstrap import APPLICATION
from app.common.exceptions import NotFoundException, get_exception_responses
from app.domains.bouchons.models import BouchonsApiOutput

router = APIRouter(tags=["bouchons"])
logger = logging.getLogger(__name__)


@router.get("/bouchons")
def lister_bouchons(limit: int = 100) -> list[BouchonsApiOutput]:
    logger.info("récupération des %i bouchons")
    return APPLICATION.repos.bouchons.list(limit=limit)


@router.get("/bouchons/{id}", responses=get_exception_responses(NotFoundException))
def consulter_bouchons_service(id: uuid.UUID) -> BouchonsApiOutput:
    return APPLICATION.repos.bouchons.get(id)


@router.get("/exigences/{exigence_id}/bouchons")
def bouchons_specification_openapi(exigence_id: uuid.UUID) -> list[BouchonsApiOutput]:
    return APPLICATION.services.bouchons.recuperer_bouchons_exigence(exigence_id=exigence_id)
