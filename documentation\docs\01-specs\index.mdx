---
title: Spécifications
hide_table_of_contents: true
tags:
  - specs
  - sommaire
---

import {Annoter} from '@site/components'
import {C4model} from '@site/definitions'

export const BRD = <Annoter details="correspond à Business Requirement Document côté anglophone"><PERSON><PERSON><PERSON></Annoter>
export const URD = <Annoter details="correspond à User Requirement Document côté anglophone. C'est de ce type de besoin que l'on va pouvoir décliner des cas d'usage En agile, on peut retrouver le terme de User Story.">Besoin Utilisateur</Annoter>
export const SRD = <Annoter details="correspond à System Reference Document côté anglophone">Besoin Logiciel</Annoter>
export const TDD = <Annoter details="correspond à Technical Design Document côté anglophone. A ne pas confondre avec Test Driven Development">Spécification Techniques Besoin Logiciel</Annoter>


Cette arborescences contient tous les documents liés aux Spécifications de l'application.

On va y retrouver les catégories suivantes:

| Catégorie | Description | Tag | C4 abstraction |
|-|-|-|------|
| {BRD}| Défini ce que les partie prenantes attendent de l'application.<br/>Il n'y a pas de notion de comment l'application y réponds.<br/>On cherche ici à décrire la valeur ajoutée attendue et comment elle va être suivie. | BM | Software System | 
| {URD}| Défini ce que les utilisateurs attendent de l'application.<br/>On cherche ici à définir ce qu'il est possible de faire et les cas d'usage associés. | BU | Software System | 
| {SRD} | Défini comment l'application réponds à un besoin utilisateur exprimé.<br/>On y retrouve donc l'architecture applicative et les reponsabilités de chaque composants répondant au besoin. | BL | Container |
| {TDD} | Pour chaque composant, défini comment celui-ci implémente les besoins logiciels exprimés. On va y retrouver également toute documentation spécifique à l'implémentation choisie |STBL | Component |  

Pour tous les diagrammes qui seront mis en place, on va appliquer la {C4model}.

Plus d'informations [disponibles au lien suivant](https://c4model.com/)

:::note 

revoir l'abstraction BRD/URD (pas sûr que ce soit pertinant de distinguer les stakeholders du public ciblé car il s'agit de besoins métiers dans les deux cas.
Des métiers différents certes, mais des métiers quand même. c'est potentiellement plus pertinent de faire tri par métier via les tags)

:::