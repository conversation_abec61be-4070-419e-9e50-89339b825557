import React from 'react';
import { Box } from '@bytel/trilogy-react-ts';
import { WorkspaceOutput } from '../../types/workspace';

interface WorkspaceTableProps {
  workspaces: WorkspaceOutput[];
  isLoading: boolean;
}

const WorkspaceTable: React.FC<WorkspaceTableProps> = ({ workspaces, isLoading }) => {
  if (isLoading) {
    return (
      <Box className="d-flex justify-content-center p-3">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </Box>
    );
  }

  if (workspaces.length === 0) {
    return (
      <Box className="p-2">
        <p>Aucun workspace trouvé</p>
      </Box>
    );
  }

  return (
    <div className="table-responsive div-table-responsive-admin">
      <table className="table table-admin">
        <thead>
          <tr>
            
            <th>Nom</th>
            <th>Description</th>
            <th>Date de création</th>
          </tr>
        </thead>
        <tbody>
          {workspaces.map((workspace) => (
            <tr >
              
              <td>{workspace.nom}</td>
              <td>{workspace.description}</td>
              <td>{new Date(workspace.date_creation).toLocaleDateString()}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default WorkspaceTable;