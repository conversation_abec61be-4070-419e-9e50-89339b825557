import * as React from "react";
import { Severity, SnackBarHandler } from "helpers/snackbar/snackbar";
import { useLogger } from "./logger";

export type TypeError = Error & { response?: { status?: number } };

type TypeSnackbarState = {
  error?: TypeError;
  message?: string;
  severity: Severity;
  duration: number | "infinite";
  logToPrisme?: boolean;
};

export type TypeSnackbarContext = TypeSnackbarState & {
  show: (state: Partial<TypeSnackbarState>) => void;
  hide: () => void;
  showInfo: (message: string, state?: Omit<Partial<TypeSnackbarState>, "message" | "severity">) => void;
  showWarning: (message: string, state?: Omit<Partial<TypeSnackbarState>, "message" | "severity">) => void;
  showError: (error: TypeError, state?: Omit<Partial<TypeSnackbarState>, "error">) => void;
  catchError: (message: string) => (error: TypeError) => void;
};

export const SnackbarContext = React.createContext<TypeSnackbarContext | null>(null);

function errorSeverity(error: TypeError) {
  return error?.response?.status !== 401 ? "error" : "hidden";
}

const DEFAULT_STATE: TypeSnackbarState = {
  severity: "hidden",
  duration: 10000,
};
export const SnackbarProvider: React.FC<
  React.PropsWithChildren<{
    mockSnackbar?: TypeSnackbarContext;
    showStackTrace: boolean;
  }>
> = ({ children, mockSnackbar, showStackTrace }) => {
  const [state, setState] = React.useState<TypeSnackbarState>(DEFAULT_STATE);
  const logger = useLogger();

  let snackbarContext: TypeSnackbarContext;
  if (mockSnackbar != null) {
    snackbarContext = mockSnackbar;
  } else {
    const show = (state: Partial<TypeSnackbarState>) => {
      const severity = state.error ? errorSeverity(state.error) : "info";
      const newState: TypeSnackbarState & { message: string } = {
        ...DEFAULT_STATE,
        severity,
        message: state.error?.message || "Une erreur est survenue",
        ...state,
      };
      if (newState.logToPrisme) {
        logger.logToPrisme(newState.message, {
          erreur: newState.error,
        });
      }
      setState(newState);
    };

    snackbarContext = {
      ...state,
      hide: () => setState(DEFAULT_STATE),
      show,
      showError: (error: TypeError, state?: Omit<Partial<TypeSnackbarState>, "error">) => show({ error, ...state }),
      catchError: (message: string) => (error: TypeError) => {
        show({
          error,
          message,
        });
      },
      showInfo: (message: string, state?: Omit<Partial<TypeSnackbarState>, "message" | "severity">) => show({ message, ...state }),
      showWarning: (message: string, state?: Omit<Partial<TypeSnackbarState>, "message" | "severity">) => show({ message, severity: "warning", ...state }),
    };
  }

  return (
    <SnackbarContext.Provider value={snackbarContext}>
      {children}
      <SnackBarHandler showStackTrace={showStackTrace} />
    </SnackbarContext.Provider>
  );
};

export const useSnackbar = () => {
  return React.useContext(SnackbarContext) as TypeSnackbarContext;
};
