import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TextareaChangeEvent } from '@bytel/trilogy-react-ts/lib/components/textarea/TextareaProps';
import { Card, CardContent, Button, Input, InputChangeEvent, Textarea, Title, TitleLevels, Modal, ModalFooter, ModalTitle, Divider, ModalMarkup, Text, Container } from "@bytel/trilogy-react-ts";
import { useAPIClient } from 'providers/api';
import { useSnackbar } from 'providers/snackbar';
import { useQuery } from 'react-query';
import { AxiosError } from 'axios';
import ChatAnimation, { chatAnimationMessageSystem } from 'layout/ChatAnimation';
import './workspaceAll.css';

const WorkspacesPage: React.FC = () => {
  const [newWorkspace, setNewWorkspace] = useState({
    id: '',
    nom: '',
    description: '',
  });
  const [isModalActive, setIsModalActive] = useState<boolean>(false);
  const { showError } = useSnackbar();
  const { workspaceService } = useAPIClient();
  const navigate = useNavigate();
  chatAnimationMessageSystem.showMessage('Choisissez un espace de travail ou créez-en un.');


  const { data: workspaces, error, isLoading, refetch } = useQuery(
    'workspaces',
    () => workspaceService.searchWorkspaces(),
    {
      initialData: [],
    }
  );

  const handleCreateWorkspace = async () => {
    try {
      await workspaceService.createWorkspace({
        nom: newWorkspace.nom,
        description: newWorkspace.description
      });
      setNewWorkspace({ id: '', nom: '', description: '' });
      closeModal();
      
      // Utiliser notre nouveau système au lieu de showInfo
      chatAnimationMessageSystem.showMessage('Espace de travail créé avec succès');
      
      refetch();
    } catch (e) {
      showError(e as AxiosError, { message: "Erreur lors de la création de l'espace de travail" });
    }
  };

  const handleInputChange = (event: InputChangeEvent) => {
    const { inputValue, inputName } = event;
    setNewWorkspace((prevState) => ({
      ...prevState,
      [inputName]: inputValue,
    }));
  };

  const handleTextareaChange = (event: TextareaChangeEvent) => {
    const { textareaValue, textareaName } = event;
    setNewWorkspace((prevState) => ({
      ...prevState,
      [textareaName]: textareaValue,
    }));
  };

  const handleCardClick = (id: string) => {
    navigate(`/workspace/${id}`);
  };

  useEffect(() => {
    if (error) {
      showError(error as AxiosError, { message: "Erreur lors de la récupération des espaces de travail" });
    }
  }, [error, showError]);

  // Fonction pour tronquer une chaîne de texte trop longue
  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  const closeModal = () => {
    setIsModalActive(false);
  };

  return (
    <div className='workspaces-page'>
      <div className='head'>
      <div className="headline-container">
        <Title level={TitleLevels.ONE} className="headline">
          Mes espaces de travail
        </Title>
        <Button variant="PRIMARY" onClick={() => setIsModalActive(true)} className='create-button'>
          Créer
        </Button>
        <Modal
          active={isModalActive}
          triggerMarkup={ModalMarkup.BUTTON}
          closeIcon
          onClose={closeModal}
          ctaContent="Créer"
        >
          <ModalTitle>Créer un nouvel espace</ModalTitle>
          <Divider />
          <div className="create-button-modal">
            <Input
              placeholder="Titre de l'espace de travail"
              type="text"
              name="nom"
              value={newWorkspace.nom}
              onChange={handleInputChange}
            />
            <Textarea
              placeholder="Description (facultatif)"
              name="description"
              value={newWorkspace.description}
              onChange={handleTextareaChange}
            />
          </div>
          <ModalFooter className="modal-footer">
            <Button variant="SECONDARY" onClick={closeModal}>
              Annuler
            </Button>
            <Button variant="PRIMARY" onClick={handleCreateWorkspace}>
              Créer
            </Button>
          </ModalFooter>
        </Modal>
      </div>

      </div>

      <Container className='workspaces-container' fullwidth>
        {isLoading ? (
          <p>Chargement des espaces de travail...</p>
        ) : (
          workspaces && workspaces.map((workspace) => (
            <Card key={workspace.id} className='workspace-card' onClick={() => handleCardClick(workspace.id)}>
              <CardContent className='p-4'>
                <Title level={TitleLevels.THREE} className='headline'>
                  {workspace.nom}
                </Title>
                <Text>{truncateText(workspace.description, 100)}</Text>
                <div className='hover-content'>
                  <Text>
                    Date de création : {workspace.date_creation ? new Date(workspace.date_creation).toISOString().slice(0, 10) : ''}
                  </Text>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </Container>
      <ChatAnimation
        showDialog={false}
        exigenceId={''}
        generationId={''}
        workspaceId={''}
      />
    </div>
  );
};

export default WorkspacesPage;
