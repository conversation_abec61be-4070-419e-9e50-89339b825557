import { test } from "@playwright/test";
import { authenticate } from "../../auth";
import { Individu, ResultatsRecherche } from "services/interfaces/generated/HELLOWORLD-FORMATION/rechercherPersonnes.ts";

test.beforeEach(({ page }) => authenticate(page));

test("Recherche d'une personne", async ({ page }) => {
  const jamesBond = {
    personne: [
      {
        nom: "<PERSON>",
        prenom: "<PERSON>",
        login: "jbond",
        _type: "INDIVIDU",
        idPersonneUnique: "123",
      },
    ],
  } satisfies ResultatsRecherche & { personne: Individu[] };
  await page.route(/\/api\/personnes.+/, (route, request) => {
    if (new URL(request.url()).searchParams.get("nom")?.toLowerCase().includes("bond")) {
      return route.fulfill({ json: jamesBond });
    }
    return route.fulfill({ json: [] });
  });
  await page.goto("/");

  await page.getByRole("textbox").click();
  await page.getByRole("textbox").fill("Bond");
  await page.getByRole("button", { name: "Rechercher" }).click();

  await page.getByText("Bond").click();

  await page.waitForURL("**/personnes/123", { waitUntil: "domcontentloaded" });
});
