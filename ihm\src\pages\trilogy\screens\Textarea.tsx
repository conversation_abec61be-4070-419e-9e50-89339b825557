import React from 'react'
import { Section, Divider, Textarea } from '@bytel/trilogy-react-ts'

export const TextareaScreen = (): JSX.Element => {
  return (
    <Section>
      <Textarea placeholder='placeholder' label='Label dynamique fjdhf jfjdhk f' />
      <Divider />

      <Textarea placeholder='placeholder' label='Label non dynamique jdsghf sdf' dynamicPlaceholder={false} />
      <Divider />

      <Textarea placeholder='placeholder' label='Label  fhdjh f' dynamicPlaceholder={false} maxLength={150} />
      <Divider />
      <Textarea placeholder='Avec icone disabled' label='Label' disabled iconName='tri-advisor' />

      <Textarea placeholder='Avec icone' label='Avec compteur' maxLength={150} iconName='tri-advisor' />
      <Divider />

      <Textarea
        placeholder='Avec status icon'
        label='Label fdjhfj hfjdh fdhjkjq fdhfjh qsjfkh kjfhjq fhkdk fd'
        maxLength={150}
        iconName='tri-advisor'
        statusIconName='tri-check-circle'
        status='success'
      />
      <Divider />

      <Textarea
        placeholder='Avec status icon erreur'
        label='Label lorefdjhf sfjkd jfkjkfh jkdhf kjsh kfhjshdfjdhjhdq fjhdjk hfjkdh fhj'
        iconName='tri-advisor'
        statusIconName='tri-exclamation-circle'
        status='error'
        help='Ceci est un message derreur'
        typo='has-text-error'
      />
    </Section>
  )
}
