"""
Contient l'ensemble des instructions pour l'écriture des bouchons dans un format supporté.
"""

from .models import FormatBouchon

INSTRUCTIONS_FORMATS_BOUCHONS = {
    FormatBouchon.VF: """Pour chacun des exemples trouvés, créé le fichier de configuration d'un mock selon ce format :
            Le format sera de tableau JSON.
            Le json devra contenir un objet vf_service qui contiendra les éléments pour matcher l'appel.
            A l'intérieur de cet objet vf_service, nous voulons :
            - vf_url : qui contiendra l'URL du bouchon
            - vf_method : qui contiendra la méthode http associée
            - vf_input_body : qui contiendra le contenu du body (si nécessaire (par exemple, pas de body pour une méthode GET))
            Le json devra également contenir un objet vf_reponse qui contiendra la réponse du bouchon pour cet appel :
            - vf_status_code : qui contiendra le code http de retour
            - vf_output_body : qui contiendra le body de sortie pour cette réponse
            
            format du json de sortie : [{{"vf_service": {{"vf_url": "url", "vf_method": "GET", "vf_input_body": "body"}}, "vf_reponse": {{"vf_status_code": le code, "vf_output_body": "body"}}]
            """,
    FormatBouchon.WIREMOCK: """ Pour tout ce que tu as identifié, peux-tu créer un mock JSON distinct pour chaque combinaison possible de requêtes et de réponses de l'API, en veillant à inclure
    les données spécifiques décrites dans la documentation (exemples, valeurs attendues, contraintes) pour rendre chaque mock réaliste et exploitable dans Wiremock.
    ** Attention tous les mocks doivent pouvoir être lancés en même temps dans Wiremock il ne faut donc pas créer de doublons sur la partie "request" du json **

    format du json de sortie : [{{mock1}}, {{mock2}}, {{mock3}}]
    """,
}
