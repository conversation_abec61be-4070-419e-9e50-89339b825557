import AxiosPrismeLogger from "@bytel/prisme-logger/lib/axios";
import { BQuery } from "@bytel/query";
import { TypeConfig } from "helpers/config.ts";
import API from "services/api.ts";
import { mockObject } from "tests/test-utils.tsx";
import { vi } from "vitest";
import { TypeSnackbarContext } from "providers/snackbar.tsx";
import * as reactRouter from "react-router-dom";

export const mockConfig = {} as TypeConfig;

// Mock Navigation
export const mockUseNavigate = vi.fn();
vi.mock("react-router-dom", async (importOriginal) => {
  const actual = await importOriginal<typeof reactRouter>();
  return {
    ...actual,
    useNavigate: () => mockUseNavigate,
  };
});
export const mockSnackbar: TypeSnackbarContext = {
  duration: "infinite",
  severity: "info",
  catchError: vi.fn(),
  show: vi.fn(),
  showInfo: vi.fn(),
  showWarning: vi.fn(),
  showError: vi.fn(),
  hide: vi.fn(),
};

// Mock LOGGER
export const mockLogger: AxiosPrismeLogger = {
  wrapAxiosInstance: vi.fn(),
  logToPrisme: vi.fn(),
  forcerEnvoiQueueMessages: vi.fn(),
  setTrackerId: vi.fn(),
} as unknown as AxiosPrismeLogger;

// Mock API
export const mockAPI = mockObject(new API({} as BQuery));
