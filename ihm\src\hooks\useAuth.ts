import { useQuery } from 'react-query';
import { useAPIClient } from 'providers/api';

export const useAuth = () => {
  const { adminService } = useAPIClient();

  const { data: user, isLoading, error } = useQuery(
    'currentUser',
    () => adminService.getCurrentUser(),
    {
      staleTime: 1000 * 60 * 5, // Garder les données fraîches pendant 5 minutes
      refetchOnWindowFocus: false,
    }
  );

  return {
    user,
    isLoading,
    error,
    isAdmin: user?.admin === true,
  };
};