export interface DemandeIdentificationInput {
  document_source?: string;
  demandeur_id?: string;
  workspace_id: string;
  exigences: string[];
  documents?: string[];
}

export interface DemandeIdentificationFiltre {
  document_source?: string;
  demandeur_id?: string;
  workspace_id?: string;
  statuts: string[];
  exigences?: string[];
  documents?: string[];
}

export interface DemandeIdentificationOutput {
  id: string;
  workspace_id: string;
  workspace_name: string;
  date_creation: string;
  date_demande: string;
  date_debut: string;
  date_fin: string;
  statut: string;
  demandeur_id: string;
  demandeur_name: string;
  document_source?: string;
  resultat?: string;
  stats?: {
    models?: any[];
    total_tokens?: string;
    prompt_tokens?: string;
    completion_tokens?: string;
    reasoning_tokens?: string;
    total_cost?: string;
  };
  entrants?: {
    exigences?: any[];
  };
}
