export interface WorkspaceBase {
  nom: string;
  description: string;
}

export interface WorkspaceOutput extends WorkspaceBase {
  id: string;
  date_creation: string;
  owner_id: string;
}

export interface WorkspaceCreateInput extends WorkspaceBase {
  // Champs supplémentaires pour la création si nécessaire
}

export interface WorkspaceUpdateInput {
  id: string;
  nom?: string;
  description?: string;
}