"""
injection des dépendences/middleware utilisé par l'API web qui ne sont pas spécifique à la sécurité ou l'observabilité
"""

import logging

from fastapi import Request
from fastapi.routing import APIRoute
from fastapi.utils import generate_unique_id

from app.common.exceptions import BaseAPIException

LOGGER = logging.getLogger(__name__)


async def exceptions_handler(request: Request, call_next):
    """
    middleware rajouté pour pouvoir gérer les exception remontés et les transformer à la volée en réponse JSON
    """
    try:
        return await call_next(request)
    except Exception as error:
        if isinstance(error, BaseAPIException):
            # est une erreur prévue (4XX, 5XX) donc on renvoit au client la réponse et on la log au niveau erreur
            LOGGER.error(error)
            return error.response()
        # pas une erreur prévue donc on log avec stacktrace et renvoie une erreur 500
        LOGGER.fatal("erreur non-gérée rencontrée: %s", error, stack_info=True)
        return BaseAPIException(message=f"une erreur non-gérée est survenue: {error}", status_code=500).response()


def custom_operation_ids_generation(route: APIRoute):
    """
    modification des operationId de la specification openapi générée
    de manière à être en camelCase qui est la convention côté typecsript
    """
    if route.include_in_schema:
        capitalized = "".join(chunk.capitalize() for chunk in route.name.lower().split("_"))

        return f"{route.name[0]}{capitalized[1:]}"
    else:
        generate_unique_id(route=route)
