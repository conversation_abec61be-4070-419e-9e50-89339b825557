import AxiosPrismeLogger from "@bytel/prisme-logger/lib/axios";

import * as React from "react";
import { useConfig } from "./config";
import { useAuth } from "@bytel/react-oauth2";

export const LoggerContext = React.createContext<AxiosPrismeLogger | null>(null);

export const LoggerProvider: React.FC<React.PropsWithChildren<{ mockLogger?: AxiosPrismeLogger }>> = ({ children, mockLogger }) => {
  const config = useConfig();
  const { userInfo } = useAuth();

  const LOGGER = React.useMemo(() => {
    if (mockLogger) return mockLogger;

    if (config.PRISME_ENDPOINT) {
      return new AxiosPrismeLogger({
        environnement: config.ENVIRONNEMENT,
        st: config.ST,
        urlEndpoint: config.PRISME_ENDPOINT,
        composant: "ihm",
        version: config.VERSION,
        disableInput: true,
        userInfoProvider: () => ({
          login: userInfo?.login,
          habilit: userInfo?.roles?.join(","),
        }),
      });
    } else {
      return null;
    }
  }, [config, mockLogger, userInfo]);

  return <LoggerContext.Provider value={LOGGER}>{children}</LoggerContext.Provider>;
};

export const useLogger = () => {
  return React.useContext(LoggerContext) as AxiosPrismeLogger;
};
