{"name": "ihm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "start": "vite", "preview": "vite preview --port 5173", "postinstall": "npm run generate", "prebuild": "npm run generate", "generate": "stts -f ../catalogue-interfaces -o src/services/interfaces/generated -e ts", "openapi-ts-client": "openapi-ts", "test": "echo a corriger", "coverage": "npm test -- --coverage", "presonar": "npm run coverage", "sonar": "sonar-scanner -Dsonar.projectKey=CATIA-ihm -Dsonar.sources=src -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info -Dsonar.exclusions=**/*.spec.tsx,**/*.test.tsx", "lint:fix": "eslint --fix .", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "e2e:codegen": "playwright codegen http://localhost:5173", "e2e:dev": "vite --config e2e/vite-e2e.config.ts", "e2e:preview": "vite build --config e2e/vite-e2e.config.ts && vite preview --port 5173"}, "dependencies": {"@bytel/markdown-trilogy": "^1.0.0", "@bytel/prisme-logger": "^2.1.4", "@bytel/query": "^2.2.1", "@bytel/react-oauth2": "^3.3.0", "@bytel/trilogy-react-ts": "^3.9.7", "axios": "^1.7.5", "gsap": "^3.12.7", "json5": "^2.2.3", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "^18.3.1", "react-code-blocks": "^0.1.6", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.26.1", "styled-components": "^6.1.19", "swagger-ui-react": "^5.21.0"}, "devDependencies": {"@bytel/swagger-to-ts": "^6.2.0", "@eslint/js": "^9.9.1", "@playwright/test": "^1.46.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.5.0", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "@types/styled-components": "^5.1.34", "@types/swagger-ui-react": "^5.18.0", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^25.0.0", "nyc": "^17.0.0", "playwright-test-coverage": "^1.2.12", "prettier": "^3.3.3", "sass": "^1.77.8", "sonar-scanner": "^3.1.0", "typescript": "^5.5.4", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-multiple-assets": "^1.3.1", "vite-plugin-istanbul": "^6.0.2", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.0.5"}}