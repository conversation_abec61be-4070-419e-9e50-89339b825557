import React from 'react';
import { Title, TitleL<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ModalMarkup, View } from '@bytel/trilogy-react-ts';
import { Requirement } from 'services/interfaces/requirementInterfaces';
import { DocumentOutput } from 'types/document';
import SwaggerUI from "swagger-ui-react";
import "swagger-ui-react/swagger-ui.css";
import { markdownTrilogy } from "@bytel/markdown-trilogy";
import DocumentTreeSelector from './DocumentTreeSelector'; // Importer le nouveau composant

interface RequirementsHeaderProps {
  filteredRequirements: Requirement[];
  activeRequirement: number | null;
  handleGenerateClickForActiveRequirement: (documentIds?: string[]) => void;
  isDetailsModalActive: boolean;
  setIsDetailsModalActive: React.Dispatch<React.SetStateAction<boolean>>;
  isSimpleGenerateModalActive: boolean;
  setIsSimpleGenerateModalActive: React.Dispatch<React.SetStateAction<boolean>>;
  availableDocuments: DocumentOutput[]; // Typage plus précis
  selectedDocumentIds: string[];
  setSelectedDocumentIds: React.Dispatch<React.SetStateAction<string[]>>;
}

const RequirementsHeader: React.FC<RequirementsHeaderProps> = ({
  filteredRequirements, 
  activeRequirement, 
  handleGenerateClickForActiveRequirement, 
  isDetailsModalActive, 
  setIsDetailsModalActive, 
  isSimpleGenerateModalActive, 
  setIsSimpleGenerateModalActive,
  availableDocuments,
  selectedDocumentIds,
  setSelectedDocumentIds
}) => (
  <div className="requirements-header">
    {activeRequirement !== null && filteredRequirements[activeRequirement] && (
      <div>
        {/* Modale pour les Détails de l'Exigence */}
        <Modal 
          className='modal-large' 
          active={isDetailsModalActive} 
          triggerMarkup={ModalMarkup.BUTTON} 
          closeIcon 
          onClose={() => setIsDetailsModalActive(false)} 
          ctaContent="Fermer"
        >
          <Title level={'THREE'}>{filteredRequirements[activeRequirement].nom}</Title>
          {filteredRequirements[activeRequirement] && (
            <div className="requirement-details">
              <div dangerouslySetInnerHTML={{ __html: markdownTrilogy(filteredRequirements[activeRequirement].description) }}></div>
              <Divider />
              <div className="description-exigence" >
                {filteredRequirements[activeRequirement].type === "OpenAPI" ? (
                  <SwaggerUI spec={filteredRequirements[activeRequirement].data} />
                ) : (
                  <View className='requirements-details-content' style={{ overflowY: 'hidden', overflowX: 'hidden', textAlign: "left" }}>
                    <div dangerouslySetInnerHTML={{ __html: markdownTrilogy(filteredRequirements[activeRequirement].data) }}></div>
                  </View>
                )}
              </div>
            </div>
          )}
        </Modal>

        {/* Modale pour la Génération Unitaire avec DocumentTreeSelector */}
        <Modal 
          className='modal-large'
          active={isSimpleGenerateModalActive} 
          triggerMarkup={ModalMarkup.BUTTON} 
          closeIcon 
          onClose={() => setIsSimpleGenerateModalActive(false)} 
          ctaContent="Confirmer"
          size="large"
        >
          <ModalTitle>Génération des cas de test</ModalTitle>
          <Divider />
          <Text>Sélectionnez un document à inclure dans la génération (optionnel) :</Text>
          
          <div >
            <DocumentTreeSelector
              documents={availableDocuments}
              selectedDocumentIds={selectedDocumentIds}
              onSelectionChange={setSelectedDocumentIds}
            />
          </div>
          
          <ModalFooter className="modal-footer">
            <Button variant='SECONDARY' onClick={() => setIsSimpleGenerateModalActive(false)}>
              Annuler
            </Button>
            <Button 
              variant='PRIMARY' 
              onClick={() => handleGenerateClickForActiveRequirement(selectedDocumentIds)}
              // disabled={selectedDocumentIds.length === 0}
            >
              Confirmer{/*selectedDocumentIds.length > 0 ? ` (${selectedDocumentIds[0]})` : ''*/}
            </Button>
          </ModalFooter>
        </Modal>
      </div>
    )}
  </div>
);

export default RequirementsHeader;
