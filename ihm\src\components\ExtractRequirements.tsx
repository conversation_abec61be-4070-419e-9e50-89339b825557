import React from "react";
import { Button, Input, Text, Modal, ModalMarkup, Alert, Table, TableTh, TableBody, View, Columns, ColumnsItem, Link, TableHead, TableTr, Select, SelectOption, Fab, Icon } from "@bytel/trilogy-react-ts";
import { useAPIClient } from "providers/api";
import { ExtractedDocUnique, ExtractedRequirement, ExtractionError } from "services/interfaces/requirementInterfaces";
import { useSnackbar } from "providers/snackbar";
import { AxiosError } from "axios";

import { markdownTrilogy } from "@bytel/markdown-trilogy"

const ExtractionErrorsModal: React.FC<{ erreurs: ExtractionError[], triggerContent: string }> = (props) => {

  return (
    <Modal
      triggerMarkup={ModalMarkup.A}
      title="Erreurs extraction documentation unique"
      triggerContent={props.triggerContent}
      closeIcon
      className='modal-large'
      fullwidth
    >
      <View>
        {props.erreurs.map((erreur) => <Text>Erreur sur <Link blank href={erreur.url}>la page confluence {erreur.id}</Link> :<br></br> {erreur.message}</Text>)}
      </View>
    </Modal>
  )
}


const RequirementsDetailsModal: React.FC<{ titre: string, markdownContent: string, index: string }> = (props) => {

  return (
    <div className={`exigence-${props.index}-details`}>
      <Modal
        panel
        triggerMarkup={ModalMarkup.A}
        title={props.titre}
        triggerContent={<Icon name="tri-top-news" size="small" />}
        closeIcon
        className='modal-large'
        fullwidth
        key={props.index}
      >
        <View>
          <div dangerouslySetInnerHTML={{ __html: markdownTrilogy(props.markdownContent) }}></div>
        </View>
      </Modal>
    </div>
  )
}

const RequirementsSelection: React.FC<{ exigences: ExtractedRequirement[] }> = (selection) => {


  function exigenceAsTableRow(exigence: ExtractedRequirement, index: number) {
    return (

      <TableTr key={index} className={`exigence-${index}`}>
        <TableTh className={`exigence-${index}-porteur`}>{exigence.porteur}</TableTh>
        <TableTh className={`exigence-${index}-titre`}>{exigence.nom}</TableTh>
        <TableTh className={`exigence-${index}-lien`}>
          <Columns verticalCentered centered>
            <ColumnsItem>
              <RequirementsDetailsModal titre={exigence.nom} markdownContent={exigence.data} index={index} />
            </ColumnsItem>
            <ColumnsItem>
              <Link href={exigence.metadonnees.source.url} blank iconName="tri-link" ></Link>
            </ColumnsItem>
          </Columns>
        </TableTh>
      </TableTr>
    )
  }

  return (
    <View style={{ height: '600px', overflowY: 'scroll', overflowX: 'hidden' }}>
      <Table fullwidth>
        <TableHead>
          <TableTh>Porteur</TableTh>
          <TableTh>Exigence</TableTh>
          <TableTh>Actions</TableTh>
        </TableHead>
        <TableBody className="exigences-extraites">
          {selection.exigences.map((exigence, index) => exigenceAsTableRow(exigence, index))}
        </TableBody>
      </Table>
    </View>
  )
}

interface ExtractRequirementsProps {
  currentWorkspace: string;
}


export const ExtractRequirementsForm: React.FC<ExtractRequirementsProps> = (props) => {

  const currentWorkspace = props.currentWorkspace;
  const [pageId, setPageId] = React.useState("");
  const [disabled, setDisabled] = React.useState(true)
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [extractedRequirements, setExtractedRequirements] = React.useState<ExtractedDocUnique | null>(null);
  const [porteurSelection, setPorteurSelection] = React.useState<string[]>([]);
  const [selectedPorteur, setSelectedPorteur] = React.useState<string | undefined>(undefined)
  const [selectedRequirements, setSelectedRequirements] = React.useState<ExtractedRequirement[]>([]);
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);

  const { showError } = useSnackbar();
  //TODO: voir pour utiliser la mascote (mais qui doit être basculée en provider pour que ce soit viable)
  const { showInfo } = useSnackbar();

  const { requirementService } = useAPIClient()

  function formatPageId(value: String) {
    const tmpValue = value.replace(',', '.').replace(/[^0-9.]/g, '')
    const digits = tmpValue.split('.').filter((_, i) => i <= 1)
    let rightDigits = digits[1] || ''
    if (rightDigits.length > 2) rightDigits = rightDigits.substring(0, 2)
    const finalValue = digits.join('.')
    // les identifiant confluence sont sur 9 chiffres à priori et ce dès 2015
    if (finalValue.length > 8) { setDisabled(false) } else { setDisabled(true) }
    return finalValue
  }

  function filterRequirements(selectedPorteur: string | undefined) {
    if (extractedRequirements) {

      if (selectedPorteur === undefined) {
        setSelectedRequirements(extractedRequirements.exigences)
      } else {
        setSelectedRequirements(extractedRequirements.exigences.filter((value) => value.porteur == selectedPorteur))
      }
      setSelectedPorteur(selectedPorteur)
    }
  }

  const extractRequirements = async () => {
    //reset de l'état général du composant avant de lancer une nouvelle extraction
    setExtractedRequirements(null)
    setSelectedRequirements([])
    setSelectedPorteur(undefined)
    setErrorMessage(null)
    setIsLoading(true)
    try {
      const extraction = await requirementService.extractDocUnique(pageId)
      setExtractedRequirements(extraction)
      setSelectedRequirements(extraction.exigences)
      const porteurs = extraction.exigences.map((exigence) => exigence.porteur).sort()
      setPorteurSelection([... new Set(porteurs)])
    } catch (error: any) {
      showError(error as AxiosError, {
        message: `${error.response?.data?.message}`,
      });
      setErrorMessage(`${error.response?.data?.message}`)
      setExtractedRequirements(null)
    } finally {
      setIsLoading(false)
    }
  }

  const createRequirements = async () => {
    setIsLoading(true)
    console.log(`création des exigences sur le workspace: ${currentWorkspace}`)
    const payload = JSON.stringify(selectedRequirements)
    console.debug(payload)
    try {

      for (var newRequirement of selectedRequirements) {
        console.log(`création de l'exigence: ${newRequirement.nom}`)
        await requirementService.createRequirement(
          newRequirement.nom,
          newRequirement.description,
          newRequirement.type,
          newRequirement.data,
          '',
          currentWorkspace
        );
      }
      showInfo("Exigences crées avec succès")
    } catch (error: any) {
      showError(error as AxiosError, {
        message: `${error.response?.data?.message}`,
      });
    } finally {
      setIsLoading(false)
    }
    //TODO: voir pour utiliser une api backend faisant du bulk
  }



  return (
    <>
      <Columns>
        <ColumnsItem>
          <Input
            onChange={(e) => setPageId(formatPageId(e.inputValue))}
            value={pageId}
            forceControl
            type="search"
            hovered
            hasIcon
            loading={isLoading}
            placeholder="id page confluence"
            help="Renseignez l'identifiant de la page confluence contenant votre documentation unique"
          />
        </ColumnsItem>
        <ColumnsItem narrow>
          <Button className="is-secondary" onClick={() => extractRequirements()} loading={isLoading} disabled={disabled}>Extraire Exigences</Button>
        </ColumnsItem>
        <ColumnsItem>
          <Select disabled={selectedRequirements.length == 0} selected={selectedPorteur}
            onChange={(e) => {
              const value: string | undefined =
                typeof e === 'string' || typeof e === 'number'
                  ? String(e)
                  : e.selectValue
                    ? String(e.selectValue)
                    : undefined
              filterRequirements(value)
            }}>
            <SelectOption value={undefined} label="tout sélectionner" />
            {porteurSelection.map((porteur) => <SelectOption value={porteur}>{porteur}</SelectOption>)}
          </Select>
        </ColumnsItem>
        <ColumnsItem narrow>
          <Button variant="PRIMARY" onClick={() => createRequirements()} loading={isLoading} disabled={selectedRequirements.length == 0}>Ajouter {selectedRequirements.length} Exigences</Button>
        </ColumnsItem>
      </Columns>
      {errorMessage !== null && <Alert display alert="ERROR" title="Extraction en échec" description={errorMessage}></Alert>}
      {extractedRequirements !== null && extractedRequirements.erreurs.length == 0 &&
        <Alert display alert="SUCCESS"
          title={<>Extraction de <Link blank href={extractedRequirements.url}>{extractedRequirements.nom}</Link> réussie</>}
          description={<Text><b>{extractedRequirements?.exigences.length} exigences</b> ont étés extraites à partir des <b>{extractedRequirements?.pages_extraites} pages</b> parcourues</Text>}
        />
      }
      {extractedRequirements !== null && extractedRequirements.erreurs.length > 0 &&
        <Alert display alert="WARNING"
          title={<>Extraction partielle de <Link blank href={extractedRequirements.url}>{extractedRequirements.nom}</Link></>}
          description={
            <Text><b>{extractedRequirements?.exigences.length} exigences</b> ont étés extraites à partir des <b>{extractedRequirements?.pages_extraites} pages</b> parcourues. Cependant, <ExtractionErrorsModal erreurs={extractedRequirements.erreurs} triggerContent={`${extractedRequirements.erreurs.length} erreurs`} /> ont étés rencontrés durant l'extraction.</Text>
          }
        />
      }
      {extractedRequirements !== null && extractedRequirements.exigences.length > 0 && <RequirementsSelection exigences={selectedRequirements} />}
    </>
  )
}


export const ExtractRequirementsModal: React.FC<ExtractRequirementsProps> = (props) => {

  return (
    <Modal
      panel
      // onOpen={() => { console.log('open the gates') }}
      triggerMarkup={ModalMarkup.BUTTON}
      triggerClassNames={'button is-primary'}
      title={'Extraction Exigences Documentation Unique'}
      triggerContent={'Extraction Exigences'}
      closeIcon
      className='modal-large-panel'
      fullwidth
    >
      <View>
        <ExtractRequirementsForm currentWorkspace={props.currentWorkspace} />
      </View>
    </Modal>
  )
}


export const ExtractRequirementsFabModal: React.FC<ExtractRequirementsProps> = (props) => {

  const [isOpen, setIsOpen] = React.useState<boolean>(false)

  const handleModalState = (wantedState: boolean) => {
    console.debug(`open state: ${isOpen}. wanted state: ${wantedState}`)
    setIsOpen(wantedState)
  }


  return (
    <div id="importRequirements">
      <Fab testId="importRequirementsFab" iconName="tri-cloud-plus-circle" onClick={() => handleModalState(true)}>Extraction Exigences</Fab>
      <Modal
        testId="importRequirementsModal"
        panel
        active={isOpen}
        title={'Extraction Exigences Documentation Unique'}
        onOpen={() => handleModalState(true)}
        disableHandlingClickOutside
        onClose={() => handleModalState(false)}
        closeIcon
        className='modal-large-panel'
        fullwidth
      >
        <View>
          <ExtractRequirementsForm currentWorkspace={props.currentWorkspace} />
        </View>
      </Modal>
    </div>
  )
}