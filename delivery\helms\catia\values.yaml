# Préciser dans la cartographie du referentiel de configuration les éléments suivants:
# ihm.config
# apiweb.config
# db

ihm:
  config: # refconf
    servicesIhmBaseUrl: ""  
    logapiUrlEndpoint: ""
    clientId: catia-bouyguestelecom-fr
    authority: ""

  fullnameOverride: ihm
  imagePullSecrets:
    - name: pullsecret-glouton-ro
  ingress:
    enabled: true
  deploymentExtraSpecs: |
    strategy:
      type: Recreate
  readinessProbe: |
    httpGet:
      path: /health
      port: {{ .Values.service.httpPortName }}
    initialDelaySeconds: 20
    timeoutSeconds: 5
    periodSeconds: 10
    failureThreshold: 3
  extraVolumes: |-
    - name: config-json
      configMap:
        name: ihm-conf
        items:
        - key: config.json
          path: config.json
  extraVolumeMounts: |-
    - name: config-json
      mountPath: /usr/share/nginx/html/config.json
      subPath: config.json
  configFiles:
    config.json: |
      {
        "ST": "{{ .Values.global.ideploy_st_nom }}",
        "ENVIRONNEMENT": "{{ .Values.global.ideploy_cartographie_nom }}",
        "VERSION": "{{ .Values.global.ideploy_st_version }}",
        "PRISME_ENDPOINT": "{{ .Values.config.logapiUrlEndpoint }}",
        "API_URL": "{{ .Values.config.servicesIhmBaseUrl }}",
        "OAUTH2_OIDC_CLIENT_ID": "{{ .Values.config.clientId }}",
        "OAUTH2_OIDC_AUTHORITY": "{{ .Values.config.authority }}"
      }


db: # refconf
  enabled: false                  # mettre a true pour déclencher la création d'une instance rds associé à l'environnement
  allocate_storage: "20"          # espace disque alloué en Go, doit au minimum être de 20
  instance_type: "db.t4g.micro"   # le type d'instance rds qui sera créé
  name: "bdd-{{ .Values.global.ideploy_st_nom }}-{{ .Values.global.ideploy_cartographie_nom }}" 

apiweb:

  config: # refconf
    log_level: INFO
    degraded: "false"
    clientId: catia-bouyguestelecom-fr
    storage_type: ""
    authority: ""
    authorityHostname: ""
    https_proxy: ""
    no_proxy: ""
    db_dialect: ""
    db_host: ""
    db_port: ""
    llm:
      enabled: true
    atlassian:
      hostname: ""
      url: ""
      enabled: false
      verify: true


  fullnameOverride: apiweb
  imagePullSecrets:
    - name: pullsecret-glouton-ro
  ingress:
    enabled: true
# Pour pouvoir acceder aux secrets via Vault (https://gotocloud.pages.gitlab.int.nbyt.io/kubeshift/documentation/ops/Features/secret-management)
  serviceAccount:
    name: default

  deploymentExtraSpecs: |
    strategy:
      type: Recreate
  readinessProbe: |
    httpGet:
      path: /alive
      port: {{ .Values.service.httpPortName }}
    initialDelaySeconds: 20
    timeoutSeconds: 5
    periodSeconds: 10
    failureThreshold: 3
  extraEnvVars: |
    - name: LOG_LEVEL
      value: {{ .Values.config.log_level }}
    - name: OIDC_CLIENT_ID
      value: {{ .Values.config.clientId }}
    - name: OIDC_REALM_URL
      value: {{ .Values.config.authority }}
    - name: STORAGE_TYPE
      value: {{ .Values.config.storage_type }}
    - name: DB_DIALECT
      value: "{{ .Values.config.db_dialect }}"
    - name: DB_HOST
      value: "{{ .Values.config.db_host }}"
    - name: DB_PORT
      value: "{{ .Values.config.db_port }}"
    - name: DEGRADED
      value: "{{ .Values.config.degraded }}"
    - name: HTTPS_PROXY
      value: "{{ .Values.config.https_proxy }}"
    - name: NO_PROXY
      value: "{{ .Values.config.no_proxy }}"
    - name: LLM_ENABLED
      value: "{{ .Values.config.llm.enabled }}"
    - name: ATLASSIAN_URL
      value: "{{ .Values.config.atlassian.url }}"
    - name: ATLASSIAN_ENABLED
      value: "{{ .Values.config.atlassian.enabled }}"
    - name: ATLASSIAN_VERIFY
      value: "{{ .Values.config.atlassian.verify }}"
    - name: ATLASSIAN_LOGIN
      valueFrom:
        secretKeyRef:
          name: vault-catia-secrets
          key: atlassianLogin
    - name: ATLASSIAN_TOKEN
      valueFrom:
        secretKeyRef:
          name: vault-catia-secrets
          key: atlassianToken
    - name: LLM_API_KEY
      valueFrom:
        secretKeyRef:
          name: vault-catia-secrets
          key: apikey
    - name: DB_USER
      valueFrom:
        secretKeyRef:
          name: vault-catia-secrets
          key: dbuser
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          name: vault-catia-secrets
          key: dbpassword
