import React, { useState } from 'react'
import {
  AutoComplete,
  Button,
  Columns,
  ColumnsItem,
  Divider,
  IconName,
  Section,
  Text,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'
import { Item } from '../../components/autocomplete/AutoCompleteProps'
import { InputChangeEvent, InputClickEvent } from '../../components/input/InputProps'

const getSuggestions = async () => {
  return [
    { label: 'name', data: { info: 1 } },
    { label: 'age', data: { info: 2 } },
    { label: 'car', data: { info: 3 } },
    { label: 'test', data: { info: 4 } },
    { label: 'trilogy', data: { info: 5 } },
  ]
}

export const AutoCompleteScreen = (): JSX.Element => {
  const [value, setValue] = useState<string>('')
  const [autoCompleteInputValue, setAutoCompleteInputValue] = useState<string>('')
  const [status, setStatus] = useState<boolean>(false)
  const [data] = [['name', 'age', 'car', 'test', 'trilogy']]

  const onChange = (e: InputChangeEvent) => {
    console.log('OnChange Autocomplete : ', e)
    setValue(e.inputValue)
  }

  const onIconClick = (e: InputClickEvent) => {
    console.log('onIconClick Autocomplete : ', e)
  }

  const [suggestions, setSuggestions] = useState([
    { label: 'name', data: { info: 1 } },
    { label: 'age', data: { info: 2 } },
    { label: 'car', data: { info: 3 } },
    { label: 'test', data: { info: 4 } },
    { label: 'trilogy', data: { info: 5 } },
  ])

  const setEmptySuggestions = () => {
    setSuggestions([])
  }

  const setNewSuggestions = () => {
    setSuggestions([
      { label: 'name', data: { info: 6 } },
      { label: 'age', data: { info: 7 } },
      { label: 'car', data: { info: 8 } },
      { label: 'test', data: { info: 9 } },
    ])
  }

  return (
    <Section>
      <Title level={TitleLevels.THREE}>Autocomplete With Debounce Suggests</Title>

      <AutoComplete
        getSuggestions={async (search) => {
          const res = await fetch(`https://v3.sg.media-imdb.com/suggestion/x/${search}.json`)
          const data = await res.json()
          return data.d.map((item: any) => ({ label: item.l, data: { description: item.s } }))
        }}
        data={[]}
        placeholder='Marque et modèle de votre ancien téléphone'
        onItemSelected={({ value }) => {
          console.log('value : ', value)
        }}
        inputValue={autoCompleteInputValue}
        onChange={({ inputValue }) => setAutoCompleteInputValue(inputValue)}
        debounceSuggestionsTimeout={500}
        onFocus={(e) => console.log('FOCUS : ', e)}
        onBlur={(e) => console.log('BLUR : ', e)}
        loading
      />
      <Title level={TitleLevels.THREE}>Autocomplete</Title>
      <Columns>
        <ColumnsItem>
          <Text>value: {JSON.stringify(value)}</Text>
        </ColumnsItem>
        <ColumnsItem>
          <Button onClick={() => setValue('')}>reset</Button>
          <Button onClick={() => setStatus(!status)}>{status ? 'enable' : 'disable'}</Button>
        </ColumnsItem>
      </Columns>

      <AutoComplete
        customIcon={IconName.ACCOMODATION}
        displayMenu={true}
        inputValue={value}
        data={data}
        absoluteMenu
        fullwidthMenu
        disabled={status}
        placeholder='Autocomplete'
        onItemSelected={(e) => {
          setValue(e.value || '')
          console.log('onItemSelected : ', e.value)
        }}
        onChange={onChange}
        onIconClick={onIconClick}
        onFocus={(e) => console.log('FOCUS : ', e)}
        onBlur={(e) => console.log('BLUR : ', e)}
      />

      <Divider />

      <Title level={TitleLevels.THREE}>Autocomplete custom data</Title>

      <AutoComplete<Item<{ info: number }>>
        customIcon={IconName.ACCOMODATION}
        displayMenu={false}
        data={suggestions}
        placeholder='Autocomplete'
        onItemSelected={(e) => console.log('itemSelected => ', e)}
        onChange={(e) => console.log('OnChange Autocomplete : ', e)}
        onFocus={(e) => console.log('FOCUS : ', e)}
        onBlur={(e) => console.log('BLUR : ', e)}
      >
        {(item) => <Text>La super info : {item.data.info}</Text>}
      </AutoComplete>

      <Button variant='PRIMARY' onClick={setEmptySuggestions}>
        Set empty suggestions
      </Button>
      <Button variant='SECONDARY' onClick={setNewSuggestions}>
        Set new suggestions
      </Button>
      <Divider />

      <Title level={TitleLevels.THREE}>Autocomplete with getSuggestions function</Title>

      <AutoComplete<Item<{ info: number }>>
        customIcon={IconName.ACCOMODATION}
        displayMenu={false}
        data={[]}
        getSuggestions={getSuggestions}
        placeholder='Autocomplete'
        onItemSelected={(e) => console.log('itemSelected => ', e)}
        onChange={(e) => console.log('OnChange Autocomplete : ', e)}
        onFocus={(e) => console.log('FOCUS : ', e)}
        onBlur={(e) => console.log('BLUR : ', e)}
      >
        {(item) => <Text>La super info : {item.data.info}</Text>}
      </AutoComplete>
    </Section>
  )
}
