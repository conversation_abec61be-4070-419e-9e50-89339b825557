import React from 'react'
import { Section, Title, TitleLevels, Divider, TagList, Tag, TagVariant, IconName, Text } from '@bytel/trilogy-react-ts'

export const TagScreen = (): JSX.Element => {
  return (
    <>
      <Section>
        <TagList>
          <Tag onClick={() => alert('test')}>Tag toto</Tag>
          <Tag variant={TagVariant.SECONDARY}>Tag secondaire</Tag>
          <Tag onClick={() => alert('test')} variant={TagVariant.ERROR}>
            Tag error
          </Tag>
          <Tag onClick={() => alert('test')} variant={TagVariant.SUCCESS}>
            Tag success
          </Tag>
          <Tag onClick={() => alert('test')} variant={TagVariant.WARNING}>
            Tag warning
          </Tag>
        </TagList>
        <Title level={TitleLevels.THREE}>Tag supprimable</Title>
        <Divider />
        <Tag deletable onClick={() => alert('Tag action')} variant={TagVariant.SECONDARY}>
          Tag supprimable
        </Tag>
        <Tag deletable onClick={() => alert('Tag action')} variant={TagVariant.ERROR}>
          Tag supprimable
        </Tag>
      </Section>

      <Section>
        <Title level={TitleLevels.THREE}>Tag avec liste</Title>
        <Divider />
        <Text>Avec liste</Text>
        <TagList>
          <Tag variant={TagVariant.ERROR}>Tag error</Tag>
          <Tag variant={TagVariant.SECONDARY}>Tag secondaire</Tag>
          <Tag variant={TagVariant.SUCCESS}>Tag success</Tag>
          <Tag variant={TagVariant.WARNING}>Tag warning</Tag>
        </TagList>
      </Section>

      <Section>
        <Title level={TitleLevels.THREE}>Tag avec icon</Title>
        <Divider />
        <TagList>
          <Tag iconName={IconName.EXCLAMATION_CIRCLE} variant={TagVariant.ERROR}>
            Tag error
          </Tag>
          <Tag iconName={IconName.CHECK_CIRCLE} variant={TagVariant.SUCCESS}>
            Tag success
          </Tag>
          <Tag iconName={IconName.ALERT} variant={TagVariant.WARNING}>
            Tag warning
          </Tag>
          <Tag iconName={IconName.INFOS_CIRCLE} variant={TagVariant.SECONDARY}>
            Tag secondaire
          </Tag>
          <Tag iconName={IconName.CHECK_CIRCLE}>Tag warning</Tag>
        </TagList>
      </Section>

      <Section>
        <Title level={TitleLevels.THREE}>Small tag</Title>
        <Divider />
        <Tag small variant={TagVariant.ERROR}>
          Tag error
        </Tag>
        <Tag small variant={TagVariant.SUCCESS}>
          Tag success
        </Tag>
        <Tag small iconName={IconName.ALERT} variant={TagVariant.WARNING}>
          Tag warning
        </Tag>
      </Section>
    </>
  )
}
