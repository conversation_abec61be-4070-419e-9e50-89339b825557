import React from 'react'
import {
  Section,
  Box,
  BoxHeader,
  BoxContent,
  Divider,
  BoxFooter,
  BoxMarkup,
  Text,
  Link,
  AutoLayout,
} from '@bytel/trilogy-react-ts'
import { TrilogyColor } from '@bytel/trilogy-react-ts'

export const BoxScreen = (): JSX.Element => {
  return (
    <Section>
      <AutoLayout>
        <Box active background={'GREY_LIGHT'}>
          <BoxHeader color={TrilogyColor.PRIMARY}>
            <Text>Active</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc maximus tellus sed erat maximus porta.
                Etiam non ex in dolor faucibus tempor. Sed ullamcorper, ligula sit amet dictum posuere, urna tortor
                vulputate justo, ut luctus justo eros sed erat. Fusce finibus dolor ex. Duis vel velit in lectus
                placerat aliquam nec at elit. Aenean metus neque, accumsan id ipsum sodales, fermentum lacinia eros. Ut
                gravida aliquet magna, id efficitur magna ultrices a. In quis bibendum tortor. Nam quam lacus, suscipit
                a vehicula ac, vehicula eget risus.
              </Text>
            </Box>
          </BoxContent>
        </Box>

        <Divider />

        <Box background={'GREY_LIGHT'}>
          <BoxHeader color={TrilogyColor.PRIMARY}>
            <Text>Simple</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc maximus tellus sed erat maximus porta.
                Etiam non ex in dolor faucibus tempor. Sed ullamcorper, ligula sit amet dictum posuere, urna tortor
                vulputate justo, ut luctus justo eros sed erat. Fusce finibus dolor ex. Duis vel velit in lectus
                placerat aliquam nec at elit. Aenean metus neque, accumsan id ipsum sodales, fermentum lacinia eros. Ut
                gravida aliquet magna, id efficitur magna ultrices a. In quis bibendum tortor. Nam quam lacus, suscipit
                a vehicula ac, vehicula eget risus.
              </Text>
            </Box>
          </BoxContent>
        </Box>

        <Divider />

        <Box background={'GREY_LIGHT'}>
          <BoxHeader>
            <Text>Box accentuée</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc maximus tellus sed erat maximus porta.
                Etiam non ex in dolor faucibus tempor. Sed ullamcorper, ligula sit amet dictum posuere, urna tortor
                vulputate justo, ut luctus justo eros sed erat. Fusce finibus dolor ex. Duis vel velit in lectus
                placerat aliquam nec at elit. Aenean metus neque, accumsan id ipsum sodales, fermentum lacinia eros. Ut
                gravida aliquet magna, id efficitur magna ultrices a. In quis bibendum tortor. Nam quam lacus, suscipit
                a vehicula ac, vehicula eget risus.
              </Text>
            </Box>
            <Box>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc maximus tellus sed erat maximus porta.
                Etiam non ex in dolor faucibus tempor. Sed ullamcorper, ligula sit amet dictum posuere, urna tortor
                vulputate justo, ut luctus justo eros sed erat. Fusce finibus dolor ex. Duis vel velit in lectus
                placerat aliquam nec at elit. Aenean metus neque, accumsan id ipsum sodales, fermentum lacinia eros. Ut
                gravida aliquet magna, id efficitur magna ultrices a. In quis bibendum tortor. Nam quam lacus, suscipit
                a vehicula ac, vehicula eget risus.
              </Text>
            </Box>
            <Box>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc maximus tellus sed erat maximus porta.
                Etiam non ex in dolor faucibus tempor. Sed ullamcorper, ligula sit amet dictum posuere, urna tortor
                vulputate justo, ut luctus justo eros sed erat. Fusce finibus dolor ex. Duis vel velit in lectus
                placerat aliquam nec at elit. Aenean metus neque, accumsan id ipsum sodales, fermentum lacinia eros. Ut
                gravida aliquet magna, id efficitur magna ultrices a. In quis bibendum tortor. Nam quam lacus, suscipit
                a vehicula ac, vehicula eget risus.
              </Text>
            </Box>
          </BoxContent>
        </Box>

        <Box background={'GREY_LIGHT'} flat>
          <BoxHeader>
            <Text>Box Flat</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Text>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc maximus tellus sed erat maximus porta. Etiam
              non ex in dolor faucibus tempor. Sed ullamcorper, ligula sit amet dictum posuere, urna tortor vulputate
              justo, ut luctus justo eros sed erat. Fusce finibus dolor ex. Duis vel velit in lectus placerat aliquam
              nec at elit. Aenean metus neque, accumsan id ipsum sodales, fermentum lacinia eros. Ut gravida aliquet
              magna, id efficitur magna ultrices a. In quis bibendum tortor. Nam quam lacus, suscipit a vehicula ac,
              vehicula eget risus.
            </Text>
          </BoxContent>
        </Box>

        <Box background={'GREY_LIGHT'}>
          <BoxHeader>
            <Text>Box avec Titre</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box>
              <BoxHeader color={TrilogyColor.PRIMARY}>
                <Text>Box is-unboxed</Text>
              </BoxHeader>
              <BoxContent>
                <Text>
                  Eget tincidunt tincidunt id massa sollicitudin. Egestas felis dolor neque nunc. Eget suscipit enim
                  velit ultricies justo ultrices sed leo cras.
                </Text>
                <Divider unboxed />
                <Text>
                  Eget tincidunt tincidunt id massa sollicitudin. Egestas felis dolor neque nunc. Eget suscipit enim
                  velit ultricies justo ultrices sed leo cras.
                </Text>
              </BoxContent>
            </Box>
          </BoxContent>
        </Box>

        <Box background={'GREY_LIGHT'}>
          <BoxHeader>
            <Text>Box avec Titre</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box>
              <BoxHeader color={TrilogyColor.PRIMARY}>
                <Text>Ceci est mon titre</Text>
              </BoxHeader>
              <BoxContent>
                <Text>
                  Eget tincidunt tincidunt id massa sollicitudin. Egestas felis dolor neque nunc. Eget suscipit enim
                  velit ultricies justo ultrices sed leo cras.
                </Text>
              </BoxContent>
            </Box>
          </BoxContent>
        </Box>

        <Box>
          <BoxHeader>
            <Text>leftBorder</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box leftBorder={TrilogyColor.ERROR}>
              <BoxContent>
                <Text>
                  Eget tincidunt tincidunt id massa sollicitudin. Egestas felis dolor neque nunc. Eget suscipit enim
                  velit ultricies justo ultrices sed leo cras.
                </Text>
              </BoxContent>
            </Box>
          </BoxContent>
        </Box>

        <Box>
          <BoxHeader>
            <Text>Avec un footer</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box>
              <BoxHeader color={TrilogyColor.PRIMARY}>
                <Text>Ceci est mon titre</Text>
              </BoxHeader>
              <BoxContent>
                <Text>
                  Eget tincidunt tincidunt id massa sollicitudin. Egestas felis dolor neque nunc. Eget suscipit enim
                  velit ultricies justo ultrices sed leo cras.
                </Text>
              </BoxContent>
              <BoxFooter>
                <Link>Lien</Link>
              </BoxFooter>
            </Box>
          </BoxContent>
        </Box>

        <Box>
          <BoxHeader>
            <Text>Box cliquable</Text>
          </BoxHeader>
          <BoxContent background={'GREY_LIGHT'}>
            <Box markup={BoxMarkup.A}>
              <BoxContent>
                <Text>
                  Eget tincidunt tincidunt id massa sollicitudin. Egestas felis dolor neque nunc. Eget suscipit enim
                  velit ultricies justo ultrices sed leo cras.
                </Text>
              </BoxContent>
              <BoxFooter>
                <Link>Lien</Link>
              </BoxFooter>
            </Box>
          </BoxContent>
        </Box>

        <Box>
          <BoxContent background={'GREY_LIGHT'}>
            <Box leftBorder={TrilogyColor.ERROR} backgroundSrc={'https://picsum.photos/id/1/1500/600'}>
              <Text>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. In pharetra venenatis neque, ac fringilla
                mauris fermentum vel. Maecenas viverra, erat id condimentum ultricies, enim enim lacinia sem, sed
                blandit nisi metus suscipit elit. Phasellus magna risus, mattis sed consectetur at, rhoncus vitae quam.
                Vivamus varius nisl a nibh finibus, non laoreet eros ornare. Phasellus dignissim ullamcorper tortor ut
                iaculis. Fusce tincidunt finibus fermentum. Praesent pulvinar sapien a turpis faucibus, et semper quam
                scelerisque. Morbi interdum nec ipsum eu facilisis. Mauris commodo interdum est et ornare. Proin eu diam
                vel arcu convallis tempor. Nam facilisis sodales enim, quis volutpat magna scelerisque sit amet. Nam in
                urna urna. Donec et orci ac ex pretium tristique. Cras vel erat quis nibh sodales ultrices vitae
                lobortis libero. Quisque semper nisl dictum, maximus eros id, malesuada ligula. Etiam maximus urna vitae
                tempor suscipit. Mauris sed nisl sit amet dui tincidunt hendrerit sed et risus.
              </Text>
            </Box>
          </BoxContent>
        </Box>
      </AutoLayout>
    </Section>
  )
}
