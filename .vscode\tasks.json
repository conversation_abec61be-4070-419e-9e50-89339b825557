{
	"version": "2.0.0",
	"tasks": [
		{
			"type": "npm",
			"script": "dev",
			"path": "ihm",
			"isBackground": true,
			"problemMatcher": [
				{
					"pattern": [
						{
							"regexp": ".",
							"file": 1,
							"location": 2,
							"message": 3
						}
					],
					"background": {
						"activeOnStart": true,
						"beginsPattern": "VITE",
						"endsPattern": "ready in"
					}
				}
			],
			"label": "frontend - preview",
			"detail": "permet de démarrer le serveur de l'ihm"
		},
		{
			"type": "npm",
			"script": "start",
			"path": "documentation",
			"isBackground": true,
			"problemMatcher": [
				{
					"pattern": [
						{
							"regexp": ".",
							"file": 1,
							"location": 2,
							"message": 3
						}
					],
					"background": {
						"activeOnStart": true,
						"beginsPattern": ".",
						"endsPattern": "."
					}
				}
			],
			"label": "📝 docs - ✏️ preview",
			"detail": "permet de visualiser en local ses éditions"
		},
		{
			"type": "npm",
			"script": "build",
			"path": "documentation",
			"group": "build",
			"problemMatcher": [],
			"label": "📝 docs - 📦️ build",
			"detail": "quick-doc build --out-dir public",
			"hide": true
		},
		{
			"type": "npm",
			"script": "serve",
			"path": "documentation",
			"problemMatcher": [],
			"label": "📝 docs - 🚀 release",
			"detail": "permet de visualiser la release qui sera sur gitlab pages",
			"dependsOn": [
				"📝 docs - 📦️ build"
			]
		},
		{
			"problemMatcher": [
				"$python",
				{
					"pattern": [
						{
							"regexp": ".",
							"file": 1,
							"location": 2,
							"message": 3
						}
					],
					"background": {
						"beginsPattern": {
							"regexp": "FastAPI"
						},
						"endsPattern": {
							"regexp": "Application startup complete."
						}
					}
				}
			],
			"type": "shell",
			"command": "uv run fastapi dev ${workspaceFolder}/backend/app/main.py",
			"label": "backend - preview",
			"detail": "permet de démarrer le serveur fastAPI en mode debug",
			"options": {
				"cwd": "${workspaceFolder}/backend"
			},
			"isBackground": true
		},
		{
			"problemMatcher": [
				"$python"
			],
			"type": "shell",
			"command": "uv run ${workspaceFolder}/backend/generate_openapi.py",
			"label": "backend - generate openapi",
			"detail": "genere dans le dossier catalogue-interfaces la specificiation openapi",
			"options": {
				"cwd": "${workspaceFolder}/backend"
			},
			"runOptions": {
				"instanceLimit": 1
			},
			"presentation": {
				"reveal": "always",
				"panel": "new",
				"close": true
			},
			"isBackground": false
		},
		{
			"label": "🎉 local env - start",
			"detail": "démarre l'environnement local de dev 👷 attention de bien avoir réalisé les installations au préalable",
			"dependsOn": [
				"backend - preview",
				"frontend - preview",
			],
			"problemMatcher": []
		}
	]
}