import React, { useState } from 'react'
import {
  Box,
  BoxContent,
  Breadcrumb,
  BreadcrumbItem,
  Button,
  Card,
  CardContent,
  Container,
  Divider,
  IconName,
  RowItem,
  Rows,
  Section,
  Text,
} from '@bytel/trilogy-react-ts'
import { TrilogyColor, TypographyBold } from '@bytel/trilogy-react-ts'

export const BreadcrumScreen = (): JSX.Element => {
  const [active, setActive] = useState(2)

  return (
    <Section>
      {/* N EXISTE PAS EN MOBILE */}

      <Text typo={[TypographyBold.TEXT_WEIGHT_SEMIBOLD]}>Simple Breadcrumb</Text>

      <Breadcrumb>
        <BreadcrumbItem active={active == 0} href='https://google.fr'>
          Google
        </BreadcrumbItem>
        <BreadcrumbItem active={active == 1} to='#anchor'>
          Parent avec ancre
        </BreadcrumbItem>
        <BreadcrumbItem active={active == 2}>Parent</BreadcrumbItem>
        <BreadcrumbItem active={active == 3}>Page en cours</BreadcrumbItem>
      </Breadcrumb>

      <Button variant='PRIMARY' onClick={() => setActive((prev) => (prev + 1 > 3 ? 0 : prev + 1))}>
        active + 1
      </Button>

      <Text typo={[TypographyBold.TEXT_WEIGHT_SEMIBOLD]}>Breadcrumb in box</Text>
      <Box>
        <BoxContent>
          <Breadcrumb>
            <BreadcrumbItem active={active == 0} href='https://google.fr'>
              Google
            </BreadcrumbItem>
            <BreadcrumbItem active={active == 1} to='#anchor'>
              Parent avec ancre
            </BreadcrumbItem>
            <BreadcrumbItem active={active == 2}>Parent</BreadcrumbItem>
            <BreadcrumbItem active={active == 3}>Page en cours</BreadcrumbItem>
          </Breadcrumb>
        </BoxContent>
      </Box>

      <Text typo={[TypographyBold.TEXT_WEIGHT_SEMIBOLD]}>Breadcrumb in card</Text>
      <Card>
        <CardContent>
          <Breadcrumb>
            <BreadcrumbItem active={active == 0} href='https://google.fr'>
              Google
            </BreadcrumbItem>
            <BreadcrumbItem active={active == 1} to='#anchor'>
              Parent avec ancre
            </BreadcrumbItem>
            <BreadcrumbItem active={active == 2}>Parent</BreadcrumbItem>
            <BreadcrumbItem active={active == 3}>Page en cours</BreadcrumbItem>
          </Breadcrumb>
        </CardContent>
      </Card>

      <Text typo={[TypographyBold.TEXT_WEIGHT_SEMIBOLD]}>Breadcrumb in rows</Text>
      <Container centered={true} verticalCentered={true}>
        <Rows>
          <RowItem></RowItem>
          <RowItem>
            <Container centered verticalCentered>
              <Breadcrumb>
                <BreadcrumbItem href='https://google.fr'>Google</BreadcrumbItem>
                <BreadcrumbItem to='#anchor'>Parent avec ancre</BreadcrumbItem>
                <BreadcrumbItem>Parent</BreadcrumbItem>
                <BreadcrumbItem active>Page en cours</BreadcrumbItem>
              </Breadcrumb>
            </Container>
          </RowItem>
        </Rows>
        <Divider iconName={IconName.LOGO} color={TrilogyColor.GREY_LIGHT} />
      </Container>
    </Section>
  )
}
