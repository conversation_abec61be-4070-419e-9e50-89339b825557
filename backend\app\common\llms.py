"""
les différentes fonction pour faire une inférence auprès d'un LLM
"""

import logging

import openai
from langchain_openai.chat_models import AzureChatOpenAI

from app.common.config import ChatGPTConfig

LOGGER = logging.getLogger(__name__)


def get_llm(llm_config: ChatGPTConfig, model_kwargs: dict = None, **kwargs) -> AzureChatOpenAI:
    """
    Méthode à utiliser pour bénéficier d'une instance plus personnalisable
    (exemple : retourner uniquement du JSON : {"response_format": {"type": "json_object"}})
    """
    LOGGER.debug("Création d'un client Azure OpenAI...")
    if model_kwargs is None:
        model_kwargs = {}
    llm = AzureChatOpenAI(**llm_config.model_dump(exclude=["enabled"]), model_kwargs=model_kwargs, **kwargs)
    # TODO: a voir si on en aura besoin en mode debug
    # llm.client = OpenAiModelsWrapper(llm.client)
    return llm


class OpenAiModelsWrapper:
    """
    Classe permettant de mieux visualiser les appels et réponse aux LLM d'OpenAI lorsque l'on fait du streaming de la réponse
    """

    def __init__(self, wrapped_class):
        self.wrapped_class = wrapped_class

    def __getattr__(self, attr):
        original_func = getattr(self.wrapped_class, attr)

        def wrapper(*args, **kwargs):
            result = original_func(*args, **kwargs)
            LOGGER.debug("Response du LLM: %s", result)
            try:
                if isinstance(result, openai.Stream):
                    message = str(result)
                else:
                    message = f"Completion successfull: Model used is '{result.model}'. {result.usage}"
                LOGGER.info(message)
            except Exception as e:
                LOGGER.error("Erreur lors de la récupération des informations d'usage du modèle: %s", e)
            return result

        return wrapper
