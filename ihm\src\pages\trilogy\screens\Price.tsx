import React from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Price,
  PriceLevel,
  PriceVariant,
  Section,
  Spacer,
  SpacerSize,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'
import { Alignable, VariantState } from '@bytel/trilogy-react-ts'

export const PriceScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={TitleLevels.THREE}>Price tag</Title>

      <Price
        level={'1'}
        amount={24.99}
        showCents
        period={'mois'}
        tagAmount={10}
        tagSymbol={'%'}
        suptitle={'A partir de '}
      />

      <Price
        level={'1'}
        amount={24.99}
        showCents
        period={'mois'}
        tagAmount={10}
        tagSymbol={'%'}
        suptitle={'A partir de '}
      />

      <Price
        level={'3'}
        amount={24.99}
        showCents
        period={'mois'}
        tagAmount={10}
        tagSymbol={'%'}
        suptitle={'A partir de '}
      />

      <Price
        level={'4'}
        amount={1}
        period={'mois'}
        tagAmount={10}
        tagSymbol={'%'}
        suptitle={'A partir de '}
      />

      <Price level={'1'} amount={24.99} showCents period={'mois'} tagAmount={5} tagSymbol={'%'} />

      <Price level={'1'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'%'} />

      <Spacer size={SpacerSize.SMALL} />

      <Price level={'2'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'€'} />

      <Spacer size={SpacerSize.SMALL} />

      <Price level={'3'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'€'} />

      <Spacer size={SpacerSize.SMALL} />

      <Price level={'4'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'€'} />

      <Spacer size={SpacerSize.SMALL} />

      <Price level={'5'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'€'} />

      <Spacer size={SpacerSize.SMALL} />

      <Price level={'6'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'€'} />

      <Spacer size={SpacerSize.SMALL} />

      <Price level={'7'} amount={24.99} showCents period={'mois'} tagAmount={10} tagSymbol={'€'} />

      <Spacer size={SpacerSize.MEDIUM} />

      <Price variant={PriceVariant.PRIMARY} level={'6'} amount={1000} showCents />
      <Price variant={PriceVariant.PRIMARY} level={'6'} amount={18.99} showCents />
      <Title level={TitleLevels.THREE}>Sur une même ligne</Title>
      <Price
        variant={PriceVariant.PRIMARY}
        level={'7'}
        amount={1000}
        mention='(1)'
        period='mois'
        showCents
        align={Alignable.ALIGNED_START}
        suptitle={'A partir de '}
      />

      <Price
        variant={PriceVariant.PRIMARY}
        level={'7'}
        amount={24}
        period='mois'
        align={Alignable.ALIGNED_START}
        inline
      />

      <Price
        variant={PriceVariant.PRIMARY}
        level={'7'}
        amount={1000}
        mention='(1)'
        period='mois'
        showCents
        align={Alignable.ALIGNED_START}
        suptitle={'A partir de '}
        inline
      />

      <Price
        variant={PriceVariant.PRIMARY}
        level={'1'}
        amount={1000}
        mention='(1)'
        period='mois'
        showCents
        align={Alignable.ALIGNED_CENTER}
      />

      <Price
        variant={PriceVariant.PRIMARY}
        level={'1'}
        amount={1000}
        mention='(1)'
        period='mois'
        showCents
        align={Alignable.ALIGNED_END}
      />

      <Divider />

      <Title level={TitleLevels.THREE}>Tailles</Title>

      <Price
        variant={PriceVariant.PRIMARY}
        level={PriceLevel.LEVEL1}
        amount={100}
        mention='(1)'
        period='mois'
        showCents
      />

      <Price
        variant={PriceVariant.PRIMARY}
        level={PriceLevel.LEVEL2}
        amount={24.99}
        mention='(1)'
        period='mois'
        showCents
      />

      <Price
        variant={PriceVariant.SECONDARY}
        level={PriceLevel.LEVEL3}
        amount={24.99}
        mention='(1)'
        period='mois'
        showCents
      />

      <Price
        variant={PriceVariant.PRIMARY}
        level={PriceLevel.LEVEL4}
        amount={18.99}
        mention='(1)'
        period='mois'
        showCents
      />

      <Price
        variant={PriceVariant.SECONDARY}
        level={PriceLevel.LEVEL5}
        amount={18.99}
        mention='(1)'
        period='mois'
        showCents
      />

      <Price
        variant={PriceVariant.SECONDARY}
        level={PriceLevel.LEVEL6}
        amount={18.1}
        mention='(1)'
        period='mois'
        showCents
      />

      <Price
        variant={PriceVariant.SECONDARY}
        level={PriceLevel.LEVEL7}
        amount={18.1}
        mention='(1)'
        period='mois'
        showCents
      />
      <Divider />

      <Title level={TitleLevels.THREE}>Prix inversé</Title>

      <Hero variant={VariantState.TERTIARY}>
        <Price inverted level={PriceLevel.LEVEL1} amount={18.99} mention='(1)' period='mois' showCents />

        <Price striked inverted level={PriceLevel.LEVEL1} amount={18.99} mention='(1)' period='mois' showCents />
      </Hero>

      <Divider />

      <Title level={TitleLevels.THREE}>Avec ou sans les centimes</Title>

      <Price variant={PriceVariant.PRIMARY} level={PriceLevel.LEVEL4} amount={18} period='mois' />

      <Price variant={PriceVariant.PRIMARY} level={PriceLevel.LEVEL4} amount={18} period='mois' showCents />
    </Section>
  )
}
