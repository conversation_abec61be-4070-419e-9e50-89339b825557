{"exigences": [{"nom": "import swagger", "description": "description swagger", "type": "OpenAPI", "data": "{\r\n\t\"openapi\": \"3.0.2\",\r\n\t\"servers\": [\r\n\t\t{\r\n\t\t\t\"url\": \"https://api.bouyguestelecom.fr\",\r\n\t\t\t\"description\": \"Accès à la prod\"\r\n\t\t}\r\n\t],\r\n\t\"paths\": {\r\n\t\t\"/etalements-de-paiements\": {\r\n\t\t\t\"post\": {\r\n\t\t\t\t\"tags\": [\r\n\t\t\t\t\t\"souscrireEdp\"\r\n\t\t\t\t],\r\n\t\t\t\t\"summary\": \"Permet de souscrire un EDP\",\r\n\t\t\t\t\"operationId\": \"souscrireEdp\",\r\n\t\t\t\t\"parameters\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"trackerId\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"Unique généré au démarrage de la navigation de l’utilisateur et qui sera fourni lors de chaque appel aux ressources. Cet identifiant peut être unique pour un device donné mais a minima est généré pour chaque navigation sur nos sites.\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 36\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"x-request-id\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"Identifiant de requête sur le SI (identifiant unique généré lors de l'appel à chaque ressource si non présent en entrée et fourni à chaque service appelé en synchrone)\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 36\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"x-source\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"Nom de l’émetteur d’un traitement (sous la forme <NomDuST>.<application>)\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 64\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"x-action-id\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"la requête va être appliquée sur le SI avec potentiellement un ensemble de traitements synchrones décorrélés dans le temps : les actions (identifiant unique généré lors du démarrage d'un nouveau traitement asynchrone ou si non présent en entrée et fourni à chaque service appelé en synchrone)\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 36\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"x-message-id\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"Unique pour chaque interaction entre ST (il peut permettre de gérer l'anti-rejeu sur des alimentations par exemple)\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 36\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"x-caller-id\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 36\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"x-process\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"Nom humainement compréhensible de l’action de l’appelant (ex : soumissionPanier)\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 64\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"X-Version\",\r\n\t\t\t\t\t\t\"required\": false,\r\n\t\t\t\t\t\t\"description\": \"Version demandée\",\r\n\t\t\t\t\t\t\"in\": \"header\",\r\n\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"pattern\": \"d+\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"requestBody\": {\r\n\t\t\t\t\t\"content\": {\r\n\t\t\t\t\t\t\"application/json\": {\r\n\t\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\t\"$ref\": \"#/components/schemas/SouscrireEdpIn\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\t\"required\": true\r\n\t\t\t\t},\r\n\t\t\t\t\"responses\": {\r\n\t\t\t\t\t\"200\": {\r\n\t\t\t\t\t\t\"content\": {\r\n\t\t\t\t\t\t\t\"application/json\": {\r\n\t\t\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\t\t\"$ref\": \"#/components/schemas/SouscrireEdpOut\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"description\": \"Operation Successful\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"400\": {\r\n\t\t\t\t\t\t\"description\": \"Bad request\",\r\n\t\t\t\t\t\t\"content\": {\r\n\t\t\t\t\t\t\t\"application/json\": {\r\n\t\t\t\t\t\t\t\t\"schema\": {\r\n\t\t\t\t\t\t\t\t\t\"$ref\": \"#/components/schemas/SouscrireEdp400Response\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\"x-security-in\": \"VERT\",\r\n\t\t\t\t\"x-security-out\": \"VERT\"\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t\"components\": {\r\n\t\t\"schemas\": {\r\n\t\t\t\"ActeurCreateur\": {\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"type\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"entiteReseauDistribution\": {\r\n\t\t\t\t\t\t\"$ref\": \"#/components/schemas/EntiteReseauDistribution\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"type\": {\r\n\t\t\t\t\t\t\"description\": \"acteurs possible\",\r\n\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\"enum\": [\r\n\t\t\t\t\t\t\t\"CDC\",\r\n\t\t\t\t\t\t\t\"CDV\",\r\n\t\t\t\t\t\t\t\"CDV_BD\",\r\n\t\t\t\t\t\t\t\"PERSONNE\",\r\n\t\t\t\t\t\t\t\"TLV\"\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\"Edp\": {\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"montantPrelevement\",\r\n\t\t\t\t\t\"montantTotal\",\r\n\t\t\t\t\t\"nombrePrelevements\",\r\n\t\t\t\t\t\"offrePrincipale\",\r\n\t\t\t\t\t\"oneOff\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"montantPrelevement\": {\r\n\t\t\t\t\t\t\"description\": \"montant d'une mensualité\",\r\n\t\t\t\t\t\t\"type\": \"number\",\r\n\t\t\t\t\t\t\"format\": \"float\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"montantTotal\": {\r\n\t\t\t\t\t\t\"description\": \"montant total de l'EDP\",\r\n\t\t\t\t\t\t\"type\": \"number\",\r\n\t\t\t\t\t\t\"format\": \"float\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"nombrePrelevements\": {\r\n\t\t\t\t\t\t\"description\": \"nombre de prélèvement consituant l'EDP\",\r\n\t\t\t\t\t\t\"type\": \"integer\",\r\n\t\t\t\t\t\t\"format\": \"int32\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"offrePrincipale\": {\r\n\t\t\t\t\t\t\"$ref\": \"#/components/schemas/OffrePrincipale\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"oneOff\": {\r\n\t\t\t\t\t\t\"description\": \"montant payé par le client lors de sa commande\",\r\n\t\t\t\t\t\t\"type\": \"number\",\r\n\t\t\t\t\t\t\"format\": \"float\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\"EntiteReseauDistribution\": {\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"codeEnseigne\",\r\n\t\t\t\t\t\"codePointVente\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"codeEnseigne\": {\r\n\t\t\t\t\t\t\"description\": \"code enseigne de la boutique de vente\",\r\n\t\t\t\t\t\t\"maxLength\": 3,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"codePointVente\": {\r\n\t\t\t\t\t\t\"description\": \"code point de vente\",\r\n\t\t\t\t\t\t\"maxLength\": 4,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\"OffrePrincipale\": {\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"noOffre\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"noOffre\": {\r\n\t\t\t\t\t\t\"description\": \"id de l'offre du client\",\r\n\t\t\t\t\t\t\"maxLength\": 6,\r\n\t\t\t\t\t\t\"minLength\": 1,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\"SouscrireEdpIn\": {\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"acteurCreateur\",\r\n\t\t\t\t\t\"edp\",\r\n\t\t\t\t\t\"idCommande\",\r\n\t\t\t\t\t\"modePaiement\",\r\n\t\t\t\t\t\"nom\",\r\n\t\t\t\t\t\"prenom\",\r\n\t\t\t\t\t\"typeOffreAchetee\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"acteurCreateur\": {\r\n\t\t\t\t\t\t\"$ref\": \"#/components/schemas/ActeurCreateur\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"edp\": {\r\n\t\t\t\t\t\t\"$ref\": \"#/components/schemas/Edp\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"idCommande\": {\r\n\t\t\t\t\t\t\"description\": \"identifiant de la commande\",\r\n\t\t\t\t\t\t\"maxLength\": 20,\r\n\t\t\t\t\t\t\"minLength\": 1,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"modePaiement\": {\r\n\t\t\t\t\t\t\"description\": \"moyen de paiement du client\",\r\n\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\"enum\": [\r\n\t\t\t\t\t\t\t\"PRELEVEMENT\"\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"nom\": {\r\n\t\t\t\t\t\t\"description\": \"nom de famille du client\",\r\n\t\t\t\t\t\t\"maxLength\": 40,\r\n\t\t\t\t\t\t\"minLength\": 1,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"prenom\": {\r\n\t\t\t\t\t\t\"description\": \"prénom du client\",\r\n\t\t\t\t\t\t\"maxLength\": 40,\r\n\t\t\t\t\t\t\"minLength\": 1,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"siren\": {\r\n\t\t\t\t\t\t\"description\": \"siren du client : obligatoire si client PRO doit être à 9 caractères max, mais on tolère 15 le temps de corriger les siret.\",\r\n\t\t\t\t\t\t\"maxLength\": 15,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"typeOffreAchetee\": {\r\n\t\t\t\t\t\t\"description\": \"offre commerciale : parcours du client\",\r\n\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\"enum\": [\r\n\t\t\t\t\t\t\t\"ACQUISITION_SUBVENTIONNEE\",\r\n\t\t\t\t\t\t\t\"RENOUVELLEMENT\",\r\n\t\t\t\t\t\t\t\"TERMINAL_NU\"\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\"SouscrireEdpOut\": {\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"idEdp\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"idEdp\": {\r\n\t\t\t\t\t\t\"description\": \"identifiant de l'EDP\",\r\n\t\t\t\t\t\t\"maxLength\": 20,\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\"SouscrireEdp400Response\": {\r\n\t\t\t\t\"description\": \"Description d'une erreur fonctionnelle\",\r\n\t\t\t\t\"type\": \"object\",\r\n\t\t\t\t\"required\": [\r\n\t\t\t\t\t\"error\"\r\n\t\t\t\t],\r\n\t\t\t\t\"properties\": {\r\n\t\t\t\t\t\"error\": {\r\n\t\t\t\t\t\t\"description\": \"Le code erreur permet de discriminer les différentes erreurs\",\r\n\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\"enum\": [\r\n\t\t\t\t\t\t\t\"FORMAT_INCORRECT\"\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"error_description\": {\r\n\t\t\t\t\t\t\"description\": \"Description de l'erreur, si possible fixe pour un même code erreur\",\r\n\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\"maxLength\": 256\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"error_uri\": {\r\n\t\t\t\t\t\t\"description\": \"L'URI de description de l'erreur\",\r\n\t\t\t\t\t\t\"type\": \"string\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"error_parameters\": {\r\n\t\t\t\t\t\t\"description\": \"Les paramètres de l'erreur\",\r\n\t\t\t\t\t\t\"type\": \"array\",\r\n\t\t\t\t\t\t\"items\": {\r\n\t\t\t\t\t\t\t\"type\": \"string\",\r\n\t\t\t\t\t\t\t\"maxLength\": 64\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t\"info\": {\r\n\t\t\"x-st\": \"EDPIUM\",\r\n\t\t\"title\": \"ETALEMENT-DE-PAIEMENT-souscrireEdp\",\r\n\t\t\"version\": \"1.1.127\",\r\n\t\t\"description\": \"API des services métiers EDIPIUM\",\r\n\t\t\"contact\": {\r\n\t\t\t\"name\": \"MOE EDPIUM\"\r\n\t\t},\r\n\t\t\"x-execution\": \"api\",\r\n\t\t\"x-type\": \"metier-bytel\",\r\n\t\t\"x-objetmetier\": \"ETALEMENT-DE-PAIEMENT\",\r\n\t\t\"x-role\": \"consumer\"\r\n\t},\r\n\t\"tags\": [\r\n\t\t{\r\n\t\t\t\"name\": \"souscrireEdp\",\r\n\t\t\t\"description\": \"Permet de souscrire un EDP\"\r\n\t\t}\r\n\t]\r\n}", "id": "512e2e31-3574-4f55-b8e2-c409f9c7f03d", "owner_id": "fd268429-bb48-4ebe-bcba-840b79b45b4a", "ws_id": "80909b37-3871-4807-b5d4-60b0d04d5a92"}], "documents": null}