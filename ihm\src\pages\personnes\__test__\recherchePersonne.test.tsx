import { fireEvent, screen, waitFor } from "@testing-library/react";
import { customRender } from "tests/custom-renderer";
import { mockAPI } from "tests/init-mocks";
import { RecherchePersonnesPage } from "pages/personnes/recherchePersonne.tsx";
import { Individu } from "services/interfaces/generated/HELLOWORLD-FORMATION/rechercherPersonnes.ts";

describe("Recherche d'une personne", () => {
  test("recherche simple", async () => {
    // GIVEN
    mockAPI.personneService.rechercherPersonnes.mockResolvedValue([
      {
        _type: "Individu",
        idPersonneUnique: "800000000526",
        nom: "Bond",
        prenom: "<PERSON>",
        civilite: "M",
        dateNaissance: "01/01/1970",
        departementNaissance: "44",
      },
      {
        _type: "Individu",
        idPersonneUnique: "800000000527",
        nom: "Bond",
        prenom: "<PERSON>",
        civilite: "<PERSON>",
        dateNaissance: "01/01/1970",
        departementNaissance: "93",
      },
    ] as Individu[]);

    // WHEN
    customRender(<RecherchePersonnesPage />);

    fireEvent.change(screen.getByRole("textbox"), {
      target: { value: "Bond" },
    });
    fireEvent.click(screen.getByRole("button"));

    // THEN
    await waitFor(() => expect(mockAPI.personneService.rechercherPersonnes).toHaveBeenCalledTimes(1));
    await waitFor(() => expect(screen.getByText("James Bond")).toBeInTheDocument());
    expect(screen.getAllByRole("link").length).toBe(2);
    expect(screen.getByRole("link", { name: "James Bond" })).toHaveAttribute("href", "/personnes/800000000526");
  });
});
