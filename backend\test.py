import logging

from app.common.bootstrap import APPLICATION
from app.common.config import settings
from app.common.llms import get_llm
from app.domains.agents.langage_naturel import AgentLangageNaturel
from app.domains.agents.models import FormatExigence, TypeExigence, WorkflowIdentificationState

if __name__ == "__main__":
    print(settings)
    logging.basicConfig(level="INFO")
    APPLICATION.settings = settings
    agent = AgentLangageNaturel(
        llm=get_llm(temperature=0), llm_json=get_llm(temperature=0, model_kwargs={"response_format": {"type": "json_object"}})
    )

    exigence = TypeExigence(
        type=FormatExigence.TEXTE_LIBRE,
        identifiant="my-beautiful-exigence",
        data="""1300201660_FELOT1A.1_HUB_170 : HUB_FACTU – Appliquer la règle de "Facturation des fournisseurs occasionnels" sur la facturation entrante – C1 HAUDUROY, Louis 
En cas d'une "référence comptable de l'acheteur" fournie, le Hub de facturation doit vérifier si le centre de cout à l'origine de la demande existe bien chez Bytel et qu'il n'est pas fermé depuis 6 mois ou plus.



Le HUB doit MAJ l'état du traitement de la vérification :

=> On initie l'état du traitement de la vérification à "Vérification Référence Comptable Acheteur"



Si la "Référence comptable de l'acheteur" fournie

    Si le centre de cout à l'origine de la demande est existant chez Bytel (suite à l'appel du service SIEF.consulterCentreCout)

        Si la date locale est inférieure à la date de fin de validité ( date locale < dateFinValidité)

            Règle : OK

            Le traitement peut se poursuivre

        Sinon

            Si le centre est fermé depuis moins de 6 moins (date locale - dateFinValidité) < 6 mois

                Règle : OK

                Le traitement peut se poursuivre

            Sinon

                Règle : KO

                Motif refus : "Erreur avec Référence comptable de l'acheteur"

                Le traitement peut se poursuivre

            FinSi

        FinSi

    Sinon

        Règle : KO

        Motif refus : "Référence comptable de l'acheteur inconnue"

        Le traitement peut se poursuivre

    FinSi

Sinon

    Règle : NULL

    Le traitement peut se poursuivre

FinSi""",
    )

    data = WorkflowIdentificationState(exigences=[exigence.model_dump()])
    print(data.exigences[0])
    # WorkflowIdentificationState(tests=[a])
    result = agent.run(data)
    print(result.model_dump_json())
    # import json

    # from app.domains.identification.agents.tests.langage_naturel import EcritureDesTests

    # print(json.dumps(EcritureDesTests.model_json_schema(mode="serialization")))
