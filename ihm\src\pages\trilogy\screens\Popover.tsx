import React from 'react'
import {
  Section,
  Title,
  TitleLevels,
  Divider,
  Columns,
  ColumnsItem,
  Popover,
  Tag,
  TagVariant,
  PopoverDirection,
  PopoverArrowPosition,
  Button,
  Icon,
  IconName,
} from '@bytel/trilogy-react-ts'

export const PopoverScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={TitleLevels.THREE}>Popover</Title>
      <Divider />
      <Section>
        <Title level={TitleLevels.THREE}>Popover</Title>
        <Columns>
          <ColumnsItem>
            <Popover content='Voici une simple popover'>
              <Button variant={'PRIMARY'}>Simple</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover>
              <Button variant={'PRIMARY'}>Sans content</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover
              content={
                <>
                  <Tag variant={TagVariant.ERROR}>Test</Tag>
                  <Icon name={IconName.ACCESSORIES} />
                </>
              }
            >
              <Button variant={'PRIMARY'}>Node content</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover active content='Popover active'>
              <Button variant={'PRIMARY'}>Active</Button>
            </Popover>
          </ColumnsItem>
        </Columns>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>Popover direction</Title>
        <Columns>
          <ColumnsItem>
            <Popover content='En haut'>
              <Button variant={'PRIMARY'}>Top</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover direction={PopoverDirection.BOTTOM} content='En bas'>
              <Button variant={'PRIMARY'}>Bottom</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover direction={PopoverDirection.RIGHT} content='A droite'>
              <Button variant={'PRIMARY'}>Right</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover direction={PopoverDirection.LEFT} content='A gauche'>
              <Button variant={'PRIMARY'}>Left</Button>
            </Popover>
          </ColumnsItem>
        </Columns>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>
          Arrow Position (possiblement le css n est pas en dernière version, quand il sera à jour ça marchera ici)
        </Title>
        <Columns>
          <ColumnsItem>
            <Popover content='Centré'>
              <Button variant={'PRIMARY'}>Centré</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover arrowPosition={PopoverArrowPosition.START} content='Début'>
              <Button variant={'PRIMARY'}>Début</Button>
            </Popover>
          </ColumnsItem>
          <ColumnsItem>
            <Popover arrowPosition={PopoverArrowPosition.END} content='Fin'>
              <Button variant={'PRIMARY'}>Fin</Button>
            </Popover>
          </ColumnsItem>
        </Columns>
      </Section>
    </Section>
  )
}
