import { defineConfig, devices } from "@playwright/test";

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: "./e2e",
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: 1,
  /* Opt out of parallel tests on CI. */
  workers: "50%",
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [["junit", { outputFile: "junit.xml", outputDir: "." }], ["html"]],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: "http://localhost:5173",

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "on",
  },

  /* Configure projects for major browsers */
  projects: [
    // {
    //   name: "setup",
    //   testMatch: /.*\.setup\.ts/,
    //   use: {
    //     ...devices["Desktop Chrome"],
    //     ignoreHTTPSErrors: true,
    //   },
    // },
    {
      name: "chromium",
      use: {
        ...devices["Desktop Chrome"],
        // storageState: "e2e/.auth/user.json",
        ignoreHTTPSErrors: true,
      },
      // dependencies: ["setup"],
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: "npm run e2e:preview",
    url: "http://localhost:5173",
    reuseExistingServer: true,
    stderr: "pipe",
    stdout: "pipe",
  },
});
