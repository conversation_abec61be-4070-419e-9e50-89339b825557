import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from .models import UserStorage, UserUpdateInput


class UsersRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dao: UserStorage) -> UserStorage: ...
    @abstractmethod
    def find(self, login: str) -> UserStorage | None: ...
    @abstractmethod
    def update(self, dto: UserUpdateInput) -> UserStorage: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[UserStorage]: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> UserStorage | None: ...
