import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from .models import BouchonsApiFiltre, BouchonsApiStorage, BouchonsApiUpdate


class BouchonsApiRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dao: BouchonsApiStorage) -> BouchonsApiStorage: ...
    @abstractmethod
    def update(self, dto: BouchonsApiUpdate) -> BouchonsApiStorage: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> BouchonsApiStorage | None: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[BouchonsApiStorage]: ...
    @abstractmethod
    def find(self, filtre: BouchonsApiFiltre) -> Iterable[BouchonsApiStorage]: ...
