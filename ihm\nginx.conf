# log_format prisme_web
#     '{'
#         '"st":"portailvente_web",'
#         '"requestUri":"$request_uri",'
#         '"httpMethod":"$request_method",'
#         '"httpStatus":"$status",'
#         '"Location":"$sent_http_location",'
#         '"duree":$request_time,'
#         '"bytes_received":"$request_length",'
#         '"bytes_sent":"$body_bytes_sent",'
#         '"frontend":"$cookie_frontend",'
#         '"sso_active":"$cookie_SSO_ACTIVE",'
#         '"session_prisme":"$cookie_SESSION_PRISME",'
#         '"ctx":{'
#               '"vhost":"$server_name"'
#         '},'
#         '"headers":{'
#               '"userAgent":"$http_user_agent",'
#               '"referer":"$http_referer",'
#               '"true_client_ip":"$http_true_client_ip",'
#               '"x_forwarded_for":"$http_x_forwarded_for"'
#         '}'
#     '}';

server {
    listen       8080;

#     access_log /dev/stdout prisme_web;

    root             /usr/share/nginx/html/;

    set $location $request_uri;

    client_max_body_size 10m;
    client_body_timeout 120;

    location /health {
        access_log off;
        return 200;
    }

    location ~* ^.+\.(sh|conf|yml|md|sql|txt)$ {
        deny all;
    }

    location / {
        try_files $uri $uri/ /index.html;
    }

}
