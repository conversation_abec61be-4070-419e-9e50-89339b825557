---
title: Présentation
tags:
  - specificiations
hide_table_of_contents: true
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import TOCInline from '@theme/TOCInline';
import Mermaid from '@theme/Mermaid';
import {ApplicationName, identificationTests, C4model} from '@site/definitions';
import {Annoter} from '@site/components'


Ci-dessous une présentation générale de l'application {ApplicationName}. 
 
Vous allez y retrouvez les informations essentielles tels que sa mission et son architecture fonctionnelle.

<TOCInline toc={toc} />

### Mission de l'application

{ApplicationName} est une application dédiée à l'{identificationTests}. 
 
Elle a pour mission d'aider différentes populations d'utilisateurs à identifier 
plus rapidement leur besoins en tests de manière à établir un cahier de tests qui pourra être exporté et partagé avec d'autres utilisateurs.

Pour réaliser cela, elle va prendre en entrée des exigences qui peuvent être des média dans différents formats (texte, image, vidéo...) 
et utiliser de l'IA générative pour proposer des tests à réaliser.

Comme cette application s'adresse à des populations utilisateurs variées et utilise de l'IA générative, 
elle a aussi pour mission d'accompagner les utilisateurs en reprenants dans son fonctionnement les bonnes pratiques du test et les prévenir sur les risques.

Ci-dessous les populations d'utilisateurs identifiés


### Utilisateurs de l'application

L'application adresse différentes populations qui sont caractérisés ci-dessous.
Dans les définitions des besoins, ces catégories sont reprises afin de savoir à qui s'adresse certaines fonctionnalités et faciliter leur priorisation.

#### Utilisateurs cible

Les utilisateurs directement ciblé par l'application et sa valeur ajoutée (l'identification des tests).

##### Novices du Test

Cette population d'utilisateurs correspond aux personnes qui ont peu voir absolument pas d'expérience avec le test logiciel.
Ils ont besoin d'être accompagnés au fur à mesure de leur utilisation et nécessite un onboarding dédié.

> Exemples: 
> - Un métier à qui ont a demandé d'écrire des tests
> - Un nouvel arrivant qui n'a jamais fait de test


##### Expérimentés du test

Cette population d'utilisateurs a déjà une expérience significative dans le test et souhaite pouvoir rapidement itérer et aller à l'essentiel.
Ils ont des besoins liés à l'efficacité et s'attendent à des fonctionnalités avancés en accord avec leur expérience. De ce fait, 
c'est une population à laquelle il faut offrir une forte valeur ajoutée et qu'il ne faut pas frustrer à tort sous peine de la voir délaisser l'application.

> Exemples:
> - Un développeur qui veut gagner du temps sur l'implémentation de tests avec les outils qu'il utilise 
> - Un testeur qui veut gagner du temps sur la mise en place de la couverture de tests


#### Responsables

Les responsables de l'application sont les gestionnaires/administrateurs/exploitants... bref, les parties prenantes du développement/maintien de l'application dans le temps.
Ils n'utilisent pas directement l'application pour sa valeur ajoutée.

Les populations exacte étant liés à l'organisation de l'équipe, on ne va pas faire de distinction plus poussées.

On peut cependant noter qu'ils vont avoir des besoins de métrologies pour sortir des KPI
et vont intervernir sur le non-fonctionnel (sécurité, expérience utilisateur, coûts, supervision, infrastructure...) 



### Modes de fonctionnement

A date, il n'est pas prévu d'avoir différents modes de fonctionnement.
 
En effet, l'application repose principalement sur l'utilisation de LLM pour faire des suggestions à l'utilisateur.
De ce fait, une perte de cette connectivité ne laissera place qu'à de l'édition basique de cas de test ce qui n'est pas souhaitable.

Si les besoins d'édition/lecture sont conséquents, il serait envisageable d'avoir des modes d'édition (lecture seule, edition...) à terme. 

### Architecture générale de l'application

Ci-dessous les diagrames présentant les différentes parties de l'application et leur dépendances en s'appuyant sur la {C4model}.


<Tabs lazy>
<TabItem value="c4-system-context" label="Contexte Système">

<Mermaid
  value={`C4Context
        
        
        UpdateLayoutConfig($c4ShapeInRow="2", $c4BoundaryInRow="1")

        Boundary(tout, "Bouygues Telecom", $type="DSI"){
          Person(utilisateur, "Utilisateur cible", "Une personne en possession d'exigences à tester <br/> Il peut s'agir d'un developpeur, un testeur, un acteur métier...")
          Person(responsable, "Responsable de l'application", "Une personne devant administrer l'application et/ou en suivre les usages")
          System(Application, "${ApplicationName}", "Permet aux utilisateurs de définir leurs tests et les exporter")
          System_Ext(llm, "Plateforme LLM Bytel", "Permet d'utiliser différents modèles d'IA avec la confidentialité des données garantie")

        
        }
        Boundary(atlassianTools, "Ecosytème Atlassian", $type=" ") {
            Boundary(xpandit, "Xpandit", $type="ENTREPRISE") {
                System_Ext(xray, "Xray Test Management", "L'outil DSI choisi pour la gestion des tests")
            }
            Boundary(atlassian, "Atlassian", $type="ENTREPRISE") {
                System_Ext(jira, "Jira / Confluence", "La suite d'outils utilisé par Bytel pour communiquer sur la réalisation des projets DSI")
            }
            
        }

        
        Rel(utilisateur, Application, "utilise l'application pour identifier des tests", "Exigences")
        UpdateRelStyle(utilisateur, Application, $offsetY="0", $offsetX="-250")

        Rel(responsable, Application, "consulte et améliore le produit", "Usages")

        Rel(Application, xray, "Export", "Cas de Tests identifiés")
        UpdateRelStyle(Application, xray, $offsetY="-80", $offsetX="10")

        Rel(Application, llm, "dépends de")
        UpdateRelStyle(Application, llm, $offsetY="-20", $offsetX="-25")

        BiRel(jira, xray, "synchronisation")
        UpdateRelStyle(jira, xray, $offsetY="0", $offsetX="30")
        `}
/>

</TabItem>
<TabItem value="c4-container" label="Conteneur">

:::note

Le terme conteneur dans la modélisation des 4C correspond à une unité **deployable**

:::

<Mermaid
value={`C4Container
    UpdateLayoutConfig($c4ShapeInRow="1", $c4BoundaryInRow="2")

    Person(utilisateur, "Utilisateur", "Un utilisateur cible ou un responsable de l'application")
    Boundary(app, "${ApplicationName}", $type=" ") {
        Container(spa, "IHM Web interne", " Typescript / React / Single Page Application ", "donne accès aux différentes fonctionnalités de ${ApplicationName} aux utilisateurs depuis leur navigateur")
        Container(backend_api, "API Web", " Python / FastAPI / Docker Container ", "expose les différentes fonctionnalités de ${ApplicationName} via API")
        ContainerDb(database, "Base de données", "SQL / Postgres", "Persistance des données utiles")
    }

    Boundary(bytel, "Bytel", $type="services applicatifs communs"){

      System_Ext(authent, "SESAME", "Système d'authentification Oauth2 utilisé pour les IHM internes (demande des droits via B-IDENTITY)")
      System_Ext(llm, "Plateforme LLM Bytel", "Permet d'utiliser différents modèles d'IA avec la confidentialité des données garantie")
      System_Ext(logging_system, "PRISME", "Système de centralisation des logs applicatif")
      System_Ext(alerts_system, "B-WARNED", "Permet d'envoyer des alertes aux responsables de l'application")
    
    }



    Rel(utilisateur, spa, "utilise", "HTTPS")
    UpdateRelStyle(utilisateur, spa, $offsetY="-60")

    Rel(spa, authent, "authentifie l'utilisateur", "react-oauth2")
    UpdateRelStyle(spa, authent, $offsetX="-70", $offsetY="-40")

    BiRel(spa, backend_api, "communique avec", "async, JSON/HTTPS")
    UpdateRelStyle(spa, backend_api, $offsetX="10", $offsetY="0")

    Rel(backend_api, authent, "contrôle  les droits", "async, JSON/HTTPS")
    UpdateRelStyle(backend_api, authent, $offsetX="10", $offsetY="-10")

    Rel(backend_api, llm, "fait des générations via", "async, JSON/HTTPS")
    UpdateRelStyle(backend_api, llm, $offsetX="10", $offsetY="10")
  
    Rel(backend_api, database, "opération de lecture/écriture")
    UpdateRelStyle(backend_api, database, $offsetX="10", $offsetY="0")

`}/>


</TabItem>
<TabItem value="c4-component" label="Composant">

:::warning

Diagramme à mettre en place, une fois l'ensemble des composants identifiés, pour chaque conteneur

:::

</TabItem>

<TabItem value="c4-deploiement" label="Deploiement">

:::warning

Diagramme à mettre en place une fois les choix techniques définis

:::

</TabItem>

</Tabs>


### Suivi des décisions prises

Afin de faciliter l'onboarding, il a été décidé de mettre en place un ADR à ce wiki qui est disponible [içi](/ADR)