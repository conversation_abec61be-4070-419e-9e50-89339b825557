"""
les modèles de données pour liées à l'identification
"""

import uuid
from datetime import datetime
from enum import Enum

from langchain_core.documents import Document as langchain_document

# from langchain_core.documents import Document # voir TODO
from pydantic import BaseModel, conlist
from sqlmodel import Field, SQLModel

# from app.common import PydanticJson
from app.common.sqltypes import PydanticJSONB
from app.domains.agents.models import StatistiquesIA
from app.domains.bouchons.models import BouchonsApiInput
from app.domains.exigences.models import ExigenceOutput
from app.domains.testcases.models import TestCaseInput

# Modeles métiers


# modeles liés à l'identification et son usage
class PhotoEntrants(BaseModel):
    """
    les données figées qui vont servir de contexte d'entrée à l'agent IA
    """

    exigences: list[ExigenceOutput] = conlist(str, min_length=1)
    documents: list[langchain_document] | None = Field(None, description="la liste des documents contextuels à utiliser lors de l'identification")


# modeles liées à l'orchestration de la demande
class StatutsDemande(Enum):
    CREE = "crée"
    EN_COURS = "en cours"
    REUSSIE = "succès"
    EN_ERREUR = "erreur"


class Demande(BaseModel):
    """
    les modélisations liés à la gestion d'une demande traitée de manière asynchrone
    """

    statut: StatutsDemande = Field(default=StatutsDemande.CREE, description="l'état interne de la demande ce qui permet de suivre sa réalisation")
    date_creation: datetime = Field(default_factory=datetime.now, description="le moment où l'on a reçu la demande du client")
    date_debut: datetime | None = Field(None, description="le moment où on a démarré le traitement d'identification des tests par IA")
    date_fin: datetime | None = Field(None, description="le moment où le traitement s'est arrêté, qu'il soit réussi ou non")


class DemandeIdentification(Demande):
    """
    Correspond à une demande de génération via IA qui va être orchestrée et réalisée.
    Une demande est liée à un workspace de manière à ce que l'on empêche d'enchainer les générations alors qu'une serait en cours.
    """

    demandeur_id: uuid.UUID
    workspace_id: uuid.UUID
    stats: StatistiquesIA | None = Field(None, description="les métadonnées d'utilisation IA liées à la génération de la demande")


# Storage


class DemandeIdentificationStorage(SQLModel, table=True):
    """
    le modèle persisté en base de données pour les demandes d'identifications
    """

    __tablename__ = "identifications"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(foreign_key="workspaces.id")
    demandeur_id: uuid.UUID = Field(foreign_key="utilisateurs.id")
    date_creation: datetime = Field(default_factory=datetime.now, description="le moment où l'on a reçu la demande du client")
    date_debut: datetime | None = Field(None, description="le moment où on a démarré le traitement d'identification des tests par IA")
    date_fin: datetime | None = Field(None, description="le moment où le traitement s'est arrêté, qu'il soit réussi ou non")
    statut: StatutsDemande
    entrants: PhotoEntrants = Field(sa_type=PydanticJSONB(PhotoEntrants))
    stats: StatistiquesIA | None = Field(default=None, sa_type=PydanticJSONB(StatistiquesIA))


# DTO


class DemandeIdentificationFiltre(BaseModel):
    """
    le filtre de recherche possible pour trouver des demande d'identifications
    """

    demandeur_id: uuid.UUID | None = None
    workspace_id: uuid.UUID | None = None
    statuts: list[StatutsDemande] | None = Field(None, description="les états internes de la demande à rechercher, si indiqué")
    debut: datetime | None = Field(None, description="permet de chercher des demande à partir de cette date si indiquée")
    fin: datetime | None = Field(None, description="permet de chercher des demande jusqu'à cette date si indiquée")
    ordre: str = Field("desc", description="permet de trier les résultats par date (asc/desc)")


class DemandeIdentificationInput(BaseModel):
    """
    le modèle de données lorsque l'utilisateur demande une identification
    """

    # demandeur_id: uuid.UUID
    workspace_id: uuid.UUID = Field(description="l'identifiant de l'espace de travail sur lequel on va identifier les tests et bouchons")
    exigences: list[uuid.UUID] | None = Field(None, min_length=1)
    documents: list[uuid.UUID] | None = Field(None, description="la liste des documents contextuels à utiliser lors de l'identification")


class DemandeIdentificationUpdate(BaseModel):
    id: uuid.UUID
    statut: StatutsDemande | None = None
    date_debut: datetime | None = None
    date_fin: datetime | None = None
    stats: StatistiquesIA | None = None


class DemandeIdentificationOutput(DemandeIdentification):
    """
    le modèle de données renvoyé suite à la prise en compte de la demande
    """

    id: uuid.UUID
    # entrants: PhotoEntrants


class DemandeIdentificationOutputEnrichi(DemandeIdentificationOutput):
    """
    le modèle de données renvoyé suite à la prise en compte de la demande
    """

    workspace_name: str | None = None
    demandeur_name: str | None = None


# Evenements de mise à jour de la demande


class IdentificationEnCours(BaseModel):
    """
    mise à jour de la demande lorsque le traitement d'identification démarre.
    """

    id: uuid.UUID
    statut: StatutsDemande = StatutsDemande.EN_COURS
    date_debut: datetime = Field(default_factory=datetime.now)


class IdentificationReussie(BaseModel):
    """
    mise à jour de la demande lorsque celle-ci est terminée avec succès
    """

    id: uuid.UUID
    stats: StatistiquesIA
    statut: StatutsDemande = StatutsDemande.REUSSIE
    date_fin: datetime = Field(default_factory=datetime.now)


class IdentificationEchouee(BaseModel):
    """
    mise à jour de la demande lorsque celle-ci est terminée en erreur
    """

    id: uuid.UUID
    statut: StatutsDemande = Field(default=StatutsDemande.EN_ERREUR)
    date_fin: datetime = Field(default_factory=datetime.now)


# les données en entrée et sortie de l'agent IA d'identification
class AgentIdentificationInput(BaseModel):
    """
    les données nécessaires en entrée pour que l'agent IA réalise l'identification des tests et/ou bouchons
    """

    demande: IdentificationEnCours
    exigences: list[ExigenceOutput] = conlist(str, min_length=1)
    documents: list[langchain_document] | None = Field(None, description="la liste des documents contextuels à utiliser lors de l'identification")


class AgentIdentificationOutput(BaseModel):
    """
    les données renvoyées par l'agent IA suite à l'identification des tests et/ou bouchons.
    On ne garde pas les exigences et documents pour limiter la taille de la réponse.
    """

    demande: IdentificationReussie | IdentificationEchouee
    tests: list[TestCaseInput] | None = None
    bouchons: list[BouchonsApiInput] | None = None
