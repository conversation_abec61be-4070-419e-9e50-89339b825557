import logging
import uuid

from app.common.exceptions import InvalidContentException
from app.domains.exigences.models import FormatExigence
from app.domains.exigences.repository import ExigenceRepositoryInterface

from .models import BouchonsApiFiltre, BouchonsApiInput, BouchonsApiOutput, BouchonsApiStorage, CasUsageApiStorage, ImplementationBouchonsStorage
from .repository import BouchonsApiRepositoryInterface

# TODO: voir comment on veut gérer plusieurs générations d'affilées sur une même spécification OpenAPI


class BouchonsOpenApiService:
    """
    le service de gestion des bouchons liés à une spécification OpenAPI.
    Par dé<PERSON>ult, on considère que les bouchons peuvent être accessibles à tous
    vu que les specifications OpenAPI ne sont pas confidentielles (contrairement aux exigences en saisie libre).
    """

    def __init__(self, exigences: ExigenceRepositoryInterface, bouchons: BouchonsApiRepositoryInterface):
        self._repo_exigences = exigences
        self._repo_bouchons = bouchons
        self.log = logging.getLogger(__name__)

    def recuperer_bouchons_exigence(self, exigence_id: uuid.UUID) -> list[BouchonsApiOutput]:
        self.log.info("récupération des bouchons pour l'exigence %s", exigence_id)
        bouchons_services = self._repo_bouchons.find(BouchonsApiFiltre(exigences_ids=[exigence_id]))
        self.log.info("%i générations de bouchons récupérées", len(bouchons_services))
        return bouchons_services

    def ajouter_bouchons(self, bouchons_service: BouchonsApiInput) -> None | InvalidContentException:
        """
        persiste les bouchons générés par IA
        """
        self.log.info("sauvegarde des bouchons générés pour la spécification Open API %s", bouchons_service.exigence_id)
        self._controler_exigence(bouchons_service.exigence_id)
        self.log.debug("sérialisation DAO...")
        try:
            self.log.debug("sérialisation des CasUsageApiStorage...")
            usecases = [CasUsageApiStorage(**usecase.model_dump()) for usecase in bouchons_service.usecases]
            self.log.debug("sérialisation ImplementationBouchonsStorage...")
            implementations = [ImplementationBouchonsStorage(**implem.model_dump()) for implem in bouchons_service.implementations]
            self.log.debug("sérialisation BouchonsApiStorage...")
            dao = BouchonsApiStorage(
                exigence_id=bouchons_service.exigence_id,
                usecases=usecases,
                implementations=implementations,
                date_generation=bouchons_service.date_generation,
            )
        except Exception as error:
            print(error)
            self.log.fatal(error)
            raise error
        self.log.debug("lancement de la sauvegarde...")
        created = self._repo_bouchons.add(dao)
        self.log.info("sauvegarde réussie %s", created.id)

    def _controler_exigence(self, exigence_id: uuid.UUID) -> None | InvalidContentException:
        """
        vérifie que l'exigence indiquée correspond bien à une exigence de type Open API
        """
        self.log.info("contrôle de l'exigence %s", exigence_id)
        self.log.debug("récupération de l'exigence...")
        exigence = self._repo_exigences.get(exigence_id)
        self.log.debug("exigence trouvée: %s", exigence)
        if exigence and exigence.type == FormatExigence.OPEN_API:
            return
        raise InvalidContentException(error_reason=f"l'exigence {exigence_id} n'existes pas ou n'est pas une exigence Open API !")
