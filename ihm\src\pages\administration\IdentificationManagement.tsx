import React, { useState, useEffect } from 'react';
import { Box, Button, Text } from '@bytel/trilogy-react-ts';
import IdentificationTable from './components/IdentificationTable';
import IdentificationFiltre from './components/IdentificationFiltre';
import { SelectChangeEvent } from '@bytel/trilogy-react-ts/lib/components/select/SelectProps';

import { DemandeIdentificationOutput,DemandeIdentificationFiltre } from '../../types/identification';
import { useAPIClient } from '../../providers/api';
import CustomLoader from '../../components/CustomLoader';

const IdentificationManagement: React.FC = () => {
  const [identifications, setIdentifications] = useState<DemandeIdentificationOutput[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isActionPending, setIsActionPending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const api = useAPIClient();
  const [currentStatus, setCurrentStatus] = useState<string>("erreur");
  const [demandeIdentificationFiltre, setDemandeIdentificationFiltre] = useState<DemandeIdentificationFiltre>({
    statuts: ["erreur"]
  });

  const [searchTerm, setSearchTerm] = useState ( '' ); 
  const filteredData = identifications.filter(identification => 
    identification.statut.includes(searchTerm.toLowerCase()) || 
    identification.demandeur_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    identification.workspace_name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  const fetchIdentifications = async () => {
    setIsLoading(true);
    try {
      const data = await api.adminService.listIdentifications(demandeIdentificationFiltre);
      console.log('Identifications récupérées:', data);
      setIdentifications(data);
      setError(null);
    } catch (err) {
      console.error('Erreur lors du chargement des identifications:', err);
      setError('Erreur lors du chargement des identifications');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchIdentifications();
  }, [demandeIdentificationFiltre]);

  const handleCancelIdentification = async (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir annuler cette identification ?')) {
      setIsActionPending(true);
      try {
        await api.adminService.cancelIdentification(id);
        fetchIdentifications();
      } catch (err) {
        console.error('Erreur lors de l\'annulation:', err);
        setError('Erreur lors de l\'annulation de l\'identification');
      } finally {
        setIsActionPending(false);
      }
    }
  };

  const handleStatusChange = async (status: string | SelectChangeEvent) => {
    let statusValue: string;
    
    // Gérer à la fois string et SelectChangeEvent
    console.log("typeof status:",typeof status);
    if (typeof status === 'string') {
      statusValue = status;
    } else {
      statusValue = status.selectValue || '';
    }
  
    setCurrentStatus(statusValue);
    
    const newFiltre = {
      ...demandeIdentificationFiltre,
      statuts: statusValue === "Tout" ? [] : [statusValue]
    };
    
    setDemandeIdentificationFiltre(newFiltre);

  };

  return (
    <Box className="box-admin">
      <Box className="d-flex justify-content-between mb-4 align-items-center">
        <Text>Gestion des Identifications</Text>
        <Button 
          variant="TERTIARY"
          onClick={fetchIdentifications}
          disabled={isLoading || isActionPending}
        >
          {isActionPending ? (
            <>
              <CustomLoader size="small" className="mr-2" /> 
              Traitement en cours...
            </>
          ) : (
            'Rafraîchir'
          )}
        </Button>
      </Box>

      {error && (
        <Box className="alert alert-danger mb-4">
          {error}
        </Box>
      )}
      <div className="table-responsive div-table-responsive-admin {">
        <IdentificationFiltre  
          searchTerm={searchTerm}
          currentStatus={currentStatus}
          onChangeSearchTerm={setSearchTerm}
          onChangeFiltreStatus={handleStatusChange}

        />
        <IdentificationTable 
          identifications={filteredData} 
          isLoading={isLoading}
          onCancelIdentification={handleCancelIdentification}
        />
        
      </div>
    </Box>
  );
};

export default IdentificationManagement;