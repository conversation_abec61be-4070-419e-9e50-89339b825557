# creer un volume bdd-catia : 
# podman volume create bdd-catia
# lancer la bdd postgres
# podman compose up


services:

  bdd:
    container_name: catia-bdd
    image: registry.glouton.int.nbyt.fr/postgres:15
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: catia
      POSTGRES_DB: catia
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    env_file:
      - .env

# TODO: a voir si utile de faire du build d'image en local ou si les tasks preview sont suffisantes
  # apiweb:
    # image: catia/apiweb
    # build:
    #   context: contextPath
    #   dockerfile: Dockerfile