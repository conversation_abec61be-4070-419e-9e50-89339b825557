import { FeedbackOutput } from '../services/feedbackService';
import { Workspace } from '../services/workspaceService';
import { Requirement } from '../services/interfaces/requirementInterfaces';
import { Generation } from '../services/interfaces/generationInterfaces';
import { UserOutput } from './user';

export interface FeedbackWithDetails extends FeedbackOutput {
  workspace?: Workspace | null;
  exigence?: Requirement | null;
  generation?: Generation | null;
  user?: UserOutput | null;
}