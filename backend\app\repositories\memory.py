import uuid
from datetime import datetime

from pydantic import BaseModel

from app.common.config import AppRepos
from app.domains.exigences.repository import ExigenceFiltre, ExigenceRepositoryInterface, ExigenceStorage
from app.domains.users.repository import UsersRepositoryInterface, UserStorage
from app.domains.workspace.repository import WorkspaceRepositoryInterface, WorkspaceStorage
from app.repositories import BaseRepository


def init_memory_storage(storage: AppRepos):
    storage.users = MemoryUserRepository()
    storage.workspaces = MemoryWorkspaceRepository()
    storage.exigences = MemoryExigenceRepository()


class Entity(BaseModel):
    id: uuid.UUID


class MemoryBaseRepository(BaseRepository):
    def __init__(self):
        self.entities: list[Entity] = []

    def add(self, dao: Entity):
        self.entities.append(dao)
        return dao

    def get(self, entity_id: uuid.UUID):
        for entity in self.entities:
            if entity.id == entity_id:
                return entity

    def update(self, update_dto: Entity):
        for entiy in self.entities:
            if entiy.id == update_dto.id:
                entiy = entiy.model_copy(update=update_dto.model_dump(exclude_unset=True))
                return entiy

    def list(self, limit: int = None):
        return self.entities[:limit]


# TODO: à basculer dans les tests
FAKE_ID = uuid.UUID("ccd9907a-3510-46f5-b77d-ca00f811a031")


class MemoryUserRepository(MemoryBaseRepository, UsersRepositoryInterface):
    def __init__(self):
        self.entities: list[UserStorage] = []
        self.entities.append(UserStorage(login="admin", actif=False, date_inscription=datetime.now(), admin=True, id=FAKE_ID))

    def find(self, login):
        for user in self.entities:
            if user.login == login:
                return user


class MemoryWorkspaceRepository(MemoryBaseRepository, WorkspaceRepositoryInterface):
    def __init__(self):
        self.entities: list[WorkspaceStorage] = []
        self.entities.append(
            WorkspaceStorage(
                nom="the memory workspace", description="un workspace contenu en ram qui disparaitra", date_creation=datetime.now(), owner_id=FAKE_ID
            )
        )

    def find(self, owner_id):
        found = [workspace for workspace in self.entities if workspace.owner_id == owner_id]
        return found


class MemoryExigenceRepository(MemoryBaseRepository, ExigenceRepositoryInterface):
    def __init__(self):
        self.entities: list[ExigenceStorage] = []
        self.entities.append(
            ExigenceStorage(
                nom="Exigence test",
                description="Une exigence de test en mémoire",
                type="FUNCTIONAL",
                data="Test data",
                date_creation=datetime.now(),
                owner_id=FAKE_ID,
                ws_id=FAKE_ID,  # Lien vers le workspace mock
            )
        )

    def find(self, filtre: ExigenceFiltre) -> list[ExigenceStorage]:
        found = []
        if filtre.ws_id:
            # a le moins d'éléments à itérer et on a qu'un seul user par workspace pour l'instant
            found = [exigence for exigence in self.entities if exigence.ws_id == filtre.ws_id]
        elif filtre.owner_id:
            found = [exigence for exigence in self.entities if exigence.owner_id == filtre.owner_id]
        return found
