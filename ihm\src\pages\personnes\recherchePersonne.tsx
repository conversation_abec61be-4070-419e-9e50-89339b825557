import { Box, Button, Container, Input, Loader, Text } from "@bytel/trilogy-react-ts";
import React from "react";
import { useAPIClient } from "providers/api";

import { useSnackbar } from "providers/snackbar";
import { AxiosError } from "axios";
import { Individu as IndividuRecherche } from "services/interfaces/generated/HELLOWORLD-FORMATION/rechercherPersonnes.ts";
import { Link } from "react-router-dom";
import { ApiButton } from "components/ApiButton";

export const RecherchePersonnesPage: React.FC = () => {
  const [inputValue, setInputValue] = React.useState<string>("");
  const [listePersonnes, setListePersonnes] = React.useState<IndividuRecherche[] | null>(null);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const { showError } = useSnackbar();
  const { personneService } = useAPIClient();

  // On focus sur l'input
  React.useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const rechercherPersonnes = async () => {
    try {
      setIsLoading(true);
      setListePersonnes(null);
      const liste = await personneService.rechercherPersonnes(inputValue);
      console.log(liste);
      setListePersonnes(liste);
    } catch (e) {
      console.error("Erreur lors de la recherche des personnes", e);
      showError(e as AxiosError, {
        message: "Erreur lors de la recherche des personnes",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container className={"body-recherche-personnes"}>
      <Box>
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.inputValue)}
          onKeyUp={(e) => {
            if (e.inputKeyCode === 13) {
              return rechercherPersonnes();
            }
          }}
          testId="RecherchePersonnesPage.Input"
        />
        <Button onClick={() => rechercherPersonnes()} id="RecherchePersonnesPage.Button">Rechercher</Button>
      </Box>
      {isLoading && (
        <Box>
          <Loader />
        </Box>
      )}
      {listePersonnes && listePersonnes.length === 0 && <Box>Aucune personne trouvée</Box>}
      {listePersonnes?.map((personne) => {
        return <LignePersonne personne={personne} key={personne.idPersonneUnique} />;
      })}
    </Container>
  );
};

const LignePersonne: React.FC<{ personne: IndividuRecherche }> = ({ personne }) => {
  return (
    <Link to={`/personnes/${personne.idPersonneUnique}`} key={personne.idPersonneUnique}>
      <Box className={"infos-personne"}>
        {personne.prenom} {personne.nom}
      </Box>
    </Link>
  );
};
