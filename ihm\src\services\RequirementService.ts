import { Service } from "./commonService";
import { Requirement, ExtractedDocUnique } from "services/interfaces/requirementInterfaces";
import { BQuery } from "@bytel/query";

export class RequirementService extends Service {
  constructor(bquery: BQuery) {
    super(bquery);
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON> toutes les exigences d'un workspace
   * @param ws_id L'identifiant du workspace
   */
  async getRequirementsByWorkspaceId(ws_id: string): Promise<Requirement[]> {
    try {
      const response = await this.bquery.get(`/workspaces/${ws_id}/exigences`);
      return response as Requirement[];
    } catch (error) {
      console.error(`Failed to fetch workspace with id ${ws_id}:`, error);
      return [];
    }
  }

  /**
   * Crée une nouvelle exigence dans un workspace
   * @param nom Le nom de l'exigence
   * @param description La description de l'exigence
   * @param type Le type de l'exigence
   * @param data Les données de l'exigence
   * @param ws_id L'identifiant du workspace
   */
  async createRequirement(
    nom: string,
    description: string,
    type: string,
    data: string,
    ws_id: string
  ): Promise<void> {
    try {
      const body = {
        nom,
        description,
        type,
        data,
        ws_id
      };

      await this.bquery.post(`/exigences`, body);
      console.log(`Requirement created successfully in workspace ${ws_id}.`);
    } catch (error) {
      console.error('Failed to create requirement:', error);
    }
  }

  /**
   * Récupère une exigence par son identifiant
   * @param id L'identifiant de l'exigence
   */
  async getRequirement(id: string): Promise<Requirement | null> {
    try {
      const response = await this.bquery.get(`/exigences/${id}`);
      return response as Requirement;
    } catch (error) {
      console.error(`Failed to fetch requirement with id ${id}:`, error);
      return null;
    }
  }

  /**
   *  Permet d'extraire les exigences contenues dans une documentation unique
   * @param pageId l'id de la page confluence contenant la documentation unique
   * @returns les exigences extraites depuis les pages de la documentation unique
   */
  async extractDocUnique(pageId: string): Promise<ExtractedDocUnique> {
    return this.bquery.get(`/extraction/du?pageId=${pageId}`)
  }
}
