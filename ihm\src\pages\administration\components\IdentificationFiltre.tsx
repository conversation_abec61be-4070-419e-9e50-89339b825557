import React from "react";
import { Input, Columns, ColumnsItem, Select, SelectOption } from "@bytel/trilogy-react-ts";

interface IdentificationFiltreProps {
  searchTerm: string;
  currentStatus: string;
  onChangeSearchTerm: (searchTerm: string) => void;
  onChangeFiltreStatus: (status: string) => void;
}

const IdentificationFiltre: React.FC<IdentificationFiltreProps> = ({ searchTerm, currentStatus, onChangeSearchTerm, onChangeFiltreStatus }) => {
  return (
    <>
      {
        <Columns multiline>
          <ColumnsItem size={8}>
            <Input type="text" placeholder="Rechercher" className="mb-4 p-2 border border-gray-300 rounded" value={searchTerm} onChange={(e) => onChangeSearchTerm(e.inputValue)} />
          </ColumnsItem>
          <ColumnsItem size={4}>
            <Select
              label="Statut"
              name="Statut"
              selected={currentStatus}
              onChange={(value) => {
                if (typeof value === "object" && "selectValue" in value) {
                  onChangeFiltreStatus(value.selectValue || "");
                } else {
                  onChangeFiltreStatus(value as string);
                }
              }}
            >
              <SelectOption value="Tout">Tout</SelectOption>
              <SelectOption value="crée">crée</SelectOption>
              <SelectOption value="erreur">erreur</SelectOption>
              <SelectOption value="en cours">en cours</SelectOption>
              <SelectOption value="succès">succès</SelectOption>
            </Select>
          </ColumnsItem>
        </Columns>
      }
    </>
  );
};

export default IdentificationFiltre;
