import React from 'react'
import { Spacer, SpacerSize, IconName, Link, Section, Title, TextLevels, Text } from '@bytel/trilogy-react-ts'

export const LinkScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={'ONE'}>Liens dans un text</Title>
      <Text>
        Je suis dans un paragraphe et ceci est un <Link>lien standard</Link> tandis que ceci est un
        <Link plain> lien standard non souligné</Link>. Si je souhaite un lien de couleur tertiaire, je peux également
        utiliser ce <Link tertiary>lien souligné</Link>, ou bien
        <Link plain tertiary>
          celui-ci non souligné
        </Link>
        .
      </Text>
      <Spacer size={SpacerSize.LARGE} />
      <Title level={'ONE'}>Liens vers page externe</Title>
      <Link iconName={IconName.NEW_TABBED_PAGE} level={TextLevels.FOUR} href='https://google.com' blank>
        En savoir plus
      </Link>

      <Link href='https://google.com' iconName={IconName.NEW_TABBED_PAGE}>
        En savoir plus
      </Link>
    </Section>
  )
}
