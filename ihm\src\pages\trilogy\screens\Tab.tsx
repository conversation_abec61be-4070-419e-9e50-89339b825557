import React from 'react'
import { Button, ButtonColor, Section, Tabs, TabsItem, Title, TitleLevels } from '@bytel/trilogy-react-ts'

export const TabScreen = (): JSX.Element => {
  const [activeIndexEventTab, setActivateIndexEventTab] = React.useState<number>(1)
  const [index, setIndex] = React.useState<number>(2)

  return (
    <Section>
      <Title level={TitleLevels.THREE}>Event</Title>

      <Tabs activeIndex={activeIndexEventTab}>
        <TabsItem
          disabled
          iconName='tri-sim-card'
          active={activeIndexEventTab === 0}
          onClick={() => setActivateIndexEventTab(0)}
        >
          One
        </TabsItem>
        <TabsItem
          iconName='tri-sim-card'
          active={activeIndexEventTab === 1}
          onClick={() => setActivateIndexEventTab(1)}
        >
          Two
        </TabsItem>
        <TabsItem
          iconName='tri-sim-card'
          active={activeIndexEventTab === 2}
          onClick={() => setActivateIndexEventTab(2)}
        >
          Three
        </TabsItem>
      </Tabs>

      <Tabs activeIndex={activeIndexEventTab} inverted>
        <TabsItem
          disabled
          iconName='tri-sim-card'
          active={activeIndexEventTab === 0}
          onClick={() => setActivateIndexEventTab(0)}
        >
          One
        </TabsItem>
        <TabsItem
          iconName='tri-sim-card'
          active={activeIndexEventTab === 1}
          onClick={() => setActivateIndexEventTab(1)}
        >
          Two
        </TabsItem>
        <TabsItem
          iconName='tri-sim-card'
          active={activeIndexEventTab === 2}
          onClick={() => setActivateIndexEventTab(2)}
        >
          Three
        </TabsItem>
      </Tabs>

      <Tabs activeIndex={index}>
        <TabsItem disabled iconName='tri-sim-card' active={index === 1} onClick={() => setIndex(1)}>
          Ceci est litem 1
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 2} onClick={() => setIndex(2)}>
          Ceci est litem 2
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 3} onClick={() => setIndex(3)}>
          Ceci est litem 3
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 4} onClick={() => setIndex(4)}>
          Ceci est litem 4
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
      </Tabs>

      <Tabs activeIndex={index} inverted>
        <TabsItem disabled iconName='tri-sim-card' active={index === 1} onClick={() => setIndex(1)}>
          Ceci est litem 1
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 2} onClick={() => setIndex(2)}>
          Ceci est litem 2
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 3} onClick={() => setIndex(3)}>
          Ceci est litem 3
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 4} onClick={() => setIndex(4)}>
          Ceci est litem 4
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem iconName='tri-sim-card' active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
      </Tabs>

      <Tabs activeIndex={index}>
        <TabsItem disabled active={index === 1} onClick={() => setIndex(1)}>
          Ceci est litem 1
        </TabsItem>
        <TabsItem active={index === 2} onClick={() => setIndex(2)}>
          Ceci est litem 2
        </TabsItem>
        <TabsItem active={index === 3} onClick={() => setIndex(3)}>
          Ceci est litem 3
        </TabsItem>
        <TabsItem active={index === 4} onClick={() => setIndex(4)}>
          Ceci est litem 4
        </TabsItem>
        <TabsItem active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
      </Tabs>

      <Tabs activeIndex={index} inverted>
        <TabsItem disabled active={index === 1} onClick={() => setIndex(1)}>
          Ceci est litem 1
        </TabsItem>
        <TabsItem active={index === 2} onClick={() => setIndex(2)}>
          Ceci est litem 2
        </TabsItem>
        <TabsItem active={index === 3} onClick={() => setIndex(3)}>
          Ceci est litem 3
        </TabsItem>
        <TabsItem active={index === 4} onClick={() => setIndex(4)}>
          Ceci est litem 4
        </TabsItem>
        <TabsItem active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
        <TabsItem active={index === 5} onClick={() => setIndex(5)}>
          Ceci est litem 5
        </TabsItem>
      </Tabs>

      <Title level={TitleLevels.THREE}>controlled</Title>

      <Tabs>
        <TabsItem
          disabled
          iconName='tri-sim-card'
          active={activeIndexEventTab === 0}
          onClick={() => setActivateIndexEventTab(0)}
        >
          One
        </TabsItem>
        <TabsItem active={activeIndexEventTab === 1} onClick={() => setActivateIndexEventTab(1)}>
          Two
        </TabsItem>
        <TabsItem active={activeIndexEventTab === 2} onClick={() => setActivateIndexEventTab(2)}>
          Three
        </TabsItem>
        <TabsItem active={activeIndexEventTab === 3} onClick={() => setActivateIndexEventTab(3)}>
          Four
        </TabsItem>
        <TabsItem active={activeIndexEventTab === 4} onClick={() => setActivateIndexEventTab(4)}>
          Five
        </TabsItem>
      </Tabs>

      <Button
        variant={ButtonColor.SECONDARY}
        onClick={() => {
          setActivateIndexEventTab(2)
        }}
      >
        change active to Three
      </Button>
    </Section>
  )
}
