import { BQuery } from "@bytel/query";
import * as React from "react";
import APIClient from "../services/api";
import { useConfig } from "./config";
import { useLogger } from "./logger";

const APIContext = React.createContext<APIClient | null>(null);

export const APIProvider: React.FC<React.PropsWithChildren<{ mockAPI?: APIClient }>> = ({ children, mockAPI }) => {
  const logger = useLogger();
  const config = useConfig();

  const instanceAPI = React.useMemo(() => {
    if (mockAPI) return mockAPI;

    return new APIClient(
      new BQuery(
        {
          baseURL: config.API_URL,
        },
        logger,
      ),
    );
  }, [mockAPI, config.API_URL, logger]);

  return <APIContext.Provider value={instanceAPI}>{children}</APIContext.Provider>;
};

export const useAPIClient = (): APIClient => {
  return React.useContext(APIContext) as APIClient;
};
