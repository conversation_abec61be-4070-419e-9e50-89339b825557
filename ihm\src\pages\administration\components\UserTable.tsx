import React from "react";
import { <PERSON>, But<PERSON> } from "@bytel/trilogy-react-ts";
import { UserOutput } from "../../types/user";

interface UserTableProps {
  users: UserOutput[];
  isLoading: boolean;
  onMakeAdmin: (login: string) => void;
  onRemoveAdmin: (login: string) => void;
}

const UserTable: React.FC<UserTableProps> = ({ users, isLoading, onMakeAdmin, onRemoveAdmin }) => {
  if (isLoading) {
    return (
      <Box className="d-flex justify-content-center p-4">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </Box>
    );
  }

  if (users.length === 0) {
    return (
      <Box className="p-2">
        <p>Aucun utilisateur trouvé</p>
      </Box>
    );
  }

  return (
    <div className="table-responsive div-table-responsive-admin {">
      <table className="table table-admin">
        <thead>
          <tr>
            <th>Login</th>
            <th>Date d'inscription</th>
            <th>Admin</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {users.map((user) => (
            <tr>
              <td>{user.login}</td>
              <td>{new Date(user.date_inscription).toLocaleDateString()}</td>
              <td>{user.admin ? "Oui" : "Non"}</td>
              <td>
                {!user.admin && (
                  <Button variant="INFO" onClick={() => onMakeAdmin(user.login)}>
                    Promouvoir admin
                  </Button>
                )}
                {user.admin && (
                  <Button variant="DANGER" onClick={() => onRemoveAdmin(user.login)}>
                    Retirer admin
                  </Button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default UserTable;
