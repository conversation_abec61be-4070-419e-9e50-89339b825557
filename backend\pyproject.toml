[project]
name = "CATIA"
version = "0.1.0"
# description = "Add your description here"
# readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi[standard]>=0.115.12",
    "opentelemetry-instrumentation-fastapi>=0.52b1",
    "pydantic-settings>=2.8.1",
    "pyjwt[crypto]>=2.10.1",
    "sqlmodel>=0.0.24",
    "starlette-exporter>=0.23.0",
    "pytest",
    "asgi-correlation-id>=4.3.4",
    "langchain>=0.3.23",
    "langgraph>=0.3.30",
    "langchain-openai>=0.3.13",
    "langchain-community>=0.3.21",
    "openapi3-parser>=1.1.21",
    "colorlog>=6.9.0",
    "art>=6.5",
    "pandas>=2.2.3",
    "psycopg2>=2.9.10",
    "docx2txt>=0.9",
    "pypdf>=5.6.0",
    "atlassian-python-api>=4.0.4",
    "cleantext>=1.1.4",
    "html-to-markdown>=1.3.3",
]

[[tool.uv.index]]
name = "glouton"
url = "https://glouton.int.nbyt.fr/artifactory/api/pypi/pypi/simple"
default = true

[dependency-groups]
dev = [
    "pip-system-certs>=4.0",
    "pysonar>=1.0.1.1548",
    "pytest-cov>=6.1.1",
    "pytest-testdox>=3.1.0",
    "ruff>=0.11.2",
]

[tool.ruff]
target-version = "py310"
line-length = 150


[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "B008",  # do not perform function calls in argument defaults
    "B904",  # Allow raising exceptions without from e, for HTTPException
]


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.pytest.ini_options]
addopts = "-ra -q --force-testdox --cov=app --cov-report html --cov-report xml --cov-report term:skip-covered"
pythonpath = [".", "backend"]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.sonar]
# https://docs.sonarsource.com/sonarqube-server/10.8/analyzing-source-code/scanners/sonarscanner-for-python/#with-a-pyprojecttoml-file

projectKey="CATIA-apiweb"
sources="app"
host.url="https://sonarqube.int.nbyt.fr"
