import React from "react";
import { <PERSON><PERSON>, <PERSON>, ModalTitle, <PERSON><PERSON><PERSON>ooter, ModalMarkup, Button } from "@bytel/trilogy-react-ts";
import { FeedbackWithDetails } from "../../../types/feedback";

interface FeedbackDetailsModalProps {
  feedback: FeedbackWithDetails | null;
  isOpen: boolean;
  onClose: () => void;
}

const FeedbackDetailsModal: React.FC<FeedbackDetailsModalProps> = ({
  feedback,
  isOpen,
  onClose,
}) => {
  if (!feedback) return null;

  return (
    <Modal active={isOpen} triggerMarkup={ModalMarkup.BUTTON} closeIcon onClose={onClose}>
      <ModalTitle>Commentaire du feedback</ModalTitle>
      <Box>
        <pre
          style={{
            backgroundColor: "#f5f5f5",
            padding: "15px",
            borderRadius: "4px",
            maxHeight: "400px",
            overflowY: "auto",
            whiteSpace: "pre-wrap",
            fontSize: "14px"
          }}
        >
          {feedback.comment || 'Aucun commentaire'}
        </pre>
      </Box>
      <ModalFooter>
        <Button variant="SECONDARY" onClick={onClose}>
          Fermer
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default FeedbackDetailsModal;