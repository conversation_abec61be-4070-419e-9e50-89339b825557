import React from 'react'
import { Section, Title, TitleLevels, Divider, Box, ButtonMarkup, Notification, IconName } from '@bytel/trilogy-react-ts'
import { AlertState } from '@bytel/trilogy-react-ts'

export const NotificationScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={TitleLevels.THREE}>Notifications</Title>
      <Divider />
      <Title level={TitleLevels.THREE}>Version simple</Title>
      <Box>
        <Notification title='Notification' />
      </Box>
      <Divider />
      <Title level={TitleLevels.THREE}>Version simple sans icône</Title>
      <Box>
        <Notification title='Notification' hasIcon={false} />
      </Box>

      <Title level={TitleLevels.THREE}>Déclinaison de couleur</Title>
      <Box>
        <Notification alert={AlertState.INFO} title='Notification' />
        <Notification alert={AlertState.SUCCESS} title='Notification' />
        <Notification alert={AlertState.WARNING} title='Notification' />
        <Notification alert={AlertState.ERROR} title='Notification' />
      </Box>

      <Title level={TitleLevels.THREE}>Version avec texte additionnel</Title>
      <Box>
        <Notification
          title='Notification'
          description='Reprehenderit eiusmod duis eu consectetur deserunt enim esse est do mollit. Aliqua et velit et culpa nulla veniam tempor veniam voluptate nulla. Nisi est sunt incididunt irure in ullamco eiusmod sunt. Reprehenderit incididunt labore qui culpa cillum eiusmod ex non aute ea Lorem. Incididunt laborum quis consequat commodo laborum consectetur id anim elit pariatur.'
        />
      </Box>

      <Title level={TitleLevels.THREE}>Version avec bouton et chevron</Title>
      <Box>
        <Notification
          alert={AlertState.INFO}
          title='Notification with button'
          buttonContent='Valider'
          buttonMarkup={ButtonMarkup.BUTTON}
          // buttonVariant={VariantState.PRIMARY}
          // eslint-disable-next-line no-alert
          buttonClick={() => alert('Test call to action click event')}
        />
        <Notification title='Notification with arrow' arrow />
      </Box>

      <Title level={TitleLevels.THREE}>Message informatif</Title>
      <Box>
        <Notification title='Notification info' info />
        <Notification title='Notification success' alert={AlertState.SUCCESS} info />
        <Notification title='Notification warning' alert={AlertState.WARNING} info />
        <Notification title='Notification error' alert={AlertState.ERROR} info />
      </Box>

      <Title level={TitleLevels.THREE}>Message informatif sans icône</Title>
      <Box>
        <Notification title='Notification info' info hasIcon={false} />
      </Box>

      <Title level={TitleLevels.THREE}>Version bannière</Title>
      <Box>
        <Notification description='Banner notification description' banner iconName={IconName.SIM_CARD} />
      </Box>
    </Section>
  )
}
