import React from "react";
import { APIProvider } from "./api";
import { ConfigProvider } from "./config";
import { <PERSON>rror<PERSON><PERSON><PERSON> } from "./error";
import { LoggerProvider } from "./logger";
import { SnackbarProvider } from "./snackbar";
import { AuthProvider } from "./authPkce";
import { QueryClient, QueryClientProvider } from "react-query";

export const Providers: React.FC<React.PropsWithChildren> = ({ children }) => {
  const showStackTrace = process.env.NODE_ENV === "development";
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchInterval: false,
        retry: false,
      },
    },
  });
  return (
    <ConfigProvider>
      <AuthProvider>
        <LoggerProvider>
          <SnackbarProvider showStackTrace={showStackTrace}>
            <ErrorHandler showStackTrace={showStackTrace}>
              <APIProvider>
                <QueryClientProvider client={queryClient}>
                  {children}
                </QueryClientProvider>
              </APIProvider>
            </ErrorHandler>
          </SnackbarProvider>
        </LoggerProvider>
      </AuthProvider>
    </ConfigProvider>
  );
};
