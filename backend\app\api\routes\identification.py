import logging
import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>, BackgroundTasks, Depends, HTTPException

from app.api.security import ADMIN_AUTHENTIFICATION, get_current_user

# from app.api.security import validate_access_token
from app.common.bootstrap import APPLICATION
from app.common.exceptions import NotAvailableException, get_exception_responses
from app.domains.identification.models import (
    DemandeIdentificationFiltre,
    DemandeIdentificationInput,
    DemandeIdentificationOutput,
    DemandeIdentificationOutputEnrichi,
)
from app.domains.identification.service import DemandeInvalideException, IdentificationEchouee, IdentificationEnCoursException, NotFoundException

router = APIRouter(tags=["identification"])
logger = logging.getLogger(__name__)


@router.post("/identifications", responses=get_exception_responses(IdentificationEnCoursException, DemandeInvalideException))
def demander_identification(
    demande: DemandeIdentificationInput, background_tasks: BackgroundTasks, current_user=Depends(get_current_user)
) -> DemandeIdentificationOutput:
    if APPLICATION.settings.DEGRADED:
        raise NotAvailableException(message="application en mode dégradé: le service n'est pas disponible")
    demande_cree: DemandeIdentificationOutput = APPLICATION.services.identifications.creer_demande(dto=demande, demandeur_id=current_user.id)
    background_tasks.add_task(APPLICATION.services.identifications.identifier, demande_cree.id)
    return demande_cree


@router.get("/identifications/{demande_id}", responses=get_exception_responses(NotFoundException))
def suivre_demande_identification(demande_id: uuid.UUID) -> DemandeIdentificationOutput:
    return APPLICATION.services.identifications.status_demande(demande_id)


@router.get("/identifications/{demande_id}/abandonner", responses=get_exception_responses(NotFoundException))
def cloturer_demande(demande_id: uuid.UUID) -> DemandeIdentificationOutput:
    return APPLICATION.services.identifications.maj_statut_demande(IdentificationEchouee(id=demande_id))


@router.post("/admin/identifications", responses=get_exception_responses(), dependencies=[ADMIN_AUTHENTIFICATION])
def liste_identifications_admin(Filtredemande: DemandeIdentificationFiltre, limit: int = 100) -> list[DemandeIdentificationOutputEnrichi]:
    try:
        # Si le statut n'est pas spécifié ou est "Tout", ne pas filtrer par statut
        if Filtredemande.statuts and "tout" in Filtredemande.statuts:
            Filtredemande.statuts = None

        demandes = APPLICATION.services.identifications.trouver_demandes(Filtredemande, limit=limit)
        logger.info(f"Récupération réussie de {len(demandes)} demandes d'identification")
        return demandes
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des demandes d'identification: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")
