import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from .models import WorkspaceStorage, WorkspaceUpdateInput


class WorkspaceRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dao: WorkspaceStorage) -> WorkspaceStorage: ...
    @abstractmethod
    def update(self, dto: WorkspaceUpdateInput) -> WorkspaceStorage: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> WorkspaceStorage | None: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[WorkspaceStorage]: ...
    @abstractmethod
    def find(self, owner_id: uuid.UUID) -> Iterable[WorkspaceStorage]: ...
