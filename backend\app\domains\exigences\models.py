import uuid
from datetime import datetime

from pydantic import BaseModel
from sqlmodel import Field, SQLModel

from app.domains.agents.models import FormatExigence

# Objet métier


class ExigenceBase(BaseModel):
    """
    le modèle de données pour les exigences
    """

    nom: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=255)
    type: FormatExigence
    data: str | None = Field(default=None)


# ce qui est persisté (Data Access Object)


class ExigenceStorage(SQLModel, ExigenceBase, table=True):
    """
    le modèle de données de la table exigences
    """

    __tablename__ = "exigences"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    date_creation: datetime
    owner_id: uuid.UUID = Field(foreign_key="utilisateurs.id")
    ws_id: uuid.UUID = Field(foreign_key="workspaces.id")


# ce qui est exposé vers l'extérieur (Data Transfert Objects)


class ExigenceCreateInput(ExigenceBase):
    """
    modèle de données pour la création d'un Exigence
    """

    owner_id: uuid.UUID
    ws_id: uuid.UUID


class ExigencePostInput(ExigenceBase):
    """
    modèle de données pour la création d'un Exigence
    """

    ws_id: uuid.UUID


class ExigenceUpdateInput(BaseModel):
    """
    modèle de données pour mettre à jour les infos
    """

    # le jour où on voudra permettre de modifier le proprio d'une Exigence
    # id: uuid.UUID
    pass


class ExigenceOutput(ExigenceBase):
    """
    comment le Exigence est vu depuis l'API
    """

    id: uuid.UUID
    owner_id: uuid.UUID
    ws_id: uuid.UUID


class ExigenceFiltre(BaseModel):
    """
    le filtre de recherche des Exigences
    """

    owner_id: uuid.UUID | None = None
    ws_id: uuid.UUID | None = None
    exigences_ids: list[uuid.UUID] | None = None
