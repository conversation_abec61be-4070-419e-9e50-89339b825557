"""
définition de l'application FastAPI avec gestion du démarrage
"""

import logging
from contextlib import asynccontextmanager

import asgi_correlation_id
from fastapi import FastAPI

from app.api.dependencies import custom_operation_ids_generation, exceptions_handler
from app.api.monitoring import init_api_metrics, init_api_tracing
from app.api.routes import api_router
from app.api.security import init_api_security
from app.common.bootstrap import APPLICATION, init_app
from app.common.config import AppSettings, settings
from app.common.logging import configure_logging


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    fonction en charge de réaliser le startup/shutdown de l'application.
    Typiquement, permet de vérifier les connexions ou charger en mémoire des éléments.
    On en aura probablement besoin pour charger des models
    """
    logger = logging.getLogger(__name__)
    configure_logging(settings)
    logger.info(f"Démarrage de l'application {app.title} avant exposition de l'API web")

    logger.info(f"Configuration: {settings}")
    # TODO: vérifier le bon démarrage des services de télémétrie avant de recevoir des requêtes htttp
    # TODO: récupérer les secrets depuis vault ?
    # from app.common.bootstrap import APPLICATION, init_app, settings
    init_app(APPLICATION, settings)

    yield
    # si il y a besoin de faire des opérations de cleanup
    logger.info(f"Arrêt de l'application {app.title}")


def create_app(settings: AppSettings) -> FastAPI:
    """
    création des routes et ajout des middlesware avant de démarrer l'application
    """
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        redoc_url="/documentation" if settings.DOCS_UI == "redoc" else None,
        docs_url="/documentation" if settings.DOCS_UI == "swagger" else None,
        generate_unique_id_function=custom_operation_ids_generation,
        lifespan=lifespan,
    )
    app.include_router(api_router)
    app.middleware("http")(exceptions_handler)
    app.add_middleware(asgi_correlation_id.CorrelationIdMiddleware)
    init_api_security(app, settings)
    init_api_metrics(app, settings)
    init_api_tracing(app, settings)
    return app
