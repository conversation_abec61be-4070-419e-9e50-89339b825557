import logging
from uuid import UUID

import pandas as pd
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import StreamingResponse

from app.api.security import get_current_user
from app.common.bootstrap import APPLICATION
from app.domains.exigences.models import ExigenceCreateInput, ExigenceOutput, ExigencePostInput
from app.domains.users.models import UserOutput

router = APIRouter(tags=["exigences"])
logger = logging.getLogger(__name__)


@router.get("/exigences", response_model=list[ExigenceOutput])
async def list_exigences(limit: int = 100) -> list[ExigenceOutput]:
    logger.info(f"Received request to list exigences with limit={limit}")
    try:
        result = APPLICATION.services.exigences.get_exigences(limit)
        logger.info(f"Successfully retrieved {len(result)} exigences")
        return result
    except Exception as e:
        logger.error(f"Error listing exigences: {str(e)}", exc_info=True)
        raise


@router.get("/exigences/{id_exigence}", response_model=ExigenceOutput)
async def get_exigence(id_exigence: UUID):
    logger.info(f"Received request to get exigence with id={id_exigence}")
    try:
        exigence = APPLICATION.services.exigences.get_exigence_by_id(id_exigence)
        if not exigence:
            logger.warning(f"Exigence not found with id={id_exigence}")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exigence not found")
        logger.info(f"Successfully retrieved exigence with id={id_exigence}")
        return exigence
    except Exception as e:
        logger.error(f"Error getting exigence: {str(e)}", exc_info=True)
        raise


@router.post("/exigences", response_model=ExigenceOutput, status_code=status.HTTP_201_CREATED)
async def add_exigence(request: Request, payload: ExigencePostInput, current_user: UserOutput = Depends(get_current_user)):
    exigence = ExigenceCreateInput(**payload.model_dump(exclude={"owner_id"}), owner_id=current_user.id)
    logger.info(f"Received request to create exigence with data={exigence.model_dump()}")
    try:
        logger.info(f"Create exigence with Current user ID: {current_user.id}")
        exigence.owner_id = current_user.id
        result = APPLICATION.services.exigences.create_exigence(exigence)
        logger.info(f"Successfully created exigence with id={result.id}")
        return result
    except Exception as e:
        logger.error(f"Error creating exigence: {str(e)}", exc_info=True)
        raise


@router.get("/me/exigences", response_model=list[ExigenceOutput])
async def get_user_exigences(current_user: UserOutput = Depends(get_current_user)):
    logger.info(f"Received request to list exigences for user={current_user.id}")
    try:
        result = APPLICATION.services.exigences.get_exigence_by_user(current_user.id)
        logger.info(f"Successfully retrieved {len(result)} exigences for user={current_user.id}")
        return result
    except Exception as e:
        logger.error(f"Error getting user exigences: {str(e)}", exc_info=True)
        raise


@router.get("/workspaces/{workspace_id}/exigences", response_model=list[ExigenceOutput])
async def get_workspace_exigences(request: Request, workspace_id: UUID):
    logger.info(f"Received request to list exigences for workspace={workspace_id}")
    try:
        result = APPLICATION.services.exigences.get_exigence_by_workspace(workspace_id)
        logger.info(f"Successfully retrieved {len(result)} exigences for workspace={workspace_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting workspace exigences: {str(e)}", exc_info=True)
        raise


@router.get("/workspace/{workspace_id}/testcases/export")
async def export_workspace_testcases(request: Request, workspace_id: UUID, format: str):
    logger.info(f"Received request to export test cases for workspace={workspace_id} in format={format}")
    try:
        result = APPLICATION.services.exigences.get_exigence_by_workspace(workspace_id)
        logger.info(f"Successfully retrieved {len(result)} exigences for workspace={workspace_id}")
        exigence_ids = [exigence.id for exigence in result]

        result = APPLICATION.services.testcases.get_testcases_by_exigences(exigence_ids, statut="validé")

        testcase_ids = [testcase.id for testcase in result]
        result_export = APPLICATION.services.testcases.export_testcases(testcase_ids, format)
        logger.info(f"Successfully retrieved {len(result)} test cases for workspace={workspace_id}")

    except Exception as e:
        logger.error(f"Error getting workspace exigences: {str(e)}", exc_info=True)
        raise
    if result is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exigence not found")

    if format.lower() == "csv":
        if isinstance(result_export, pd.DataFrame):
            return StreamingResponse(
                iter([result_export.to_csv(index=False, sep=";")]),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=testcase_{format}_{workspace_id}.csv"},
            )
    else:  # json
        content = result_export if isinstance(result_export, str) else result_export.to_json(orient="records")
        return StreamingResponse(
            iter([content]),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=testcase_{format}_{workspace_id}.json"},
        )


@router.get("/exigences/{exigence_id}/testcases/export")
async def export_exigences_testcases(request: Request, exigence_id: UUID, format: str):
    logger.info(f"Received request to export valid test cases for exigence={exigence_id}")
    try:
        result = APPLICATION.services.testcases.get_testcases_by_exigences([exigence_id], statut="validé")
        testcase_ids = [testcase.id for testcase in result]
        result_export = APPLICATION.services.testcases.export_testcases(testcase_ids, format)
        logger.info(f"Successfully retrieved {len(result)} test cases for exigence={exigence_id}")

    except Exception as e:
        logger.error(f"Error getting exigences: {str(e)}", exc_info=True)
        raise

    if result is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Exigence not found")

    if format.lower() == "csv":
        if isinstance(result_export, pd.DataFrame):
            return StreamingResponse(
                iter([result_export.to_csv(index=False, sep=";")]),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=testcase_{format}_{exigence_id}.csv"},
            )
    else:  # json
        content = result_export if isinstance(result_export, str) else result_export.to_json(orient="records")
        return StreamingResponse(
            iter([content]),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=testcase_{format}_{exigence_id}.json"},
        )


@router.get("/login/{login}/workspaces/{workspace_id}/exigences", response_model=list[ExigenceOutput])
async def get_workspace_exigences_by_login(workspace_id: UUID, current_user: UserOutput = Depends(get_current_user)):
    logger.info(f"Received request to list exigences for user={current_user.id} and workspace={workspace_id}")
    try:
        result = APPLICATION.services.exigences.get_user_workspace_exigences(ws_id=workspace_id, owner_id=current_user.id)
        logger.info(f"Successfully retrieved {len(result)} exigences for user={current_user.id} and workspace={workspace_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting user workspace exigences: {str(e)}", exc_info=True)
        raise
