import React, { useState, useCallback, useEffect } from 'react';
import { Title, TitleLevels, Text, <PERSON><PERSON>, Mo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>itle, Divider, Input, InputChangeEvent, Textarea, Select, SelectOption, Checkbox, ModalMarkup, Tabs, TabsItem, Fab } from '@bytel/trilogy-react-ts';
import { TextareaChangeEvent } from '@bytel/trilogy-react-ts/lib/components/textarea/TextareaProps';
import { Requirement } from 'services/interfaces/requirementInterfaces';
import { useDropzone } from 'react-dropzone';
import ActionDropdown from 'components/ActionDropdown';
import { FaCloudDownloadAlt } from "react-icons/fa";
import UploadDocument from './Uploaddocument';
import { BsFiletypeCsv, BsFiletypeJson } from "react-icons/bs";
import { ExtractRequirementsFabModal } from './ExtractRequirements';
import IdentificationSection from './IdentificationSection';

interface BannerProps {
  workspace: { id: string, description: string, nom: string };
  setIsModalActive: React.Dispatch<React.SetStateAction<boolean>>;
  setIsGenerateModalActive: React.Dispatch<React.SetStateAction<boolean>>;
  isModalActive: boolean;
  isGenerateModalActive: boolean;
  newRequirement: any;
  handleInputChange: (event: InputChangeEvent) => void;
  handleTextareaChange: (event: TextareaChangeEvent) => void;
  handleSelectChange: (value: any) => void;
  handleCreateRequirement: () => void;
  requirements: Requirement[];
  selectedExigencesIds: string[];
  setSelectedExigencesIds: React.Dispatch<React.SetStateAction<string[]>>;
  handleGenerateGlobal: () => void;
  toggleSelectAllExigences: () => void;
  isIdentificationInProgress?: boolean; // Nouvelle prop
  handleDownload?: (generationId: string, formatExport: string, type: string, NameExport: string,
    formatimplementation: string) => void;
  lastIdentificationData?: {
    demandeur_login: string;
    date_creation: string;
    date_debut: string | null;
    date_fin: string | null;
    statut: string;
  } | null;
}
//TODO: A DECOUPER
const Banner: React.FC<BannerProps> = ({
  workspace, 
  setIsModalActive, 
  setIsGenerateModalActive, 
  isModalActive, 
  isGenerateModalActive, 
  newRequirement, 
  handleInputChange, 
  handleTextareaChange, 
  handleSelectChange, 
  handleCreateRequirement, 
  requirements, 
  selectedExigencesIds, 
  setSelectedExigencesIds, 
  handleGenerateGlobal,
  toggleSelectAllExigences,
  isIdentificationInProgress = false, // Valeur par défaut
  handleDownload,
  lastIdentificationData = null
}) => {
  const [selectedFormat, setSelectedFormat] = useState<string>("csv");
  const [currentTime, setCurrentTime] = useState<number>(Date.now());

  const [activeTab, setActiveTab] = useState<number>(0);
  const [fileName, setFileName] = useState<string | null>(null);
  const [fileSize, setFileSize] = useState<number | null>(null);
  const [isDownloadModalActive, setIsDownloadModalActive] = useState<boolean>(false);

  // Effet pour mettre à jour la durée écoulée toutes les secondes si une identification est en cours
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isIdentificationInProgress && lastIdentificationData?.date_debut) {
      interval = setInterval(() => {
        setCurrentTime(Date.now());
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isIdentificationInProgress, lastIdentificationData?.date_debut]);
  const onDrop = useCallback((acceptedFiles: any[]) => {
    const file = acceptedFiles[0];
    setFileName(file.name);
    setFileSize(file.size);
    const reader = new FileReader();
    reader.onload = (e) => {
      newRequirement.data = e.target?.result?.toString() ?? '';
      
      // Déterminer le type du fichier et mettre à jour newRequirement.type
      if (file.type === 'application/json' || file.name.endsWith('.json')) {
        newRequirement.type = 'OpenAPI';
      } else if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        newRequirement.type = 'langage naturel';
      }
    };
    reader.readAsText(file);
  }, [newRequirement]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/json': ['.json'],
      'text/plain': ['.txt']
    }
  });

  const handleTabChange = (index: number) => {
    setActiveTab(index);
    if (index === 0) {
      setFileName(null);  // Réinitialiser le nom du fichier lorsque l'on va sur un autre onglet
    }
  };

  // Fonction pour calculer la durée entre deux dates
  const calculateDuration = (startDate: Date, endDate: Date) => {
    const durationMs = endDate.getTime() - startDate.getTime();
    const minutes = Math.floor(durationMs / (1000 * 60));
    const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  // Fonction pour calculer le temps écoulé depuis le début
  const calculateElapsedTime = (startDate: Date) => {
    const now = new Date(currentTime);
    return calculateDuration(startDate, now);
  };

  return (
    <div className="banner">
      <div className="banner-body">
        <div className="description-container">
          <Title level={TitleLevels.TWO} className="description-title">Description</Title>
          <Text className="description">{workspace.description}</Text>
        </div>

        <IdentificationSection
          isIdentificationInProgress={isIdentificationInProgress}
          lastIdentificationData={lastIdentificationData}
          calculateElapsedTime={calculateElapsedTime}
          calculateDuration={calculateDuration}
        />

        <div className="banner-buttons">
          <Fab iconName="tri-plus-circle" onClick={() => setIsModalActive(true)}>
            Ajouter une exigence
          </Fab>
          <ExtractRequirementsFabModal currentWorkspace={workspace.id}/>
          <Fab className="button is-primary"
            iconName="tri-play"
            onClick={() => setIsGenerateModalActive(true)}
            disabled={isIdentificationInProgress}
          >            
          {isIdentificationInProgress ? "Identification En cours" : "Identification Globale"}
          </Fab>
          
          
           {/* <Fab style={{ backgroundColor: '#007b52' }} 
            onClick={() => setIsDownloadModalActive(true)} 
            className="button is-secondary" 
            iconName="tri-download">Télécharger</Fab> */}
            <Button className={`fab button is-secondary`}  style={{ backgroundColor: '#007b52' }}
                    onClick={() => setIsDownloadModalActive(true)} 
                  >
                    <span className="icon"><FaCloudDownloadAlt/></span>
                    Télécharger
                  </Button>

       
          <UploadDocument 
            buttonClassName="fab button is-primary"
            buttonStyle={{ backgroundColor: '#6f6ee3' }}
          />


          <Modal active={isModalActive} triggerMarkup={ModalMarkup.BUTTON} closeIcon onClose={() => setIsModalActive(false)} ctaContent="Créer">
            <ModalTitle>Créer une nouvelle exigence</ModalTitle>
            <Divider />
            <div className="create-button-modal">
              <Input
                placeholder="Nom de l'exigence"
                type="text"
                name="nom"
                value={newRequirement.nom}
                onChange={handleInputChange}
              />
              <Textarea
                placeholder="Description"
                name="description"
                value={newRequirement.description}
                onChange={handleTextareaChange}
              />
              <Select
                label="Type"
                name="type"
                selected={newRequirement.type}
                onChange={handleSelectChange}
              >
                <SelectOption value="langage naturel">Langage naturel</SelectOption>
                <SelectOption value="user story">User Story</SelectOption>
                <SelectOption value="OpenAPI">OpenAPI</SelectOption>
              </Select>
              <Tabs activeIndex={activeTab}>
                <TabsItem active={activeTab === 0} onClick={() => handleTabChange(0)}>
                  <Title level={TitleLevels.THREE}>Saisie de texte</Title>
                </TabsItem>
                <TabsItem active={activeTab === 1} onClick={() => handleTabChange(1)}>
                  <Title level={TitleLevels.THREE}>Jointure de fichier</Title>
                </TabsItem>
              </Tabs>

              {activeTab === 0 && (
                <Textarea
                  placeholder="Mettez ici le contenu de votre exigence..."
                  name="data"
                  value={newRequirement.data}
                  onChange={handleTextareaChange}
                />
              )}
              
              {activeTab === 1 && (
                <div {...getRootProps()} className={`dropzone ${isDragActive ? 'active' : ''}`} style={{
                  border: '2px dashed #bbb', padding: '20px', borderRadius: '8px', textAlign: 'center', marginTop: '8px'
                }}>
                  <input {...getInputProps()} />
                  {fileName ? (
                    <Text>Fichier sélectionné : {fileName} ({(fileSize! / 1024).toFixed(2)} Ko)</Text>
                  ) : (
                    <Text>{isDragActive ? "Déposez le fichier ici..." : "Faites glisser un fichier ici, ou cliquez pour sélectionner un fichier"}</Text>
                  )}
                </div>
              )}
            </div>
            <ModalFooter className="modal-footer">
              <Button variant='SECONDARY' onClick={() => setIsModalActive(false)}>Annuler</Button>
              <Button variant='PRIMARY' onClick={handleCreateRequirement}>Créer</Button>
            </ModalFooter>
          </Modal>

          <Modal
            active={isGenerateModalActive}
            triggerMarkup={ModalMarkup.BUTTON}
            closeIcon
            onClose={() => setIsGenerateModalActive(false)}
            ctaContent='Confirmer'
          >
            <ModalTitle>Confirmer la génération</ModalTitle>
            <Divider />
            <Text>
              Sélectionnez les exigences à inclure dans la génération. Cette action peut prendre du temps, veuillez patienter.
            </Text>
            <div className="checkbox-group" aria-label="Liste des exigences à inclure dans la génération">
              <Checkbox
                id="checkbox-select-all"
                label="Tout sélectionner"
                value="selectAll"
                checked={selectedExigencesIds.length === requirements.length}
                onChange={toggleSelectAllExigences}
              />
              {requirements.map((req) => (
                <Checkbox
                  key={req.id}
                  id={`checkbox-${req.id}`}
                  label={req.nom}
                  value={req.id}
                  checked={selectedExigencesIds.includes(req.id)}
                  onChange={(event) => {
                    const { checkboxChecked, checkboxValue } = event;
                    setSelectedExigencesIds((prevIds) => 
                      checkboxChecked ? [...prevIds, checkboxValue] : prevIds.filter((id) => id !== checkboxValue)
                    );
                  }}
                />
              ))}
            </div>
            <ModalFooter className="modal-footer">
              <Button variant='SECONDARY' onClick={() => setIsGenerateModalActive(false)}>
                Annuler
              </Button>
              <Button variant='PRIMARY' onClick={handleGenerateGlobal}>Confirmer</Button>
            </ModalFooter>
          </Modal>
          <Modal 
  active={isDownloadModalActive} 
  triggerMarkup={ModalMarkup.BUTTON} 
  closeIcon 
  onClose={() => setIsDownloadModalActive(false)}
>
  <ModalTitle>Choisir le format d'export</ModalTitle>
  <Divider />
  <Text>
    Sélectionnez le format d'export des cas de test valides du workspace.
  </Text>
  <div style={{ margin: '20px 0', padding: '0 20px' }}>
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      marginBottom: '15px',
      padding: '10px',
      border: selectedFormat === 'csv' ? '1px solid #007b52' : '1px solid transparent',
      borderRadius: '4px',
      backgroundColor: selectedFormat === 'csv' ? 'rgba(0, 123, 82, 0.05)' : 'transparent'
    }}>
      <input 
        type="radio" 
        id="csv" 
        name="format" 
        value="csv"
        checked={selectedFormat === "csv"} 
        onChange={() => setSelectedFormat("csv")}
        style={{ marginRight: '15px', transform: 'scale(1.2)' }}
      />
      <label htmlFor="csv" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', width: '100%' }}>
        <BsFiletypeCsv size={30} style={{ marginRight: '10px' }}/>
        <div>
          <div style={{ fontWeight: 'bold' }}>Format CSV</div>
          <div style={{ fontSize: '0.9em', color: '#666' }}>Format tabulaire compatible avec Excel</div>
        </div>
      </label>
    </div>
    
    <div style={{ 
      display: 'flex', 
      alignItems: 'center',
      padding: '10px',
      border: selectedFormat === 'json' ? '1px solid #007b52' : '1px solid transparent',
      borderRadius: '4px',
      backgroundColor: selectedFormat === 'json' ? 'rgba(0, 123, 82, 0.05)' : 'transparent'
    }}>
      <input 
        type="radio" 
        id="json" 
        name="format" 
        value="json"
        checked={selectedFormat === "json"} 
        onChange={() => setSelectedFormat("json")}
        style={{ marginRight: '15px', transform: 'scale(1.2)' }}
      />
      <label htmlFor="json" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', width: '100%' }}>
        <BsFiletypeJson size={30} style={{ marginRight: '10px' }}/>
        <div>
          <div style={{ fontWeight: 'bold' }}>Format JSON</div>
          <div style={{ fontSize: '0.9em', color: '#666' }}>Format structuré pour l'intégration technique</div>
        </div>
      </label>
    </div>
  </div>
  <ModalFooter>
    <Button 
      variant='SECONDARY' 
      onClick={() => setIsDownloadModalActive(false)}
    >
      Annuler
    </Button>
    <Button 
      variant='PRIMARY' 
      onClick={() => {
        handleDownload(workspace.id, selectedFormat, 'workspace', workspace.nom, 'xray');
        setIsDownloadModalActive(false);
      }}
    >
      Télécharger
    </Button>
  </ModalFooter>
</Modal>
        </div>
      </div>
    </div>
  );
};

export default Banner;
