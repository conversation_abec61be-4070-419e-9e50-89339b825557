import { BQuery } from "@bytel/query";
import { Service } from "./commonService";
import { DocumentOutput, DocumentsOutput } from "../types/document";

/**
 * Service pour la gestion des documents utilisateur
 * @class DocumentService
 * @extends Service
 */
export class DocumentService extends Service {
  constructor(bquery: BQuery) {
    super(bquery);
  }

  /**
   * Uploade un nouveau document pour l'utilisateur authentifié
   * @param {File} file Le fichier à uploader
   * @returns {Promise<DocumentOutput>} Le document uploadé
   */
  async uploadDocument(file: File): Promise<DocumentOutput> {
    const formData = new FormData();
    formData.append('file', file);
    return this.bquery.post<DocumentOutput>(`/users/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Liste tous les documents de l'utilisateur connecté
   * @returns {Promise<DocumentsOutput>} La liste des documents
   */
  async listUserDocuments(): Promise<DocumentsOutput> {
    return this.bquery.get<DocumentsOutput>(`/users/documents`);
  }

  /**
   * Télécharge le contenu d'un document
   * @param {string} documentId L'ID du document à télécharger
   * @returns {Promise<Blob>} Le contenu du fichier sous forme de Blob
   */
  async downloadDocument(documentId: string): Promise<Blob> {
    return this.bquery.get<Blob>(`/users/documents/${documentId}/download`, {
      responseType: 'blob',
    });
  }

  /**
   * Supprime un document spécifique
   * @param {string} documentId L'ID du document à supprimer
   * @returns {Promise<void>}
   */
  async deleteDocument(documentId: string): Promise<void> {
    return this.bquery.delete<void>(`/users/documents/${documentId}`);
  }

  /**
   * Met à jour un document spécifique
   * @param {string} documentId L'ID du document à mettre à jour
   * @param {File} file Le nouveau fichier
   * @returns {Promise<DocumentOutput>} Le document mis à jour
   */
  async updateDocument(documentId: string, file: File): Promise<DocumentOutput> {
    const formData = new FormData();
    formData.append('file', file);
    return this.bquery.put<DocumentOutput>(`/users/documents/${documentId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}