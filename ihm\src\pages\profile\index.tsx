import React, { useState, useRef, useCallback } from 'react';
import { 
  Box, 
  Container,
  Title, 
  TitleLevels,
  Text,
  Divider,
  Columns,
  ColumnsItem,
  Button,
  Modal,
  ModalTitle,
  ModalFooter,
} from '@bytel/trilogy-react-ts';
import { useAPIClient } from "providers/api";
import { useSnackbar } from "providers/snackbar";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { AxiosError } from "axios";
import { DocumentOutput } from 'types/document';
import { FaCloudDownloadAlt, FaEdit, FaRegTrashAlt } from "react-icons/fa";
import UploadDocument from 'components/Uploaddocument';
import DocumentTree from 'components/DocumentTree';
import { useDropzone, FileRejection } from 'react-dropzone';

import { useAuth } from 'hooks/useAuth';
import './profile.css';

const ProfilePage: React.FC = () => {
  const { showError, showInfo } = useSnackbar();
  const apiClient = useAPIClient();
  const queryClient = useQueryClient();

  const [documentToUpdate, setDocumentToUpdate] = useState<DocumentOutput | null>(null);
  const [fileForUpdate, setFileForUpdate] = useState<File | null>(null);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);


  // Requête pour les informations utilisateur via le hook
  const { user, isLoading: isLoadingUser } = useAuth();

  // Requête pour la liste des documents
  const { data: documentsData, isLoading: isLoadingDocuments, refetch: refetchDocuments } = useQuery(
    'userDocuments',
    () => apiClient.documentService.listUserDocuments(),
    {
      enabled: !!user, // Ne lance la requête que si l'utilisateur est chargé
      onError: (error: AxiosError) => {
        showError(error, { message: "Erreur lors de la récupération des documents" });
      }
    }
  );

  // Mutation pour supprimer un document
  const deleteMutation = useMutation(
    (documentId: string) => apiClient.documentService.deleteDocument(documentId),
    {
      onSuccess: () => {
        showInfo("Document supprimé avec succès !");
        queryClient.invalidateQueries('userDocuments');
      },
      onError: (error: AxiosError) => {
        showError(error, { message: "Erreur lors de la suppression du document" });
      }
    }
  );
  
  // Mutation pour mettre à jour un document
  const updateMutation = useMutation(
    ({ documentId, file }: { documentId: string; file: File }) => apiClient.documentService.updateDocument(documentId, file),
    {
      onSuccess: () => {
        showInfo("Document mis à jour avec succès !");
        queryClient.invalidateQueries('userDocuments');
        setDocumentToUpdate(null);
        setFileForUpdate(null);
      },
      onError: (error: AxiosError) => {
        showError(error, { message: "Erreur lors de la mise à jour du document" });
      }
    }
  );

  const handleDownload = async (doc: DocumentOutput) => {
    try {
      const blob = await apiClient.documentService.downloadDocument(doc.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = doc.filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      showInfo(`Téléchargement de ${doc.filename} démarré.`);
    } catch (error) {
      showError(error as AxiosError, { message: `Erreur lors du téléchargement de ${doc.filename}` });
    }
  };

  const handleDelete = (documentId: string) => {
    setConfirmDeleteId(documentId);
  };

  const handleConfirmDelete = () => {
    if (confirmDeleteId) {
      deleteMutation.mutate(confirmDeleteId);
      setConfirmDeleteId(null);
    }
  };

  const handleCancelDelete = () => {
    setConfirmDeleteId(null);
  };
  
  // Callback pour la gestion du drag & drop dans la modal de mise à jour
  const onDropUpdate = useCallback((acceptedFiles: File[], fileRejections: FileRejection[]) => {
    if (fileRejections.length > 0) {
      setFileForUpdate(null);
      const errors = fileRejections[0].errors;
      if (errors.find(e => e.code === 'file-too-large')) {
        showError(null, { message: "Le fichier ne doit pas dépasser 100 Mo." });
      } else if (errors.find(e => e.code === 'file-invalid-type')) {
        showError(null, { message: "Type de fichier non valide. Seuls les .docx, .txt, .pdf et .xlsx sont acceptés." });
      }
      return;
    }

    if (acceptedFiles && acceptedFiles.length > 0) {
      setFileForUpdate(acceptedFiles[0]);
    }
  }, [showError]);

  // Configuration de useDropzone pour la modal de mise à jour
  const { getRootProps: getRootPropsUpdate, getInputProps: getInputPropsUpdate, isDragActive: isDragActiveUpdate } = useDropzone({
    onDrop: onDropUpdate,
    multiple: false,
    maxSize: 100 * 1024 * 1024, // 100 Mo
    accept: {
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    }
  });

  const openUpdateModal = (doc: DocumentOutput) => {
    setDocumentToUpdate(doc);
    setFileForUpdate(null); // Réinitialise le fichier
  };

  const handleUpdateSubmit = () => {
    if (documentToUpdate && fileForUpdate) {
      updateMutation.mutate({ documentId: documentToUpdate.id, file: fileForUpdate });
    } else {
      showInfo("Veuillez sélectionner un fichier pour la mise à jour.");
    }
  };

  const handleCloseUpdateModal = () => {
    setDocumentToUpdate(null);
    setFileForUpdate(null);
  };

  if (isLoadingUser) {
    return <div>Chargement du profil...</div>;
  }

  return (
    <Container>
      <div>
        <Title level={TitleLevels.ONE} className="profile-title">
          Mon profil
        </Title>
        <Divider />
        <Columns>
        <ColumnsItem size={12}>
        <Box >
          <Title level={TitleLevels.THREE}>Informations personnelles</Title>
          <Columns>
            <ColumnsItem size={4}><Text className="field-label">Identifiant</Text></ColumnsItem>
            <ColumnsItem size={8}><Text>{user?.login}</Text></ColumnsItem>
          </Columns>
          <Columns>
            <ColumnsItem size={4}><Text className="field-label">Rôle</Text></ColumnsItem>
            <ColumnsItem size={8}><Text>{user?.admin ? 'Administrateur' : 'Utilisateur'}</Text></ColumnsItem>
          </Columns>
          <Columns>
            <ColumnsItem size={4}><Text className="field-label">Date d'inscription</Text></ColumnsItem>
            <ColumnsItem size={8}><Text>{user?.date_inscription ? new Date(user.date_inscription).toLocaleDateString() : '-'}</Text></ColumnsItem>
          </Columns>
        </Box>
          </ColumnsItem>
          </Columns>
          <Columns>
          <ColumnsItem >
          <Box >
        
          <Columns flex verticalCentered marginSize={1}>
            <ColumnsItem size={8}>
              <Title level={TitleLevels.THREE} className="profile-ged">Mes Documents</Title>
            </ColumnsItem>
            <ColumnsItem size={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <UploadDocument 
                buttonClassName="button is-primary"
                buttonStyle={{ backgroundColor: '#0c7b91', borderColor: '#0c7b91' }}  
              />
            </ColumnsItem>
          </Columns>

          {/* Remplacer le tableau par DocumentTree */}
          <DocumentTree  
            documents={documentsData?.documents || []}
            isLoading={isLoadingDocuments}
            onDownload={handleDownload}
            onUpdate={openUpdateModal}
            onDelete={handleDelete}
          />
        

        {/* Modal de mise à jour */}
        {documentToUpdate && (
          <Modal active={!!documentToUpdate} onClose={handleCloseUpdateModal} size="small">
            <ModalTitle>Mettre à jour : {documentToUpdate.filename}</ModalTitle>
            <Divider />
            <Text>Sélectionnez le nouveau fichier pour remplacer "{documentToUpdate.filename}".</Text>
            
            <div {...getRootPropsUpdate()} style={{
              border: `2px dashed ${isDragActiveUpdate ? '#007bff' : '#bbb'}`,
              padding: '20px',
              borderRadius: '8px',
              textAlign: 'center',
              marginTop: '8px',
              cursor: 'pointer',
              transition: 'border .24s ease-in-out'
            }}>
              <input {...getInputPropsUpdate()} />
              {fileForUpdate ? (
                <Text>Nouveau fichier : {fileForUpdate.name} ({(fileForUpdate.size / 1024).toFixed(2)} Ko)</Text>
              ) : (
                <Text>{isDragActiveUpdate ? "Déposez le fichier ici..." : "Faites glisser un fichier ici, ou cliquez pour sélectionner"}</Text>
              )}
            </div>
            
            <ModalFooter>
              <Button variant="NEUTRAL" onClick={handleCloseUpdateModal}>Annuler</Button>
              <Button variant="PRIMARY" onClick={handleUpdateSubmit} loading={updateMutation.isLoading} disabled={!fileForUpdate}>
                Mettre à jour
              </Button>
            </ModalFooter>
          </Modal>
        )}

        {/* Modal de confirmation de suppression */}
        <Modal active={!!confirmDeleteId} onClose={handleCancelDelete} size="small">
          <ModalTitle>Confirmer la suppression</ModalTitle>
          <Divider />
          <Text>Êtes-vous sûr de vouloir supprimer ce document&nbsp;?</Text>
          <ModalFooter>
            <Button variant="NEUTRAL" onClick={handleCancelDelete}>Annuler</Button>
            <Button className='profile-btn-red' onClick={handleConfirmDelete} loading={deleteMutation.isLoading}>
              Supprimer
            </Button>
          </ModalFooter>
        </Modal>

      </Box>
          </ColumnsItem>
        </Columns>
        
        </div>
        {/* <Divider className="mt-4 mb-4" /> */}
        

    </Container>
  );
};

export default ProfilePage;