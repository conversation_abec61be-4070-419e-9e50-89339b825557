from datetime import datetime
from uuid import UUID

import pytest

from app.domains.exigences.models import ExigenceCreateInput, ExigenceOutput, ExigenceStorage
from app.domains.exigences.service import ExigenceNotFoundException, ExigenceService
from app.repositories.memory import MemoryExigenceRepository

# Constants pour les tests
FAKE_UUID = UUID("12345678-1234-5678-1234-************")
FAKE_USER_ID = UUID("*************-5432-9876-************")
FAKE_WS_ID = UUID("11111111-**************-************")


@pytest.fixture
def repository():
    return MemoryExigenceRepository()


@pytest.fixture
def service(repository):
    return ExigenceService(repository=repository)


@pytest.fixture
def sample_exigence():
    return ExigenceStorage(
        id=FAKE_UUID,
        description="Description test",
        date_creation=datetime.now(),
        owner_id=FAKE_USER_ID,
        ws_id=FAKE_WS_ID,
        nom="Test Nom",
        type="text",
    )


class TestExigenceService:
    def test_creer_exigence(self, service, repository, sample_exigence):
        # Arrange
        input_data = ExigenceCreateInput(description="Description test", owner_id=FAKE_USER_ID, ws_id=FAKE_WS_ID, nom="Test Nom", type="text")

        # Act
        result = service.create_exigence(input_data)

        # Assert
        assert isinstance(result, ExigenceOutput)
        assert result.nom == input_data.nom
        assert len(repository.entities) == 2  # 1 initial + 1 créé

    def test_obtenir_exigence_par_id_succes(self, service, repository, sample_exigence):
        # Arrange
        created = repository.add(sample_exigence)

        # Act
        result = service.get_exigence_by_id(created.id)

        # Assert
        assert isinstance(result, ExigenceOutput)
        assert result.id == created.id
        assert result.nom == created.nom

    def test_obtenir_exigence_par_id_non_trouve(self, service):
        # Act & Assert
        with pytest.raises(ExigenceNotFoundException):
            service.get_exigence_by_id(FAKE_UUID)

    def test_lister_exigences(self, service, repository, sample_exigence):
        # Arrange
        repository.add(sample_exigence)

        # Act
        result = service.get_exigences(limit=10)

        # Assert
        assert isinstance(result, list)
        assert len(result) >= 1
        assert isinstance(result[0], ExigenceOutput)

    def test_obtenir_exigence_par_utilisateur(self, service, repository, sample_exigence):
        # Arrange
        repository.add(sample_exigence)

        # Act
        result = service.get_exigence_by_user(FAKE_USER_ID)

        # Assert
        assert isinstance(result, list)
        assert len(result) >= 1
        assert all(isinstance(item, ExigenceOutput) for item in result)
        assert all(item.owner_id == FAKE_USER_ID for item in result)

    def test_obtenir_exigences_utilisateur_workspace(self, service, repository, sample_exigence):
        # Arrange
        repository.add(sample_exigence)

        # Act
        result = service.get_user_workspace_exigences(FAKE_USER_ID, FAKE_WS_ID)

        # Assert
        assert isinstance(result, list)
        assert len(result) >= 1
        assert isinstance(result[0], ExigenceOutput)
        assert all(exigence.ws_id == FAKE_WS_ID for exigence in result)

    def test_obtenir_exigence_par_workspace(self, service, repository, sample_exigence):
        # Arrange
        repository.add(sample_exigence)

        # Act
        result = service.get_exigence_by_workspace(FAKE_WS_ID)

        # Assert
        assert isinstance(result, list)
        assert len(result) >= 1
        assert all(isinstance(item, ExigenceOutput) for item in result)
        assert all(item.ws_id == FAKE_WS_ID for item in result)
        assert all(item.type in ["text", "FUNCTIONAL"] for item in result)  # Vérifie les deux types possibles
