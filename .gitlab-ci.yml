include: https://v1.bci.int.nbyt.fr/$CI_PROJECT_ID/$CI_COMMIT_SHA/gitlab-ci.yml

stages:
  - test
  - build
  - deliver
  - deploy


🔨 Build apiweb:
  when: manual
  stage: build
  image: registry.glouton.int.nbyt.fr/docker:latest
  tags:
    - dind
  services:
    - docker:dind
  needs: []
  script:
    - cd ${CI_PROJECT_DIR}/backend
    - docker build --build-arg https_proxy=$https_proxy --build-arg no_proxy=$no_proxy .
    - docker image list    


📦 Deliver apiweb:
  image: registry.glouton.int.nbyt.fr/commun/devops/ci/delivery-tools:5-LATEST
  id_tokens:
    VAULT_ID_TOKEN:
      aud: $VAULT_CI_ADDR
  tags:
    - dind
  services:
    - docker:dind
  stage: deliver
  rules:
    - if: $CI_COMMIT_TAG =~ /^apiweb@(.+)$/
    - if: $CI_COMMIT_TAG =~ /^[^@]+$/
  variables:
    APP_NAME: apiweb
  needs:
    - 🔨 Build apiweb
  script:
    - cd ${CI_PROJECT_DIR}/backend
    - echo "Verification du démarrage du service Docker"
    - count=60; rc=1; while [ ${rc} -ne 0 ] && [ ${count} -gt 0 ]; do
      count=$((count-1)); sleep 1; rc=0; docker info > /dev/null || rc=$?; done
    - if [ ${rc} -ne 0 ]; then echo 'Le service docker a pas réussi à démarrer
      en moins de une minute'; fi
    - APP_VERSION=${CI_COMMIT_REF_NAME#*@}
    - docker_to_artifactory.sh --st_name "CATIA" --name "${APP_NAME}" 
      --version "${APP_VERSION}"
      --dockerfile "${CI_PROJECT_DIR}/backend/Dockerfile"
      --build-arg "VERSION=${APP_VERSION}"
      --build-arg "https_proxy=${https_proxy}" 
      --build-arg "no_proxy=${no_proxy}"


📦 Deliver ihm:
  image: registry.glouton.int.nbyt.fr/commun/devops/ci/delivery-tools:5-LATEST
  id_tokens:
    VAULT_ID_TOKEN:
      aud: $VAULT_CI_ADDR
  tags:
    - dind
  services:
    - docker:dind
  stage: deliver
  rules:
    - if: $CI_COMMIT_TAG =~ /^ihm@(.+)$/
    - if: $CI_COMMIT_TAG =~ /^[^@]+$/
  variables:
    APP_NAME: ihm
  needs:
    - 🔨 Build ihm
  script:
    - cd ${CI_PROJECT_DIR}/ihm
    - echo "Verification du démarrage du service Docker"
    - count=60; rc=1; while [ ${rc} -ne 0 ] && [ ${count} -gt 0 ]; do
      count=$((count-1)); sleep 1; rc=0; docker info > /dev/null || rc=$?; done
    - if [ ${rc} -ne 0 ]; then echo 'Le service docker a pas réussi à démarrer
      en moins de une minute'; fi
    - APP_VERSION=${CI_COMMIT_REF_NAME#*@}
    - docker_to_artifactory.sh --st_name "CATIA"
      --name "${APP_NAME}"
      --version "${APP_VERSION}" 
      --dockerfile "${CI_PROJECT_DIR}/ihm/Dockerfile"
