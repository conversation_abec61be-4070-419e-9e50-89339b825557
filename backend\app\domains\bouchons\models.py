import uuid
from datetime import datetime

from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel

from app.domains.agents.openapi.models import CasUsageAPI, FormatBouchon, ImplementationBouchons

# Storage


class BouchonsApiStorage(SQLModel, table=True):
    """
    le modèle de données de la table référencant les bouchons identifiés et/ou implémentés à partir d'une exigence Open API
    """

    __tablename__ = "bouchons_openapi"
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    exigence_id: uuid.UUID = Field(index=True, foreign_key="exigences.id")
    date_creation: datetime = Field(default_factory=datetime.now)
    date_maj_statut: datetime | None = None
    date_generation: datetime

    usecases: list["CasUsageApiStorage"] = Relationship(back_populates="service_bouchonne")
    implementations: list["ImplementationBouchonsStorage"] | None = Relationship(back_populates="service_bouchonne")


class CasUsageApiStorage(SQLModel, table=True):
    """
    le modèle de données de la table contenant les cas d'usage identifiés à partir d'une exigence Open API
    qui vont être à implémenter
    """

    __tablename__ = "bouchons_openapi_usecases"
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    bouchons_openapi_id: uuid.UUID = Field(index=True, foreign_key="bouchons_openapi.id")
    date_creation: datetime = Field(default_factory=datetime.now)
    date_maj_statut: datetime | None = None
    service_path: str | None = Field(description="la valeur du path dans le spécification OpenAPI pour le cas d'usage décrit")
    status_code: int = Field(description="le code de statut HTTP de la réponse obtenu du service pour le cas d'usage décrit")
    http_method: str = Field(description="la méthode HTTP utilisée pour la requête faite au service")
    description: str = Field(description="une description du cas d'usage d'appel au service")
    appel: str = Field(description="la requête HTTP faite au service pour le cas d'usage décrit")
    reponse: str = Field(description="la réponse HTTP obtenu du service pour le cas d'usage décrit")

    service_bouchonne: BouchonsApiStorage | None = Relationship(back_populates="usecases")


class ImplementationBouchonsStorage(SQLModel, table=True):
    """
    le modèle de données de la table contenant les bouchons implémentés à partir des cas d'usages identifiés
    """

    __tablename__ = "bouchons_openapi_implementations"
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    bouchons_openapi_id: uuid.UUID = Field(index=True, foreign_key="bouchons_openapi.id")
    date_creation: datetime = Field(default_factory=datetime.now)
    date_maj_statut: datetime | None = None
    format: FormatBouchon
    contenu: str

    service_bouchonne: BouchonsApiStorage | None = Relationship(back_populates="implementations")


# DTO


class BouchonsApiInput(BaseModel):
    """
    dto pour l'ajout/création de bouchons d'un service généré par IA
    """

    exigence_id: uuid.UUID
    date_generation: datetime
    usecases: list[CasUsageAPI]
    implementations: list[ImplementationBouchons]


class BouchonsApiUpdate(BaseModel):
    """
    dto pour la modificiation
    """

    date_maj_statut: datetime = Field(default_factory=datetime.now)
    usecases: list[CasUsageAPI]
    implementations: list[ImplementationBouchons]


class CasUsageAPIOutput(CasUsageAPI):
    id: uuid.UUID
    # bouchons_openapi_id: uuid.UUID
    date_creation: datetime
    date_maj_statut: datetime | None


class ImplementationBouchonsOutput(ImplementationBouchons):
    id: uuid.UUID
    # bouchons_openapi_id: uuid.UUID
    date_creation: datetime
    date_maj_statut: datetime | None


class BouchonsApiOutput(BaseModel):
    """
    comment les bouchons liés à une specification OpenAPI sont vus depuis l'API
    """

    id: uuid.UUID
    date_maj_statut: datetime | None
    date_creation: datetime
    usecases: list[CasUsageAPIOutput]
    implementations: list[ImplementationBouchonsOutput]


class BouchonsApiFiltre(BaseModel):
    """
    filtre de recherche autorisé
    """

    exigences_ids: list[uuid.UUID] = Field(description="la liste des exigences OpenAPI recherchées", min_items=1)
    # debut: datetime | None = Field(None, description="permet de chercher des demande à partir de cette date si indiquée")
    # fin: datetime | None = Field(None, description="permet de chercher des demande jusqu'à cette date si indiquée")
