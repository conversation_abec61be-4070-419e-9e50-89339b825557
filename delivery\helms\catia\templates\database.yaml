{{- if .Values.db.enabled }}

apiVersion: bcloud.bouyguestelecom.fr/v1alpha1
kind: Database
metadata:
  name: "{{ .Values.db.name }}"
spec:
  bcloud_version: 4.16.0
  composant:
    name: bdd
  instance_type: {{ .Values.db.instance_type }}
  engine: postgres
  engine_version: 15
  db_instance_identifier: "{{ .Values.db.name }}"
  db_name: "catia"
  db_allocate_storage: {{ .Values.db.allocate_storage }}
  db_storage_type: gp3


{{- end }}