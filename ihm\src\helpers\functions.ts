export function nullIfEmpty(obj?: string | null) {
  return obj == "" ? null : (obj ?? null);
}

export function undefinedIfEmpty(obj?: string | null) {
  return obj == "" ? undefined : (obj ?? undefined);
}

export function isNullOrEmpty(obj?: string | null) {
  return obj === "" || obj == null;
}

export function isAnyNullOrEmpty(...liste: (string | null | undefined)[]) {
  return liste.some(isNullOrEmpty);
}
