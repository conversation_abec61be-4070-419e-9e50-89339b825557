import React from 'react'
import {
  Section,
  Title,
  TitleLevels,
  Slice,
  SliceIcon,
  SliceContent,
  TextLevels,
  SliceCta,
  SliceList,
  Checkbox,
  SliceBody,
  SliceImage,
  Text,
  Button,
  IconSize,
  IconName,
  Icon,
} from '@bytel/trilogy-react-ts'

export const SliceScreen = (): JSX.Element => {
  return (
    <Section>
      <Slice>
        <SliceIcon iconSize={IconSize.LARGE} iconName={IconName.TV_BTV} />
        <SliceContent>
          <Text level={TextLevels.ONE}>Slice avec CTA simple (ici, un simple chevron)</Text>
          <Text>Un texte supplémentaire pour décrire le contenu de laction</Text>
        </SliceContent>
        <SliceCta>
          <Icon name={IconName.ARROW_RIGHT} size={IconSize.SMALL} onClick={() => alert('Click on cta')} />
        </SliceCta>
      </Slice>

      <Slice>
        <SliceIcon iconSize={IconSize.LARGE} iconName={IconName.TV_BTV} />
        <SliceContent>
          <Text level={TextLevels.ONE}>Slice avec CTA simple (ici, un simple chevron)</Text>
          <Text>Un texte supplémentaire pour décrire le contenu de laction</Text>
        </SliceContent>
        <SliceCta>
          <Button variant={'PRIMARY'} onClick={() => alert('click on cta')}>
            Button CTA
          </Button>
        </SliceCta>
      </Slice>

      <SliceList selectable>
        <Slice selectable>
          <Checkbox removeControl removeField name='checkone' />
          <SliceBody>
            <SliceContent>
              <Text level={TextLevels.ONE}>Slice avec CTA simple (ici, un simple chevron)</Text>
              <Text>Un texte supplémentaire pour décrire le contenu de laction</Text>
            </SliceContent>
            <SliceCta>
              <Button variant={'PRIMARY'} onClick={() => alert('click on cta')}>
                Button CTA
              </Button>
            </SliceCta>
          </SliceBody>
        </Slice>
        <Slice selectable>
          <Checkbox removeControl removeField name='checktwo' />
          <SliceBody>
            <SliceIcon iconSize={IconSize.LARGE} iconName={IconName.TV_BTV} />
            <SliceContent>
              <Text level={TextLevels.ONE}>Slice avec CTA simple (ici, un simple chevron)</Text>
              <Text>Un texte supplémentaire pour décrire le contenu de laction</Text>
            </SliceContent>
            <SliceCta>
              <Button variant={'PRIMARY'} onClick={() => alert('click on cta')}>
                Button CTA
              </Button>
            </SliceCta>
          </SliceBody>
        </Slice>
        <Slice selectable>
          <Checkbox removeControl removeField name='checktwo' />
          <SliceBody>
            <SliceImage src='https://design.bouyguestelecom.fr/phone.1da9ed96.png' alt='phone' />
            <SliceContent>
              <Text level={TextLevels.ONE}>Slice avec CTA simple (ici, un simple chevron)</Text>
              <Text>Un texte supplémentaire pour décrire le contenu de laction</Text>
            </SliceContent>
            <SliceCta>
              <Button variant={'PRIMARY'} onClick={() => alert('click on cta')}>
                Button CTA
              </Button>
            </SliceCta>
          </SliceBody>
        </Slice>
        <Slice>
          <SliceBody>
            <SliceImage rounded src='https://design.bouyguestelecom.fr/profil.14ef6e58.jpg' alt='phone' />
            <SliceContent>
              <Title level={TitleLevels.THREE}>Slice avec CTA simple (ici, un simple chevron)</Title>
              <Text>Un texte supplémentaire pour décrire le contenu de laction</Text>
            </SliceContent>
            <SliceCta>
              <Icon name={IconName.ARROW_RIGHT} size={IconSize.SMALL} onClick={() => alert('Click on cta')} />
            </SliceCta>
          </SliceBody>
        </Slice>
      </SliceList>
    </Section>
  )
}
