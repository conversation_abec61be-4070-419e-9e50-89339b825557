from typing import Literal

from atlassian import Confluence
from langchain_openai.chat_models import AzureChatOpenAI
from pydantic import BaseModel, Field, SecretStr, field_serializer
from pydantic_settings import BaseSettings, SettingsConfigDict

from app.domains.bouchons.repository import BouchonsApiRepositoryInterface
from app.domains.bouchons.service import BouchonsOpenApiService
from app.domains.documents.repository import DocumentRepositoryInterface
from app.domains.documents.service import DocumentService
from app.domains.exigences.repository import ExigenceRepositoryInterface
from app.domains.exigences.service import ExigenceService
from app.domains.extraction.service import ExtractionService
from app.domains.feedbacks.repository import FeedbackRepositoryInterface
from app.domains.feedbacks.service import FeedbackService
from app.domains.identification.agent import AppAgents
from app.domains.identification.repository import IdentificationsRepositoryInterface
from app.domains.identification.service import IdentificationService
from app.domains.testcases.repository import (
    TestcasesRepositoryInterface,
    TestImplementationsRepositoryInterface,
)
from app.domains.testcases.service import TestCasesService
from app.domains.users.repository import UsersRepositoryInterface
from app.domains.users.service import UserService
from app.domains.workspace.repository import WorkspaceRepositoryInterface
from app.domains.workspace.service import WorkspaceService


class ChatGPTConfig(BaseModel):
    """Classe de configuration pour le Chat GPT utilisé en interne"""

    enabled: bool = Field(True, description="permet d'activer ou non les fonctionnalités liés aux LLM")
    api_key: SecretStr | None = Field(None, description="La clé d'API de l'API Azure.", min_length=1)
    api_version: str | None = Field("2024-10-21", description="La version de l'API Azure.")
    azure_endpoint: str | None = Field("https://poc-topsi.openai.azure.com", description="L'endpoint Azure.")
    azure_deployment: str | None = Field("topsi-gpt4o", description="Le déploiement Azure.")

    @field_serializer("api_key", when_used="always")
    def dump_secret(self, value: SecretStr):
        return value.get_secret_value()


class AtlassianConfig(BaseModel):
    """Classe de configuration pour la connexion aux outils Atlassian (Jira, Confluence...)"""

    enabled: bool = Field(
        True,
        description="permet d'activer ou non les fonctionnalités liés à Confluence",
    )
    url: str | None = Field(None, description="l'url utilisé pour appeler confluence que ce soit en direct ou via un reverse proxy")
    login: str | None = Field(None, description="le login lié au token d'authentification", min_length=1)
    token: SecretStr | None = Field(None, description="le token lié au login utilisé")
    official_url: str | None = Field("https://bouyguestelecom.atlassian.net/wiki", description="l'url officiel à retourner pour les liens confluence")
    verify: bool = Field(True, description="vérifie la chaine de certificats ou non. contournement lorsque il y a des certificats pki manquant")


class DatabaseConfig(BaseModel):
    """classe de configuration pour la connexion à la base de données"""

    dialect: Literal["sqlite", "postgresql"] | None = None
    host: str | None = Field(None, description="le nom de la machine où est hébergée la base de données")
    port: int = Field(None, description="le port pour se connecter (ex: 5432 pour du postgresql)")
    user: str | None = None
    password: SecretStr | None = None
    name: str = Field("catia", description="le nom de base de données")
    url: SecretStr | None = Field(
        None,
        description="permet d'indiquer la chaine de connexion complete, auquel cas, celle-ci prend le dessus sur le reste de la configuration",
    )


class StorageConfig(BaseModel):
    """Classe de configuration pour la persistance des données"""

    type: Literal["memory", "memsql", "database"] = Field("memsql", description="Le type de stockage à utiliser.")


class IODCConfig(BaseModel):
    """Classe de configuration pour l'authentification OIDC"""

    client_id: str = Field(
        "catia-bouyguestelecom-fr",
        description="Le client ID utilisé pour l'authentification",
    )
    realm_url: str = Field(
        "https://sesame-ap0.apps.ocp-1.pin.prd.mlb.nbyt.fr/auth/realms/miro",
        description="Le realm utilisé pour valider les token reçus",
    )


class AppSettings(BaseSettings):
    """
    the configuration of the app
    """

    model_config = SettingsConfigDict(
        # utilisation du .env à la racine du mono-repo
        env_file="../.env",
        env_ignore_empty=True,
        extra="ignore",
        env_nested_delimiter="_",
        env_nested_max_split=1,
    )

    ENVIRONMENT: Literal["dev", "local", "staging", "production"] = "local"
    APP_NAME: str
    APP_VERSION: str
    DOCS_UI: Literal["redoc", "swagger", None] = "swagger"

    STORAGE: StorageConfig = Field(StorageConfig(), description="définition des modes de persistances")
    METRICS: bool = True
    TRACE: bool = False
    LOG_LEVEL: Literal["WARNING", "INFO", "DEBUG", "TRACE"] = "INFO"
    LOG_FILE: str | None = None
    LLM: ChatGPTConfig = Field(ChatGPTConfig(), description="Le LLM utilisé pour les agents IA")
    DEGRADED: bool = Field(
        False,
        description="permet de démarrer l'application en mode dégradé. Il s'agit principalement d'une désactivation des appels aux LLM au démarrage",
    )
    OIDC: IODCConfig = Field(IODCConfig(), description="Configuration de l'authentification OIDC")
    DB: DatabaseConfig = Field(DatabaseConfig(), description="la chaine de connexion à la base de données")
    ATLASSIAN: AtlassianConfig = Field(
        AtlassianConfig(),
        description="Configuration de l'intégration aux outils Atlassian",
    )


settings = AppSettings()


class AppServices:
    users: UserService
    workspaces: WorkspaceService
    exigences: ExigenceService
    testcases: TestCasesService
    identifications: IdentificationService
    bouchons: BouchonsOpenApiService
    extraction: ExtractionService
    documents: DocumentService
    feedbacks: FeedbackService


class AppRepos:
    users: UsersRepositoryInterface
    workspaces: WorkspaceRepositoryInterface
    exigences: ExigenceRepositoryInterface
    testcases: TestcasesRepositoryInterface
    implementations: TestImplementationsRepositoryInterface
    identifications: IdentificationsRepositoryInterface
    bouchons: BouchonsApiRepositoryInterface
    documents: DocumentRepositoryInterface
    feedbacks: FeedbackRepositoryInterface


class AppExternals:
    confluence: Confluence
    llm: AzureChatOpenAI
    llm_json: AzureChatOpenAI


class Application:
    settings: AppSettings
    services: AppServices
    repos: AppRepos
    agents: AppAgents
    externals: AppExternals
