import { TypeHateoas } from "@bytel/query/lib/hateoas";

export const HATEOAS = {
  hasAction: <T, U>(
    hateoasObject: TypeHateoas<T, U> | null | undefined,
    action: keyof U,
  ): boolean => {
    return hateoasObject?._actions?.[action] != null;
  },
  hasLink: <T, U>(
    hateoasObject: TypeHateoas<T, U> | null | undefined,
    link: keyof T,
  ): boolean => {
    return hateoasObject?._links?.[link] != null;
  },
};
