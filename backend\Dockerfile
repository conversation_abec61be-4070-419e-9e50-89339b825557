# FROM registry.glouton.int.nbyt.fr/python:3.10
FROM registry.glouton.int.nbyt.fr/commun/devops/ci/python3.12

ARG VERSION=0.0.0-dev

ENV APP_NAME=CATIA
ENV APP_VERSION=$VERSION
ENV NLTK_DATA /catia/nltk_data

WORKDIR /catia

RUN pip config --user set global.index https://glouton.nimda.dolmen.bouyguestelecom.fr/artifactory/api/pypi/pypi/simple
RUN pip config --user set global.index-url https://glouton.nimda.dolmen.bouyguestelecom.fr/artifactory/api/pypi/pypi/simple
RUN pip config --user set global.trusted-host glouton.nimda.dolmen.bouyguestelecom.fr

COPY app app
COPY pyproject.toml pyproject.toml

RUN pip install --no-cache-dir -e .

# telechargement des modeles nltk
RUN mkdir -p ./nltk_data
COPY install_models.py install_models.py
RUN python install_models.py
# RUN cp -r /root/nltk_data /catia/nltk_data

EXPOSE 8080

CMD [ "fastapi", "run", "app/main.py", "--proxy-headers", "--port", "8080" ]
