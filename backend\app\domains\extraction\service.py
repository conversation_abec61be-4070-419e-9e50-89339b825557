from app.domains.extraction.models import ExigenceDocumentUnique
from app.domains.extraction.repositories import DocumentsUniquesRepositoryInterface


class ExtractionService:
    """
    le services d'extraction d'un format de donnée supporté vers des objets métiers.

    TODO: voir si mise en place d'un systeme de cache/persistence
    """

    def __init__(self, repo_docs_uniques: DocumentsUniquesRepositoryInterface):
        self._repo_du = repo_docs_uniques

    def extraire_exigence_documentation_unique(self, id_confluence: str) -> list[ExigenceDocumentUnique]:
        return self._repo_du.extraire_exigences(id_document_unique=id_confluence)
