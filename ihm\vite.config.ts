/// <reference types="vitest" />
import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react-swc";
import viteTsconfigPaths from "vite-tsconfig-paths";
import * as path from "node:path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), viteTsconfigPaths()],
  test: {
    globals: true,
    environment: "jsdom",
    mockReset: true,
    setupFiles: ["./src/tests/setup.js", "./src/tests/init-mocks.tsx"],
    exclude: ["e2e", "node_modules"],
    reporters: ["default", "junit"],
    outputFile: {
      junit: "junit.xml",
    },
    coverage: {
      provider: "v8",
      reporter: ["text", "cobertura", "lcov"],
    },
    deps: {
      optimizer: {
        web: {
          enabled: true,
          include: ["@bytel/trilogy-react-ts"],
        },
      },
    },
  },
  resolve: {
    alias: {
      "@bytel/trilogy-react-ts": path.resolve(__dirname, "./node_modules/@bytel/trilogy-react-ts/lib"),
    },
  },
  server: {
    host: "0.0.0.0",
  },
});
