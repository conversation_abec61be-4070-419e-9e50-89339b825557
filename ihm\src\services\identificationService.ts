import { Service } from "./commonService";
import { BQuery } from "@bytel/query";

export interface IdentificationResponse {
  statut: string;
  id: string;
  date_creation: Date;
  date_debut: Date;
  date_fin: Date;
}

export class IdentificationService extends Service {

  constructor(bquery: BQuery) {
    super(bquery);
  }

  async postIdentification(
    data: { workspace_id: string; exigences: string[] },
  ): Promise<IdentificationResponse> {
    const response = await this.bquery.post(`/identifications`, data);
    return response as IdentificationResponse;
  }

  async getIdentificationById(
    demande_id: string,
  ): Promise<IdentificationResponse> {
    const response = await this.bquery.get(`/identifications/${demande_id}`);
    return response as IdentificationResponse;
  }
}