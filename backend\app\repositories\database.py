import logging
import uuid

from pydantic import BaseModel
from sqlalchemy import URL
from sqlalchemy.orm import selectinload
from sqlmodel import Session, SQLModel, col, create_engine, select
from sqlmodel.pool import StaticPool

from app.common.bootstrap import AppRepos
from app.common.config import AppSettings
from app.common.sqltypes import PydanticJSONB
from app.domains.bouchons.repository import BouchonsApiFiltre, BouchonsApiRepositoryInterface, BouchonsApiStorage
from app.domains.documents.models import DocumentFilter, DocumentStorage
from app.domains.documents.repository import DocumentRepositoryInterface
from app.domains.exigences.repository import ExigenceFiltre, ExigenceRepositoryInterface, ExigenceStorage
from app.domains.feedbacks.repository import Feedback, FeedbackInput, FeedbackRepositoryInterface
from app.domains.identification.repository import DemandeIdentificationFiltre, DemandeIdentificationStorage, IdentificationsRepositoryInterface
from app.domains.testcases.repository import (
    TestCaseFilter,
    TestcasesRepositoryInterface,
    TestCaseStorage,
    TestImplementationsRepositoryInterface,
    TestImplementationStorage,
)
from app.domains.users.repository import UsersRepositoryInterface, UserStorage
from app.domains.workspace.repository import WorkspaceRepositoryInterface, WorkspaceStorage

LOGGER = logging.getLogger(__name__)


def start_engine(settings: AppSettings):
    connect_args = {"check_same_thread": False}
    if settings.DB.url is not None:
        LOGGER.debug("using existing connection string")
        engine = create_engine(
            url=settings.DB.url.get_secret_value(),
            connect_args=connect_args,
            poolclass=StaticPool if settings.STORAGE.type == "memsql" else None,
            pool_pre_ping=True,
            # echo=True if settings.LOG_LEVEL != "INFO" else False,
        )
    else:
        LOGGER.debug("creating URL object from settings...")
        db_url_object = URL.create(
            drivername=settings.DB.dialect,
            host=settings.DB.host,
            port=settings.DB.port,
            username=settings.DB.user,
            password=settings.DB.password.get_secret_value(),
            database=settings.DB.name,
        )
        LOGGER.debug("creating engine...")
        engine = create_engine(db_url_object)
    return engine


def init_db_storage(storage: AppRepos, settings: AppSettings):
    LOGGER.info("json type is: %s", PydanticJSONB.impl)
    LOGGER.info("starting database engine...")
    engine = start_engine(settings)
    LOGGER.info("initiating repositories...")
    storage.users = SqlUserRepository(engine)
    storage.workspaces = SqlWorkspaceRepository(engine)
    storage.exigences = SqlExigenceRepository(engine)
    storage.testcases = SqlTestcasesRepository(engine)
    storage.identifications = SqlIdentificationsRepository(engine)
    storage.bouchons = SqlBouchonsApiRepository(engine)
    storage.documents = SqlDocumentRepository(engine)  # Ajouter cette ligne
    storage.feedbacks = SqlFeedbackRepository(engine)
    LOGGER.info("creating tables...")
    LOGGER.debug(SQLModel.metadata.tables.keys())
    SQLModel.metadata.create_all(engine)
    LOGGER.info("database storage intiated")


class SqlBaseRepository:
    """
    les operations CRUD basiques qui vont être utilisés sur tous les repos.
    Ce qui permet de se concentrer sur les spécificités de chaque repository
    et de ne pas avoir à réécrire les mêmes méthodes pour chaque repository.

    Attention:
    SQL Alchemy faisant du lazy load pour les tables liées (cf Relationship dans les models),
    si vous souhaitez récupérer ces données liés en permanence, il vous faudra surcharger ces méthodes
    et rajouter en options ce que vous souhaitez récupérer depuis la base.

    Exemple: select(TestCaseStorage).options(selectinload(TestCaseStorage.implementations))
    """

    def __init__(self, engine, sqlmodel: SQLModel):
        self.engine = engine
        self._model = sqlmodel
        self.log = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def add(self, dao: SQLModel):
        with Session(self.engine) as session:
            session.add(dao)
            session.commit()
            session.refresh(dao)
            return dao

    def list(self, limit=100) -> list[SQLModel]:
        self.log.debug("récupération des %s (limite: %i)", self._model.__name__, limit)
        statement = select(self._model).limit(limit)
        self.log.debug(statement)
        with Session(self.engine) as session:
            return session.exec(statement).all()

    def update(self, update_model_with_id: BaseModel):
        with Session(self.engine) as session:
            dao: SQLModel = session.get(self._model, update_model_with_id.id)
            update_data = update_model_with_id.model_dump(exclude_unset=True)
            dao.sqlmodel_update(update_data)
            session.add(dao)
            session.commit()
            session.refresh(dao)
            return dao

    def get(self, entity_id: uuid.UUID) -> SQLModel:
        with Session(self.engine) as session:
            return session.get(self._model, entity_id)


class SqlUserRepository(SqlBaseRepository, UsersRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, UserStorage)

    def find(self, login: str) -> UserStorage | None:
        statement = select(UserStorage).where(UserStorage.login == login)
        with Session(self.engine) as session:
            return session.exec(statement).first()


class SqlWorkspaceRepository(SqlBaseRepository, WorkspaceRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, WorkspaceStorage)

    def find(self, owner_id: uuid.UUID) -> list[WorkspaceStorage]:
        statement = select(WorkspaceStorage).where(WorkspaceStorage.owner_id == owner_id)
        with Session(self.engine) as session:
            return session.exec(statement).all()

    # def owned_workspace_but_badly_optimized(self, user_id) -> int:
    #     statement = select(WorkspaceStorage).join(UserStorage).where()


class SqlExigenceRepository(SqlBaseRepository, ExigenceRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, ExigenceStorage)

    def find(self, filtre: ExigenceFiltre) -> list[ExigenceStorage]:
        self.log.info("recherche d'exigences avec le filtre %s", filtre)
        statement = select(ExigenceStorage)
        if filtre.ws_id:
            statement = statement.where(ExigenceStorage.ws_id == filtre.ws_id)
        if filtre.owner_id:
            statement = statement.where(ExigenceStorage.owner_id == filtre.owner_id)

        if filtre.exigences_ids and len(filtre.exigences_ids) > 0:
            statement = statement.where(col(ExigenceStorage.id).in_(filtre.exigences_ids))
        # .where(**filtre.model_dump(exclude_unset=True))

        self.log.debug("SQL statement: %s", statement)
        with Session(self.engine) as session:
            return session.exec(statement).all()


class SqlTestImplementationsRepository(SqlBaseRepository, TestImplementationsRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, TestImplementationStorage)


class SqlTestcasesRepository(SqlBaseRepository, TestcasesRepositoryInterface):
    """
    Table `testcases` ayant comme dépendance la table `implementations`
    """

    def __init__(self, engine):
        super().__init__(engine, TestCaseStorage)

    def find(self, filter: TestCaseFilter) -> list[TestCaseStorage]:
        statement = select(TestCaseStorage).options(selectinload(TestCaseStorage.implementations))

        if filter.exigences_ids and len(filter.exigences_ids) > 0:
            statement = statement.where(col(TestCaseStorage.exigence_id).in_(filter.exigences_ids))

        if filter.statut:
            statement = statement.where(TestCaseStorage.statut == filter.statut)

        if filter.debut:
            statement = statement.where(TestCaseStorage.date_generation >= filter.debut)

        if filter.fin:
            statement = statement.where(TestCaseStorage.date_generation <= filter.fin)

        with Session(self.engine) as session:
            return session.exec(statement).all()

    def get(self, entity_id: uuid.UUID) -> SQLModel:
        with Session(self.engine) as session:
            statement = select(TestCaseStorage).options(selectinload(TestCaseStorage.implementations)).where(TestCaseStorage.id == entity_id)
            return session.exec(statement).first()

    def list(self, limit=100) -> list[SQLModel]:
        statement = select(TestCaseStorage).options(selectinload(TestCaseStorage.implementations)).limit(limit=limit)
        self.log.debug(statement)
        with Session(self.engine) as session:
            return session.exec(statement).all()


class SqlIdentificationsRepository(SqlBaseRepository, IdentificationsRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, DemandeIdentificationStorage)

    # TODO: pourquoi cette fonction (find/filtre) est en double ????

    def find(self, filter: DemandeIdentificationFiltre) -> list[DemandeIdentificationStorage]:
        statement = select(DemandeIdentificationStorage)

        if filter.demandeur_id:
            statement = statement.where(DemandeIdentificationStorage.demandeur_id == filter.demandeur_id)

        if filter.workspace_id:
            statement = statement.where(DemandeIdentificationStorage.workspace_id == filter.workspace_id)

        if filter.statuts and len(filter.statuts) > 0:
            statement = statement.where(col(DemandeIdentificationStorage.statut).in_(filter.statuts))

        if filter.debut:
            statement = statement.where(DemandeIdentificationStorage.date_creation >= filter.debut)

        if filter.fin:
            statement = statement.where(TestCaseStorage.date_creation <= filter.fin)

        # Ajouter l'ordre pour afficher les demandes selon l'ordre demandé
        if filter.ordre and filter.ordre.lower() == "asc":
            statement = statement.order_by(DemandeIdentificationStorage.date_creation.asc())
        else:
            statement = statement.order_by(DemandeIdentificationStorage.date_creation.desc())

        with Session(self.engine) as session:
            return session.exec(statement).all()

    def filtre(self, filter: DemandeIdentificationFiltre, limit=100) -> list[DemandeIdentificationStorage]:
        statement = select(DemandeIdentificationStorage)

        if filter.demandeur_id:
            statement = statement.where(DemandeIdentificationStorage.demandeur_id == filter.demandeur_id)

        if filter.workspace_id:
            statement = statement.where(DemandeIdentificationStorage.workspace_id == filter.workspace_id)

        if filter.statuts and len(filter.statuts) > 0:
            statement = statement.where(col(DemandeIdentificationStorage.statut).in_(filter.statuts))

        if filter.debut:
            statement = statement.where(DemandeIdentificationStorage.date_creation >= filter.debut)

        if filter.fin:
            statement = statement.where(TestCaseStorage.date_creation <= filter.fin)

        # Ajouter l'ordre pour afficher les demandes selon l'ordre demandé
        if filter.ordre and filter.ordre.lower() == "asc":
            statement = statement.order_by(DemandeIdentificationStorage.date_creation.asc())
        else:
            statement = statement.order_by(DemandeIdentificationStorage.date_creation.desc())

        if limit:
            statement = statement.limit(limit)

        with Session(self.engine) as session:
            return session.exec(statement).all()


class SqlBouchonsApiRepository(SqlBaseRepository, BouchonsApiRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, BouchonsApiStorage)

    def find(self, filtre: BouchonsApiFiltre):
        self.log.info("recherche de bouchons avec le filtre %s...", filtre)
        statement = select(BouchonsApiStorage).options(selectinload(BouchonsApiStorage.implementations), selectinload(BouchonsApiStorage.usecases))

        if filtre.exigences_ids and len(filtre.exigences_ids) > 0:
            statement = statement.where(col(BouchonsApiStorage.exigence_id).in_(filtre.exigences_ids))

        self.log.debug(statement)

        with Session(self.engine) as session:
            return session.exec(statement).all()

    def get(self, entity_id: uuid.UUID) -> SQLModel:
        with Session(self.engine) as session:
            statement = select(BouchonsApiStorage).options(
                selectinload(BouchonsApiStorage.implementations), selectinload(BouchonsApiStorage.usecases)
            )
            statement = statement.where(BouchonsApiStorage.id == entity_id)
            return session.exec(statement).first()


class SqlDocumentRepository(SqlBaseRepository, DocumentRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, DocumentStorage)

    def find(self, filtre: DocumentFilter) -> list[DocumentStorage]:
        statement = select(DocumentStorage)

        if filtre.owner_id:
            statement = statement.where(DocumentStorage.owner_id == filtre.owner_id)

        if filtre.filename:
            statement = statement.where(DocumentStorage.filename.contains(filtre.filename))

        if filtre.document_ids and len(filtre.document_ids) > 0:
            statement = statement.where(col(DocumentStorage.id).in_(filtre.document_ids))

        with Session(self.engine) as session:
            return session.exec(statement).all()

    def delete(self, entity_id: uuid.UUID) -> bool:
        with Session(self.engine) as session:
            dao = session.get(DocumentStorage, entity_id)
            if dao:
                session.delete(dao)
                session.commit()
                return True
            return False


class SqlFeedbackRepository(SqlBaseRepository, FeedbackRepositoryInterface):
    def __init__(self, engine):
        super().__init__(engine, Feedback)

    # TODO: ça devrait être dans les services cette partie contrôle de l'input FeedbackInput
    def submit_feedback(self, feedback_input: FeedbackInput) -> Feedback:
        feedback = Feedback(**feedback_input.model_dump())
        return super().add(feedback)
