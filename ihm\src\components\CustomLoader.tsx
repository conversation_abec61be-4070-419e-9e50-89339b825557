import React from 'react';
import '../styles/loader.css';

interface CustomLoaderProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
}

const CustomLoader: React.FC<CustomLoaderProps> = ({ 
  size = 'medium', 
  color, 
  className = ''
}) => {
  const sizeClass = size !== 'medium' ? size : '';
  
  const style = color ? { 
    borderColor: `${color} ${color} transparent ${color}`
  } : {};

  return (
    <span 
      className={`custom-loader ${sizeClass} ${className}`} 
      style={style}
      aria-label="Chargement en cours"
    />
  );
};

export default CustomLoader;