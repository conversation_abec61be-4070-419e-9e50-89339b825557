# from io import String<PERSON>
import logging
import uuid

import pandas as pd

# from app.api.security import validate_access_token
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.responses import StreamingResponse

from app.api.security import get_current_user
from app.common.bootstrap import APPLICATION
from app.common.exceptions import InsufficientPermissionsException, get_exception_responses
from app.domains.exigences.service import ExigenceNotFoundException
from app.domains.testcases.models import TestCaseOutput
from app.domains.testcases.service import TestCaseNotFoundException
from app.domains.users.models import UserOutput
from app.domains.workspace.service import WorkspaceNotFoundException

router = APIRouter(tags=["testcases"])
logger = logging.getLogger(__name__)


@router.get("/workspaces/{workspace_id}/testcases", responses=get_exception_responses(WorkspaceNotFoundException))
def cas_de_test_par_espace_de_travail(workspace_id: uuid.UUID) -> list[TestCaseOutput]:
    APPLICATION.services.workspaces.get_workspace(id=workspace_id, asking_user=None)  # TODO
    return APPLICATION.services.testcases.get_testcases_by_workspace(workspace_id)


@router.get(
    "/exigences/{exigence_id}/testcases",
    responses=get_exception_responses(ExigenceNotFoundException, InsufficientPermissionsException),
    response_model=list[TestCaseOutput],
)
def cas_de_test_par_exigence(exigence_id: uuid.UUID, current_user: UserOutput = Depends(get_current_user)) -> list[TestCaseOutput]:
    validated_exigence_id = APPLICATION.services.exigences.get_exigence_by_id(exigence_id)
    if validated_exigence_id.owner_id != current_user.id and not current_user.admin:
        logger.warning("refus de l'accès: l'utilisateur n'est pas administrateur et n'est pas propriétaire de l'exigence demandée.")
        raise InsufficientPermissionsException(message=f"votre compte ({current_user.id}) n'a pas les droits pour accéder à cette exigence")
    return APPLICATION.services.testcases.get_testcases_by_exigences([exigence_id])


@router.get("/testcase/{testcase_id}/validate")
def validate_testcase(testcase_id: uuid.UUID, current_user: UserOutput = Depends(get_current_user)) -> bool:
    """Validate a testcase by its ID."""
    APPLICATION.services.testcases.validate_testcase(testcase_id, current_user.id)
    return True


@router.get("/testcase/{testcase_id}/reject")
def reject_testcase(testcase_id: uuid.UUID, current_user: UserOutput = Depends(get_current_user)) -> bool:
    """Reject a testcase by its ID."""
    APPLICATION.services.testcases.reject_testcase(testcase_id, current_user.id)
    return True


@router.get("/testcase/{testcase_id}/export")
def export_testcase(testcase_id: uuid.UUID, format: str = "json"):
    result = APPLICATION.services.testcases.export_testcases([testcase_id], format)
    if result is None:
        raise ExigenceNotFoundException(identifier=testcase_id)

    if format.lower() == "csv":
        if isinstance(result, pd.DataFrame):
            return StreamingResponse(
                iter([result.to_csv(index=False, sep=";")]),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=testcase_{format}_{testcase_id}.csv"},
            )
    else:  # json
        content = result if isinstance(result, str) else result.to_json(orient="records")
        return StreamingResponse(
            iter([content]),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=testcase_{format}_{testcase_id}.json"},
        )


@router.get("/testcase/{testcase_id}", response_model=TestCaseOutput)
def get_testcase(testcase_id: uuid.UUID, current_user: UserOutput = Depends(get_current_user)) -> TestCaseOutput:
    try:
        return APPLICATION.services.testcases.get_testcase_by_id(testcase_id)
    except TestCaseNotFoundException as e:
        logger.error(f"Testcase not found: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting testcase: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
