import { BQuery } from "@bytel/query";
import { UserCreateInput, UsersOutput, UserOutput } from '../types/user';
import { Service } from "./commonService";

/**
 * Service pour la gestion des utilisateurs
 * @class UserService
 * @extends Service
 */
export class UserService extends Service {
  constructor(bquery: BQuery) {
    super(bquery);
  }

  /**
   * Récupère la liste des utilisateurs
   * @returns {Promise<UsersOutput>} Liste des utilisateurs
   */
  async listUsers(): Promise<UsersOutput> {
    return this.bquery.get<UsersOutput>(`/users`);
  }

  /**
   * Crée un nouvel utilisateur
   * @param {UserCreateInput} user Données de l'utilisateur à créer
   * @returns {Promise<UserOutput>} L'utilisateur créé
   */
  async createUser(user: UserCreateInput): Promise<UserOutput> {
    return this.bquery.post<UserOutput>(`/users`, user);
  }

  /**
   * Récupère l'utilisateur courant
   * @returns {Promise<UserOutput>} L'utilisateur courant
   */
  async getCurrentUser(): Promise<UserOutput> {
    return this.bquery.get<UserOutput>(`/users/me`);
  }

  /**
   * Promeut un utilisateur au rôle d'administrateur
   * @param {string} login Login de l'utilisateur à promouvoir
   * @returns {Promise<void>}
   */
  async makeAdmin(login: string): Promise<void> {
    return this.bquery.post<void>(`/users/${login}/make-admin`);
  }
}