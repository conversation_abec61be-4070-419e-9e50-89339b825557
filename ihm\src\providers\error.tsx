import { Text } from "@bytel/trilogy-react-ts";
import { AxiosError } from "axios";
import * as React from "react";
import ErrorPage from "../pages/error/error";
import { SnackbarContext, TypeSnackbarContext, useSnackbar } from "./snackbar";

type ErrorState = {
  hasError: boolean;
  error?: Error;
  info?: React.ErrorInfo;
};
export type ErrorPageProps = React.PropsWithChildren<{
  showStackTrace?: boolean;
}>;

class ErrorBoundary extends React.Component<ErrorPageProps, ErrorState> {
  static contextType = SnackbarContext;
  state: ErrorState = {
    hasError: false,
  };

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    const snackbarContext = this.context as TypeSnackbarContext;
    snackbarContext?.showError(error, {
      severity: "hidden",
      logToPrisme: true,
    });
    this.setState({ hasError: true, error, info });
  }

  render() {
    const { hasError, error, info } = this.state;
    if (!hasError) {
      return <>{this.props.children}</>;
    }
    return (
      <ErrorPage>
        {this.props.showStackTrace && (
          <>
            <pre className="has-text-left is-size-8" style={{ maxHeight: 160, whiteSpace: "pre-wrap" }}>
              {error?.name}
              <br />
              {error?.message || "Erreur d'origine inconnue"}
              <br />
              {info?.componentStack}
            </pre>
            <Text has-text-grey is-italic is-size-8 is-pulled-left>
              ^ ces traces ne sont affichées qu&apos;en environnement de développement
            </Text>
          </>
        )}
      </ErrorPage>
    );
  }
}

export const ErrorHandler: React.FC<React.PropsWithChildren<{ showStackTrace: boolean }>> = ({ children, showStackTrace }) => {
  const { showError, show } = useSnackbar();

  React.useEffect(() => {
    window.onunhandledrejection = (event) => {
      const error = event.reason as Error;
      showError(error, { logToPrisme: !(error instanceof AxiosError) });
      event.stopPropagation();
    };
    window.onerror = (event) => {
      show({
        message: event.toString(),
        severity: "error",
        logToPrisme: true,
      });
    };
  }, [show, showError]);

  return <ErrorBoundary showStackTrace={showStackTrace}>{children}</ErrorBoundary>;
};
