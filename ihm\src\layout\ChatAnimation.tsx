import React, { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import styled from 'styled-components';
import { Button, Textarea, Title, Icon } from '@bytel/trilogy-react-ts';
import { TextareaChangeEvent } from '@bytel/trilogy-react-ts/lib/components/textarea/TextareaProps';
import cat_ia from 'layout/cat_ia.jpg';
import { useAPIClient } from 'providers/api';
import { useConfig } from 'providers/config';
import { VscFeedback } from 'react-icons/vsc';

type ChatAnimationProps = {
  showDialog: boolean;
  message?: string;
  isClickable?: boolean;
  exigenceId: string;  // Ajout exigenceId
  generationId: string;  // Ajout generationId
  workspaceId: string;  // Ajout workspaceId
};

export const chatAnimationMessageSystem = {
  listeners: [] as ((message: string) => void)[],
  showMessage: (message: string) => {
    chatAnimationMessageSystem.listeners.forEach(listener => listener(message));
  },
  subscribe: (callback: (message: string) => void) => {
    chatAnimationMessageSystem.listeners.push(callback);
    return () => {
      chatAnimationMessageSystem.listeners =
        chatAnimationMessageSystem.listeners.filter(listener => listener !== callback);
    };
  }
};

const ModalContainer = styled.div`
  position: fixed;
  bottom: 10px; /* Position at the same level as the cat icon */
  right: 180px; /* Positioned to the left of the cat icon */
  width: 400px;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  z-index: 2000;
  transform: translateX(500px); /* Start off-screen */
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const CloseButton = styled.div`
  cursor: pointer;
  font-size: 20px;
`;

const RatingStars = styled.div`
  display: flex;
  justify-content: flex-start;
  margin-bottom: 5px;
`;

const Star = styled.span<{ active: boolean; rating: number }>`
  font-size: 30px;
  cursor: pointer;
  color: ${props => {
    if (!props.active) return '#e0e0e0';
    switch (props.rating) {
      case 1:
        return 'red';
      case 2:
        return 'orange';
      case 3:
        return 'gold';
      case 4:
        return 'yellowgreen';
      case 5:
        return 'green';
      default:
        return 'gold';
    }
  }};
`;

const FeedbackIcon = styled.div`
  position: absolute;
  left: -60px;
  bottom: 20px;
`;

const ChatContainer = styled.div`
  position: relative;
  width: 90px;
  height: 90px;
`;

const ChatAnimation: React.FC<ChatAnimationProps> = ({ showDialog, message, isClickable, exigenceId, generationId, workspaceId }) => {
  const chatRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const [currentMessage, setCurrentMessage] = useState<string>('');
  const [isVisible, setIsVisible] = useState<boolean>(showDialog);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [rating, setRating] = useState<number>(0);
  const [comment, setComment] = useState<string>('');
  const { feedbackService } = useAPIClient();

  const handleTextareaChange = (event: TextareaChangeEvent) => {
    const { textareaValue } = event;
    setComment(textareaValue); // Update the comment state
  };

  useEffect(() => {
    const unsubscribe = chatAnimationMessageSystem.subscribe((msg) => {
      setCurrentMessage(msg);
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 5000);
      return () => clearTimeout(timer);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (chatRef.current) {
      const chatAnimation = gsap.to(chatRef.current, {
        rotation: 20,
        yoyo: true,
        repeat: -1,
        duration: 1,
        ease: 'power1.inOut',
      });
      const stopAnimation = setTimeout(() => {
        chatAnimation.pause();
      }, 2000);
      return () => {
        clearTimeout(stopAnimation);
        chatAnimation.kill();
      };
    }
  }, []);

  useEffect(() => {
    if (showModal) {
      gsap.to('.modal-container', { x: 0, duration: 0.5, ease: 'power2.out' }); // Slide into view
    }
  }, [showModal]);

  useEffect(() => {
    if ((isVisible || showDialog) && textRef.current) {
      const text = currentMessage || message || 'Choisissez un espace de travail ou créez-en un.';
      const splitText = text.split('');
      textRef.current.innerHTML = '';
      splitText.forEach((char, index) => {
        const span = document.createElement('span');
        span.textContent = char;
        span.style.opacity = '0';
        if (textRef.current) {
          textRef.current.appendChild(span);
        }
        gsap.to(span, { opacity: 1, delay: index * 0.05, duration: 0.1 });
      });
    }
  }, [isVisible, showDialog, currentMessage, message]);

  const toggleModal = () => {
    if (!isClickable) return; // Prevent opening if not clickable
    if (showModal) {
      handleCloseModal(); // Ensure animation when closing
    } else {
      setShowModal(true);
    }
  };

  const handleCloseModal = () => {
    gsap.to('.modal-container', {
      x: 500,
      duration: 0.5,
      ease: 'power2.in',
      onComplete: () => {
        setShowModal(false);
        setRating(0); // Reset the rating to 0
        setComment('');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      if (rating === 0) {
        console.error('Please select a rating');
        chatAnimationMessageSystem.showMessage("Sélectionnez une note entre 1 et 5");
        return;
      }

      const feedback = {
        rating: rating,
        comment: comment,
        exigence_id: exigenceId,
        workspace_id: workspaceId,
        generation_id: generationId,
        type: "feedback"
      };

      await feedbackService.submitFeedback(feedback);
      console.log('Feedback submitted successfully');
      chatAnimationMessageSystem.showMessage("Merci pour votre avis ! Il a été envoyé avec succès.");
      
    } catch (error) {
      console.error('Error submitting feedback:', error);
      chatAnimationMessageSystem.showMessage("Une erreur est survenue lors de l'envoi de votre avis.");
    }
    handleCloseModal();
  };

  const renderStars = () => {
    return [1, 2, 3, 4, 5].map((star) => (
      <Star key={star} active={star <= rating} rating={rating} onClick={() => setRating(star)}>
        ★
      </Star>
    ));
  };

  return (
    <div style={{ position: 'fixed', bottom: '10px', right: '10px' }}>
      {(isVisible || showDialog) && (
        <div
          style={{
            position: 'absolute',
            bottom: '100px',
            right: '10px',
            width: '300px',
            backgroundColor: '#ffffff',
            padding: '10px',
            borderRadius: '10px',
            boxShadow: '0 2px 5px rgba(0, 0, 0, 0.3)',
            transform: 'translateX(-10px)',
          }}
        >
          <div ref={textRef}></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-10px',
              right: '10px',
              width: '0',
              height: '0',
              borderLeft: '10px solid transparent',
              borderRight: '10px solid transparent',
              borderTop: '10px solid #ffffff',
            }}
          />
        </div>
      )}
      <ChatContainer>
        {isClickable && (
          <FeedbackIcon>
            <Button  onClick={toggleModal} variant="SECONDARY">
              <VscFeedback/>
              {/* <Icon name="tri-envelope-plus-circle" size="small" className="button is-primary" onClick={toggleModal}/> */}
            </Button>
          </FeedbackIcon>
        )}
        
        <div
          ref={chatRef}
          style={{
            width: '90px',
            height: '90px',
            overflow: 'hidden',
            borderRadius: '50%',
          }}
        >
          <img
            src={cat_ia}
            alt="Chat"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              backgroundColor: 'transparent',
            }}
          />
        </div>
      </ChatContainer>

      {showModal && (
        <ModalContainer className="modal-container">
          <Header>
            <Title level='THREE'>Votre avis nous intéresse</Title>
            <CloseButton onClick={handleCloseModal}>✖</CloseButton>
          </Header>
          <RatingStars>{renderStars()}</RatingStars>
          <Textarea
            placeholder="Votre commentaire..."
            value={comment}
            onChange={handleTextareaChange}
          />
          <Button variant="SECONDARY" onClick={handleSubmit}>Soumettre</Button>
        </ModalContainer>
      )}
    </div>
  );
};

export default ChatAnimation;
