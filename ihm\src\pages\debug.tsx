import React from "react";
import './workspace/workspaceAll.css'
import {ExtractRequirementsForm, ExtractRequirementsFabModal} from "components/ExtractRequirements"
import { CreateRequirementsForm, CreateRequirementsFabModal } from "components/CreateRequirement";
import {Title, Divider, TitleLevels} from "@bytel/trilogy-react-ts"
import { ApiButton } from "components/ApiButton";

/**
 * form that contains single click button to call the backend.
 * Used to check if any CORS error are present
 */
const ApiTestForm: React.FC = () => {

    return (
      <div id="ApiTestForm">
        <ApiButton uri={"/debug/public"}></ApiButton>
        <br/>
        <ApiButton uri={"/debug/private"}></ApiButton>
        <br/>
        <ApiButton uri={"/debug/admin"}></ApiButton>
        <br/>
        <ApiButton uri={"/debug/expected"}></ApiButton>
        <br/>
        <ApiButton uri={"/debug/unexpected"}></ApiButton>
        <br/>
        <ApiButton uri={"/users/me"}></ApiButton>
      </div>
    )
}

export const DebugPage: React.FC = () => {

  const currentWorkspace = "testDebug"

  return (
    <>
      <Title>Appel API backend</Title>
      <ApiTestForm/>
      <Divider/>
      <Title level={TitleLevels.TWO}>Extraction exigences document unique </Title>
      <ExtractRequirementsFabModal currentWorkspace={currentWorkspace}/>
      <br></br>
      <ExtractRequirementsForm currentWorkspace={currentWorkspace}/>
      <Divider />
      <Title level={TitleLevels.TWO}>Création exigence</Title>
      <CreateRequirementsFabModal currentWorkspace={currentWorkspace}/>
      <br/>
      <CreateRequirementsForm currentWorkspace={currentWorkspace}/>
      <Divider />
    </>)
};
