"""
middlewares dédiés à l'observabilitée
"""

from fastapi import FastAPI
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from starlette_exporter import PrometheusMiddleware, handle_metrics, handle_openmetrics

from app.common.config import AppSettings


def init_api_metrics(api: FastAPI, settings: AppSettings):
    """
    TODO: a affiner pour l'instant c'est un placeholder
    """
    if settings.METRICS:
        api.add_middleware(PrometheusMiddleware)
        api.add_route("/metrics", handle_metrics)
        api.add_route("/openmetrics", handle_openmetrics)


def init_api_tracing(api: FastAPI, settings: AppSettings):
    """
    TODO: a affiner pour l'instant c'est un placeholder
    """

    if settings.TRACE:
        FastAPIInstrumentor.instrument_app(api)
