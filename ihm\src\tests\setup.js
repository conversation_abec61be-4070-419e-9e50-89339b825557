import { afterEach, vi } from "vitest";
import { cleanup } from "@testing-library/react";
import "@testing-library/jest-dom/vitest";
import "regenerator-runtime/runtime";

// J'arrive pas à mocker correctement lottie-web, obligé d'en passer par là
HTMLCanvasElement.prototype.getContext = () => {
  return {
    fillRect: vi.fn(),
    fillStyle: vi.fn()
  };
};

// runs a cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

vi.mock("lottie-web", () => ({}));
