<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="540" height="200" viewBox="0 0 540 200" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 270 100)" id="af50450c-1ef6-4ef9-a59c-c361fec8aa73"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-270" y="-100" rx="0" ry="0" width="540" height="200" />
</g>
<g transform="matrix(Infinity NaN NaN Infinity 0 0)" id="c8cbb82a-2b3f-4d52-a9a5-b94b82de8278"  >
</g>
<g transform="matrix(20.9 0 0 20.9 270 100)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 4.14 0)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-16.14, -12)" d="M 13.740025 10.8628 C 13.864799999999999 10.8628 13.985999999999999 10.880625 14.10365 10.916275 C 14.217725 10.955475 14.278325 11.005375 14.278325 11.066 C 14.278325 11.098075000000001 14.267625 11.13015 14.246225 11.158675 C 14.231975 11.1872 14.199900000000001 11.208575 14.167800000000002 11.208575 C 14.121475000000002 11.194325 14.075125000000002 11.176499999999999 14.028775000000001 11.1551 C 13.939650000000002 11.1159 13.8434 11.094499999999998 13.7436 11.098075 C 13.6402 11.0945 13.533275000000001 11.11945 13.44415 11.172925 C 13.365725000000001 11.2264 13.32295 11.319099999999999 13.330075 11.41535 C 13.326500000000001 11.50445 13.362150000000002 11.590025 13.429875000000001 11.64705 C 13.504750000000001 11.707650000000001 13.590300000000001 11.757575000000001 13.683000000000002 11.786075 C 13.782800000000002 11.821725 13.882625 11.86095 13.982425000000001 11.907300000000001 C 14.08225 11.9572 14.167800000000002 12.032050000000002 14.231975 12.12475 C 14.303275000000001 12.238825 14.338925 12.370725 14.33535 12.506175 C 14.33535 12.7165 14.27475 12.876925 14.149975 12.983875000000001 C 14.014524999999999 13.09795 13.83985 13.154975 13.6616 13.144275 C 13.501175 13.14785 13.340775 13.108625 13.201725 13.0302 C 13.0734 12.944650000000001 13.009224999999999 12.869800000000001 13.009224999999999 12.794925000000001 C 13.009224999999999 12.759275 13.023499999999999 12.727200000000002 13.044875 12.698675000000001 C 13.0627 12.673725000000001 13.087674999999999 12.6559 13.11975 12.652350000000002 C 13.148275 12.655900000000003 13.173225 12.670150000000001 13.19105 12.691550000000001 C 13.21955 12.716500000000002 13.248075 12.745025000000002 13.280175 12.7771 C 13.32295 12.816325 13.37285 12.844850000000001 13.426325 12.86265 C 13.497625000000001 12.891175 13.57605 12.901875 13.6509 12.901875 C 13.764975 12.909 13.8755 12.87335 13.9646 12.805625000000001 C 14.043025 12.734325 14.085825000000002 12.627375 14.078675 12.52045 C 14.08225 12.41705 14.0466 12.3208 13.9753 12.24595 C 13.904 12.17465 13.81845 12.121175000000001 13.7222 12.085525 C 13.6224 12.049875 13.526125 12.010675 13.42275 11.9679 C 13.32295 11.9251 13.237375 11.86095 13.16965 11.7754 C 13.09835 11.67915 13.05915 11.561499999999999 13.066275000000001 11.440299999999999 C 13.062700000000001 11.34405 13.084100000000001 11.247799999999998 13.126875000000002 11.158674999999999 C 13.162525000000002 11.083799999999998 13.219550000000002 11.0232 13.290850000000002 10.980424999999999 C 13.358600000000003 10.941225 13.429875000000003 10.9127 13.504750000000003 10.8913 C 13.583175000000002 10.873475 13.661600000000004 10.8628 13.740025000000003 10.8628 Z M 11.914824999999999 11.03035 C 12.03605 11.137275 12.096649999999999 11.3084 12.096649999999999 11.53655 L 12.096649999999999 12.459825 C 12.096649999999999 12.691550000000001 12.036049999999998 12.8591 11.914824999999999 12.966050000000001 C 11.629649999999998 13.1835 11.237524999999998 13.1835 10.952324999999998 12.966050000000001 C 10.834699999999998 12.859100000000002 10.774099999999999 12.691550000000001 10.774099999999999 12.459825 L 10.774099999999999 11.53655 C 10.774099999999999 11.304825 10.8347 11.137275 10.952324999999998 11.03035 C 11.237524999999998 10.81645 11.629649999999998 10.81645 11.914824999999999 11.03035 Z M 20.723525 10.869925 C 20.809075 10.869925 20.86255 10.89845 20.880374999999997 10.95905 L 21.511349999999997 12.991 L 21.511349999999997 13.008825 C 21.511349999999997 13.0409 21.489974999999998 13.069425 21.461449999999996 13.083675 C 21.429374999999997 13.105075 21.390149999999995 13.115775 21.347374999999996 13.1122 C 21.308149999999998 13.115775 21.268949999999997 13.094375 21.254699999999996 13.055175 L 21.119224999999997 12.606 L 20.313574999999997 12.606 L 20.178099999999997 13.055175 C 20.167424999999998 13.094375 20.124624999999998 13.119325 20.085424999999997 13.115775000000001 C 20.049774999999997 13.115775000000001 20.014124999999996 13.105075000000001 19.982049999999997 13.083675000000001 C 19.949949999999998 13.069425 19.932125 13.0409 19.932125 13.005250000000002 C 19.932125 12.998125000000002 19.9357 12.987425000000002 19.939275 12.980300000000002 L 20.566675 10.959050000000001 C 20.5845 10.89845 20.637975 10.869925000000002 20.723525 10.869925000000002 Z M 17.543699999999998 10.869925 C 17.6257 10.869925 17.693424999999998 10.9234 17.750449999999997 11.033900000000001 L 18.231699999999996 11.9572 L 18.716524999999997 11.033900000000001 C 18.777124999999998 10.926950000000001 18.844875 10.869925 18.923299999999998 10.869925 C 18.9839 10.859225 19.040924999999998 10.902000000000001 19.051624999999998 10.9626 C 19.0552 10.9733 19.0552 10.984 19.051624999999998 10.9947 L 19.051624999999998 13.01595 C 19.051624999999998 13.044475 19.037374999999997 13.069425 19.0124 13.083675 C 18.955375 13.115775 18.88765 13.115775 18.8306 13.083675 C 18.80565 13.069424999999999 18.7914 13.044475 18.7914 13.01595 L 18.7914 11.4082 L 18.327975 12.278025000000001 C 18.313699999999997 12.320800000000002 18.2745 12.352900000000002 18.22815 12.352900000000002 C 18.185375 12.349325000000002 18.14615 12.320800000000002 18.131899999999998 12.278025000000001 L 17.668474999999997 11.390400000000001 L 17.668474999999997 13.015950000000002 C 17.668474999999997 13.044475000000002 17.654199999999996 13.069425000000003 17.62925 13.083675000000001 C 17.6043 13.101500000000001 17.572225 13.112200000000001 17.543699999999998 13.112200000000001 C 17.511625 13.112200000000001 17.479525 13.105075000000001 17.454575 13.087250000000001 C 17.42605 13.076550000000001 17.408224999999998 13.048025 17.4118 13.019525000000002 L 17.4118 10.99825 C 17.4011 10.93765 17.443875 10.880625 17.5045 10.869925 C 17.51875 10.86635 17.533 10.86635 17.5437 10.869925 Z M 22.527324999999998 10.86635 C 22.566549999999996 10.86635 22.605749999999997 10.880625 22.637849999999997 10.905575 C 22.673499999999997 10.941225000000001 22.705574999999996 10.984 22.723399999999998 11.03035 L 23.489825 12.527575 L 23.489825 10.955475 C 23.489825 10.92695 23.5041 10.902 23.529049999999998 10.8913 C 23.586074999999997 10.859224999999999 23.653824999999998 10.859224999999999 23.710849999999997 10.8913 C 23.735799999999998 10.905574999999999 23.750074999999995 10.930525 23.750074999999995 10.955475 L 23.750074999999995 13.019525 C 23.750074999999995 13.048024999999999 23.735799999999994 13.073 23.710849999999997 13.08725 C 23.682325 13.105075 23.653824999999998 13.1122 23.621724999999998 13.1122 C 23.550449999999998 13.108625 23.486275 13.0623 23.461325 12.99455 L 22.655675 11.451 L 22.655675 13.019525 C 22.655675 13.048024999999999 22.641399999999997 13.073 22.620025 13.08725 C 22.562974999999998 13.119325 22.49525 13.119325 22.4382 13.08725 C 22.413249999999998 13.072999999999999 22.398999999999997 13.048024999999999 22.398999999999997 13.019525 L 22.398999999999997 10.955475 C 22.398999999999997 10.894874999999999 22.441774999999996 10.86635 22.527324999999998 10.86635 Z M 8.642324999999998 10.86635 L 9.223399999999998 10.86635 C 9.394499999999997 10.859225 9.565599999999998 10.916275 9.697499999999998 11.026775 C 9.815149999999997 11.133725 9.875749999999998 11.301275 9.875749999999998 11.529425 L 9.875749999999998 11.557925 C 9.875749999999998 11.786074999999999 9.815149999999997 11.957199999999998 9.693949999999997 12.06415 C 9.558474999999998 12.17465 9.387374999999997 12.235249999999999 9.212699999999998 12.224549999999999 L 8.799174999999998 12.224549999999999 L 8.799174999999998 13.019524999999998 C 8.799174999999998 13.048024999999997 8.784924999999998 13.072999999999999 8.759949999999998 13.087249999999997 C 8.702924999999999 13.119324999999998 8.635199999999998 13.119324999999998 8.578149999999997 13.087249999999997 C 8.553199999999997 13.072999999999997 8.538949999999998 13.048024999999997 8.538949999999998 13.019524999999998 L 8.538949999999998 10.955474999999998 C 8.538949999999998 10.930524999999998 8.549624999999999 10.909124999999998 8.567449999999997 10.891299999999998 C 8.588849999999997 10.873474999999997 8.613799999999998 10.862799999999998 8.642324999999998 10.866349999999997 Z M 16.442175 10.86635 C 16.470675 10.86635 16.495649999999998 10.87705 16.5099 10.902000000000001 C 16.527724999999997 10.9234 16.53485 10.951925000000001 16.53485 10.980425 C 16.53485 11.00895 16.527725 11.041025000000001 16.5099 11.066 C 16.495649999999998 11.090950000000001 16.470675 11.101625 16.442175 11.101625 L 15.953774999999998 11.101625 L 15.953774999999998 13.019525 C 15.953774999999998 13.048024999999999 15.939524999999998 13.073 15.914575 13.08725 C 15.857524999999999 13.119325 15.7898 13.119325 15.732774999999998 13.08725 C 15.707824999999998 13.072999999999999 15.693549999999998 13.048024999999999 15.693549999999998 13.019525 L 15.693549999999998 11.101625 L 15.198049999999999 11.101625 C 15.169524999999998 11.101625 15.144574999999998 11.09095 15.130299999999998 11.066 C 15.112474999999998 11.041025000000001 15.101799999999999 11.012525 15.101799999999999 10.980425 C 15.101799999999999 10.951925000000001 15.108925 10.9234 15.12675 10.902000000000001 C 15.141 10.87705 15.165949999999999 10.86635 15.194474999999999 10.86635 L 16.442175 10.86635 Z M 11.437149999999999 11.098075000000001 C 11.169775 11.098075000000001 11.037875 11.240675000000001 11.037875 11.532975000000002 L 11.034324999999999 12.459825000000002 C 11.034324999999999 12.748600000000001 11.166224999999999 12.894750000000002 11.437149999999999 12.894750000000002 C 11.547649999999999 12.901875000000002 11.654599999999999 12.862675000000001 11.7366 12.787800000000002 C 11.815024999999999 12.698675000000003 11.850674999999999 12.581050000000003 11.843549999999999 12.459825000000002 L 11.843549999999999 11.532975000000002 C 11.854225 11.415350000000002 11.815024999999999 11.297700000000003 11.7366 11.205025000000003 C 11.654599999999999 11.130150000000002 11.547649999999999 11.090950000000003 11.437149999999999 11.098075000000003 Z M 20.723525 11.244225000000002 L 20.37775 12.392100000000003 L 21.065749999999998 12.392100000000003 L 20.723525 11.244225000000004 Z M 9.212699999999998 11.101625000000002 L 8.799174999999998 11.101625000000002 L 8.795599999999999 12.010675000000003 L 9.212699999999998 12.010675000000003 C 9.323199999999998 12.017800000000003 9.433724999999997 11.978575000000003 9.512149999999998 11.900150000000002 C 9.590574999999998 11.811050000000002 9.629774999999999 11.693400000000002 9.619074999999999 11.572200000000002 L 9.619074999999999 11.536550000000002 C 9.629774999999999 11.418900000000002 9.590575 11.301275000000002 9.512149999999998 11.208575000000002 C 9.430149999999998 11.133725000000002 9.323199999999998 11.094500000000002 9.212699999999998 11.101625000000002 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.18 0)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.82, -12)" d="M 7.351775 12.452775 C 7.601325 10.49925 6.221725 8.713275000000001 4.271775 8.463725 C 2.3218075 8.2142 0.5286999999999997 9.593775 0.2791600000000001 11.54375 C 0.029623000000000066 13.493699999999999 1.4092125 15.283249999999999 3.359175 15.536349999999999 C 5.3126999999999995 15.785899999999998 7.098675 14.406299999999998 7.351775 12.452774999999999 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.86 -0.61)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.14, -11.39)" d="M 5.102275 10.723775 L 3.5979249999999996 12.228125 L 3.1736999999999997 11.803925 C 4.6531 10.324525 4.799275 10.45285 5.102275 10.723775 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.87 -0.61)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.13, -11.39)" d="M 3.598075 12.263725 C 3.587375 12.263725 3.58025 12.260150000000001 3.573125 12.253025000000001 L 3.14535 11.828800000000001 C 3.131075 11.81455 3.131075 11.79315 3.14535 11.778900000000002 C 4.6497 10.274550000000001 4.80655 10.417125000000002 5.123825 10.702325000000002 C 5.13095 10.709450000000002 5.134525 10.716575000000002 5.134525 10.727275000000002 C 5.134525 10.737975000000002 5.13095 10.745100000000003 5.123825 10.752225000000003 L 3.61945 12.253025000000003 C 3.6159 12.260150000000003 3.6052 12.263725000000003 3.598075 12.263725000000003 Z M 3.2237750000000003 11.80385 L 3.598075 12.17815 L 5.04895 10.727275 C 4.78515 10.492 4.6069249999999995 10.4207 3.223775 11.80385 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.56 -0.3)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.44, -11.7)" d="M 4.029225 12.659675 L 3.6192750000000005 12.249725 L 5.1236250000000005 10.74535 C 5.5264500000000005 11.15175 4.924 11.811250000000001 4.029225 12.659675 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.56 -0.3)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.44, -11.7)" d="M 4.029325 12.6952 C 4.018625 12.6952 4.0115 12.69165 4.0043750000000005 12.6845 L 3.5944000000000003 12.27455 C 3.5872750000000004 12.267425 3.5872750000000004 12.260299999999999 3.5872750000000004 12.2496 C 3.5872750000000004 12.2389 3.5908500000000005 12.231774999999999 3.5979750000000004 12.224649999999999 L 5.102325 10.720299999999998 C 5.1166 10.706024999999999 5.137975000000001 10.706024999999999 5.15225 10.720299999999998 C 5.244925 10.805849999999998 5.294825 10.927049999999998 5.291275000000001 11.051824999999997 C 5.28415 11.447524999999997 4.834975000000001 11.950149999999997 4.05785 12.684499999999998 C 4.04715 12.691649999999997 4.03645 12.695199999999998 4.029325 12.695199999999998 Z M 3.669275 12.2496 C 3.8974249999999997 12.481325 3.9936749999999996 12.574 4.029325 12.609649999999998 C 4.6282 12.039274999999998 5.209275 11.454649999999999 5.2128499999999995 11.051825 C 5.216399999999999 10.959124999999998 5.18075 10.866449999999999 5.12015 10.79515 L 3.669275 12.2496 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.87 0.04)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.13, -12.04)" d="M 3.1807 11.814675 L 3.4837249999999997 12.1177 C 3.4908499999999996 12.124825 3.4908499999999996 12.13195 3.4837249999999997 12.139075 C 3.4801499999999996 12.14265 3.4801499999999996 12.14265 3.4765749999999995 12.14265 L 2.849175 12.2781 C 2.8171 12.281675 2.788575 12.260275 2.78145 12.228200000000001 C 2.777875 12.210375 2.785 12.19255 2.7957 12.18185 L 3.159325 11.81825 C 3.1664499999999998 11.811100000000001 3.17715 11.80755 3.1807 11.814675000000001 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.86 0.04)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.14, -12.04)" d="M 2.838525 12.313675 C 2.785075 12.313675 2.7458500000000003 12.2709 2.7458500000000003 12.217425 C 2.7458500000000003 12.192475 2.7565500000000003 12.1675 2.7743750000000005 12.149675 L 3.1379750000000004 11.786075 C 3.1593750000000003 11.76825 3.1879000000000004 11.76825 3.2092750000000003 11.786075 L 3.5123 12.089075000000001 C 3.533675 12.106900000000001 3.533675 12.139000000000001 3.5123 12.160375000000002 C 3.50515 12.167500000000002 3.498025 12.171075000000002 3.4873250000000002 12.174650000000002 L 2.8599250000000005 12.310100000000002 C 2.8528000000000007 12.310100000000002 2.8456750000000004 12.313675000000002 2.8385250000000006 12.313675000000002 Z M 3.170075 11.8538 L 2.8207000000000004 12.203149999999999 C 2.8135750000000006 12.210299999999998 2.8100250000000004 12.220975 2.8171500000000003 12.231675 C 2.8207000000000004 12.242375 2.8314000000000004 12.245925 2.8421000000000003 12.242375 L 3.4303000000000003 12.114049999999999 L 3.170075 11.8538 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -6.59 -1.69)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5.41, -10.31)" d="M 5.89725 9.889625 C 5.669099999999999 9.668600000000001 5.301925 9.67575 5.080925 9.90745 C 4.8599 10.139175000000002 4.867025 10.502775000000002 5.09875 10.7238 C 5.287675 10.905600000000002 5.57285 10.937700000000001 5.7974499999999995 10.802225 L 5.39105 10.395825 L 5.89725 9.889625 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -6.59 -1.69)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5.41, -10.31)" d="M 5.49805 10.919775 C 5.159375 10.919775 4.8849 10.645299999999999 4.8849 10.306625 C 4.8849 9.967975000000001 5.159375 9.693475000000001 5.49805 9.693475000000001 C 5.6549000000000005 9.693475000000001 5.808175 9.754075000000002 5.92225 9.864600000000001 C 5.929375 9.871725000000001 5.93295 9.878850000000002 5.93295 9.889550000000002 C 5.93295 9.900250000000002 5.929375 9.907375000000002 5.92225 9.914500000000002 L 5.441 10.395750000000001 L 5.818875 10.773625000000001 C 5.833125 10.787875000000001 5.833125 10.809275000000001 5.818875 10.823525 L 5.81175 10.83065 C 5.71905 10.8877 5.60855 10.919775 5.49805 10.919775 Z M 5.49805 9.76835 C 5.1986 9.76835 4.956175 10.01075 4.95975 10.3102 C 4.95975 10.60965 5.20215 10.85205 5.5016 10.848475 C 5.5836 10.848475 5.665575 10.83065 5.74045 10.791450000000001 L 5.36615 10.420700000000002 C 5.359 10.413575000000002 5.35545 10.406450000000001 5.35545 10.395750000000001 C 5.35545 10.385050000000001 5.359 10.377925000000001 5.36615 10.370800000000001 L 5.843825 9.8931 C 5.747574999999999 9.811125 5.6263749999999995 9.76835 5.49805 9.76835 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -6.27 -1.65)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5.73, -10.35)" d="M 5.908025 9.90045 L 5.900875 9.8933 L 5.391125 10.39595 L 5.79395 10.798775 C 5.83315 10.773824999999999 5.872375 10.745299999999999 5.90445 10.713225 C 6.1326 10.488624999999999 6.1326 10.125024999999999 5.908024999999999 9.90045 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -6.27 -1.65)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5.73, -10.35)" d="M 5.797375 10.837775 C 5.7867 10.837775 5.7795499999999995 10.834200000000001 5.772425 10.827075 L 5.36605 10.4207 C 5.3589 10.41355 5.3553500000000005 10.406425 5.3553500000000005 10.395725 C 5.3553500000000005 10.38505 5.3589 10.3779 5.36605 10.370775 L 5.87225 9.864575 C 5.8865 9.850325 5.907900000000001 9.850325 5.92215 9.864575 L 5.93285 9.8717 C 6.1717 10.11055 6.1717 10.495550000000001 5.936425 10.737950000000001 C 5.900774999999999 10.773600000000002 5.86155 10.805700000000002 5.818775 10.830650000000002 C 5.808075 10.834200000000003 5.800949999999999 10.837775000000002 5.797375 10.837775000000002 Z M 5.4409 10.395725 L 5.80095 10.755775 C 5.829475 10.73795 5.857975000000001 10.713 5.8793750000000005 10.691625 C 6.082575 10.488425 6.093275 10.1569 5.897200000000001 9.943 L 5.440900000000001 10.395724999999999 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.63 -0.46)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.37, -11.54)" d="M 5.162875 10.787925 C 5.077325 10.702375 4.938275 10.702375 4.8527249999999995 10.787925 L 3.5087999999999995 12.131874999999999 L 3.7333749999999997 12.356449999999999 L 5.155749999999999 11.108749999999999 C 5.248424999999999 11.030325 5.2555499999999995 10.8913 5.177124999999999 10.798625 C 5.169999999999999 10.79505 5.166424999999999 10.791475 5.162875 10.787925 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.64 -0.46)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.36, -11.54)" d="M 3.729725 12.392225 C 3.7190250000000002 12.392225 3.7119 12.38865 3.70475 12.381525 L 3.480175 12.15695 C 3.465925 12.142675 3.465925 12.1213 3.480175 12.107025 L 4.824125 10.7631 C 4.9239250000000006 10.663275 5.084350000000001 10.663275 5.184175000000001 10.7631 C 5.283975000000001 10.8629 5.283975000000001 11.023325 5.184175000000001 11.123149999999999 C 5.180600000000001 11.1267 5.177025 11.130275 5.173475000000001 11.133825 L 3.751100000000001 12.381525 C 3.747550000000001 12.38865 3.740400000000001 12.392225 3.729725000000001 12.392225 Z M 3.5586 12.132 L 3.7332750000000003 12.306675 L 5.1307 11.080375 C 5.209125 11.0162 5.21625 10.89855 5.152075 10.820125 C 5.087925 10.741700000000002 4.970275 10.734575000000001 4.89185 10.79875 C 4.888275 10.8023 4.8847249999999995 10.805875 4.8776 10.809425000000001 L 3.5586 12.132000000000001 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.99 1.2)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.02, -13.2)" d="M 2.4785175 13.661325 C 2.4642575 13.66845 2.4571275000000004 13.6827 2.4606925 13.696975 L 2.5213 13.953625 C 2.53555 13.989275000000001 2.5141750000000003 14.03205 2.4749525 14.04275 C 2.4464325000000002 14.05345 2.41435 14.04275 2.396525 14.021374999999999 L 2.004395 13.6328 L 3.2841750000000003 12.353024999999999 L 3.7262000000000004 12.360149999999999 L 4.025650000000001 12.6596 C 3.9543500000000007 12.7202 3.5230000000000006 13.1373 2.4785175000000006 13.661325 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.98 1.2)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.02, -13.2)" d="M 2.4501975 14.0784 C 2.4216775 14.0784 2.39316 14.067725 2.3753349999999998 14.046325 L 1.9867675 13.65775 C 1.97964 13.650625 1.976075 13.6435 1.976075 13.6328 C 1.976075 13.6221 1.97964 13.614975 1.9867675 13.60785 L 3.26655 12.328074999999998 C 3.273675 12.320949999999998 3.2843750000000003 12.317374999999998 3.2915 12.317374999999998 L 3.733525 12.324499999999999 C 3.744225 12.324499999999999 3.7513500000000004 12.328074999999998 3.7585 12.335199999999999 L 4.057925 12.634649999999999 C 4.065075 12.641774999999999 4.068625 12.652474999999999 4.068625 12.663174999999999 C 4.068625 12.673874999999999 4.065075 12.681 4.054375 12.688125 L 4.029425000000001 12.709525 C 3.6515500000000007 13.041049999999998 3.1382000000000008 13.372575 2.5001000000000007 13.68985 L 2.5607000000000006 13.94295 C 2.5714000000000006 13.9893 2.5500000000000007 14.0392 2.5072250000000005 14.06415 C 2.4858450000000003 14.07485 2.4680200000000005 14.0784 2.4501975000000007 14.0784 Z M 2.058065 13.6328 L 2.4252425 13.996425 C 2.4359375 14.01425 2.4573275 14.021375 2.47515 14.010675 C 2.4929750000000004 13.999975000000001 2.5001 13.9786 2.4894100000000003 13.960775000000002 L 2.4288075000000005 13.704100000000002 C 2.4216775000000004 13.672025000000001 2.4359375000000005 13.643500000000001 2.4644550000000005 13.629250000000003 C 3.0954250000000005 13.311975000000002 3.6052000000000004 12.984000000000002 3.9795000000000007 12.659600000000003 L 3.715700000000001 12.395800000000003 L 3.305750000000001 12.388675000000003 L 2.058065000000001 13.632800000000003 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -9.92 1.86)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-2.08, -13.86)" d="M 1.7015375 13.939275 L 2.0081124999999997 13.6327 L 2.46441 14.089 L 1.737185 14.039075 C 1.7051 14.035525 1.6837125 14.007 1.6872775 13.974925 C 1.6872775 13.960650000000001 1.6908425 13.9464 1.7015375 13.939275 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -9.93 1.86)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-2.07, -13.86)" d="M 2.4642975 14.120975 L 1.7335049999999999 14.071075 C 1.6800324999999998 14.0675 1.6443849999999998 14.021175000000001 1.6479499999999998 13.9677 C 1.6515149999999998 13.9463 1.6586449999999997 13.924925 1.67647 13.91065 L 1.983045 13.604075 C 1.9973025 13.589825 2.0186925 13.589825 2.0329525 13.604075 L 2.48925 14.060375 C 2.4999450000000003 14.071075 2.5035000000000003 14.085325000000001 2.4963800000000003 14.0996 C 2.48925 14.113850000000001 2.478555 14.120975000000001 2.4642975000000003 14.120975000000001 Z M 2.0079975 13.6825 L 1.7263775000000001 13.964125 C 1.7156825000000002 13.97125 1.7156825000000002 13.989075 1.7263775000000001 13.9962 C 1.7299425000000002 13.999775 1.733505 14.00335 1.7406350000000002 14.00335 L 2.37161 14.046125 L 2.0079975 13.6825 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.55 0.31)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.45, -12.31)" d="M 3.15955 12.51705 C 3.13815 12.51705 3.1239 12.499225 3.1239 12.481399999999999 C 3.1239 12.470724999999998 3.127475 12.4636 3.1346 12.456449999999998 L 3.480375 12.110674999999999 C 3.49465 12.0964 3.516025 12.0964 3.5303 12.110674999999999 L 3.754875 12.335249999999998 C 3.765575 12.345949999999998 3.7691250000000003 12.356649999999998 3.765575 12.370899999999999 C 3.762 12.381599999999999 3.7513 12.392299999999999 3.73705 12.39585 L 3.166675 12.51705 L 3.1595500000000003 12.51705 Z M 3.50535 12.185525 L 3.27005 12.4208 L 3.65505 12.338825 L 3.50535 12.185525 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.5 0.29)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.5, -12.29)" d="M 3.72625 12.36005 L 3.334125 12.445625 C 3.3055999999999996 12.45275 3.2771 12.434925 3.2699749999999996 12.4064 C 3.2663999999999995 12.388575 3.2699499999999997 12.37075 3.2842249999999997 12.3565 L 3.5016749999999996 12.139050000000001 L 3.72625 12.360050000000001 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.5 0.29)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-3.5, -12.29)" d="M 3.32715 12.4814 C 3.27725 12.4814 3.238025 12.442200000000001 3.238025 12.3923 C 3.238025 12.36735 3.248725 12.34595 3.263 12.328125 L 3.48045 12.110675 C 3.4947 12.096400000000001 3.5161 12.096400000000001 3.53035 12.110675 L 3.75495 12.33525 C 3.765625 12.34595 3.7692 12.35665 3.765625 12.3709 C 3.762075 12.3816 3.751375 12.3923 3.737125 12.395850000000001 L 3.344975 12.4814 L 3.3271499999999996 12.4814 Z M 3.5054 12.185525 L 3.3129 12.378025000000001 C 3.305775 12.385150000000001 3.305775 12.3923 3.309325 12.399425 C 3.3129 12.406550000000001 3.320025 12.410125 3.3307249999999997 12.410125 L 3.6586749999999997 12.338825 L 3.5054 12.185525 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -6.13 -1.65)"  >
<path style="stroke: none; stroke-width: 0.25; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,108,55); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-5.87, -10.35)" d="M 5.911425 10.2426 C 5.9043 10.221224999999999 5.8793500000000005 10.210524999999999 5.857950000000001 10.217649999999999 C 5.836575000000001 10.224775 5.825875000000001 10.249725 5.833000000000001 10.271125 C 5.833000000000001 10.2747 5.836575000000001 10.27825 5.836575000000001 10.281825 C 5.857950000000001 10.3246 5.850825 10.378074999999999 5.822300000000001 10.417275 C 5.8080500000000015 10.4351 5.811625000000001 10.46005 5.825875000000001 10.474325 C 5.843700000000001 10.488575 5.868650000000001 10.485025 5.882900000000001 10.4672 C 5.936375000000001 10.39945 5.947075000000001 10.3139 5.911425000000001 10.2426 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>