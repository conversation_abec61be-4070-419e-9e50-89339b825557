#!/usr/bin/env python3
"""
Script de test pour vérifier le nouveau service de statut des workspaces
"""

import json

import requests


def test_workspace_status():
    """Test du nouveau endpoint /workspaces/{id}/isbusy"""

    # URL de base de l'API (à adapter selon votre configuration)
    base_url = "http://localhost:8000"

    # ID d'un workspace de test (à remplacer par un ID valide)
    workspace_id = "e682a9fe7ce74a4588b6e880fcc35ffa"  # ID de test du code

    try:
        # Appel du nouveau endpoint
        response = requests.get(f"{base_url}/workspaces/{workspace_id}/isbusy")

        if response.status_code == 200:
            data = response.json()
            print("✅ Succès ! Réponse du service :")
            print(json.dumps(data, indent=2, ensure_ascii=False))

            # Vérification de la structure de la réponse
            if "is_busy" in data and "last_identification_request" in data:
                print("✅ Structure de réponse correcte")

                if data["last_identification_request"]:
                    print("✅ Dernière demande d'identification trouvée")
                    req = data["last_identification_request"]
                    required_fields = [
                        "demandeur_login",
                        "date_creation",
                        "statut",
                    ]
                    for field in required_fields:
                        if field in req:
                            print(f"  ✅ Champ '{field}': {req[field]}")
                        else:
                            print(f"  ❌ Champ manquant: {field}")
                else:
                    print(
                        "ℹ️  Aucune demande d'identification trouvée pour ce workspace"
                    )
            else:
                print("❌ Structure de réponse incorrecte")

        else:
            print(f"❌ Erreur HTTP {response.status_code}: {response.text}")

    except requests.exceptions.ConnectionError:
        print(
            "❌ Impossible de se connecter à l'API. Vérifiez que le serveur est démarré."
        )
    except Exception as e:
        print(f"❌ Erreur: {e}")


if __name__ == "__main__":
    print("Test du nouveau service de statut des workspaces")
    print("=" * 50)
    test_workspace_status()
