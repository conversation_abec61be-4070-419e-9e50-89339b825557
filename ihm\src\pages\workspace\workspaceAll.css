/* --- workspace.css + workspaceDetail.css --- */

body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  font-family: Arial, sans-serif;
}

.workspaces-page {
  display: flex;
  flex-direction: column;
  width: 80%;
  justify-content: flex-start;
  align-items: center;
  margin: auto;
  overflow-y: auto;
}

.head {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 20px 0;
}

.headline-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.headline {
  font-weight: bold;
  font-size: 2rem;
  margin: 0;
}

.create-button {
  margin-left: auto;
}

.workspaces-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.workspace-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
  margin: 10px;
  flex: 0 1 calc(20% - 20px);
}

.workspace-card:hover {
  box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.2);
}

.workspace-card:hover .hover-content {
  display: block;
}

.hover-content {
  display: none;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.page-container {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  padding-left: 20px;
  padding-right: 20px;
}

.box {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.box-header-custom {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.title-white {
  color: white;
  text-align: center;
}

.breadcrumb {
  display: flex;
  flex-wrap: nowrap;
  list-style: none;
  padding: 0;
  margin-bottom: 1rem;
}

.breadcrumb-nav {
  margin-top: 10px;
}

.breadcrumb-item {
  display: inline;
}

.breadcrumb-item+.breadcrumb-item::before {
  padding: 0;
  color: #6c757d;
}

.banner {
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.headline-grey {
  color: #6c757d;
  font-size: 1.5rem;
  margin-bottom: 10px;
  text-align: center;
}

.banner-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.description-container {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  background-color: #fff;
  margin-right: 20px;
}

.description-title {
  margin-bottom: 10px;
  color: #6c757d;
}

.banner-buttons {
  display: flex;
  gap: 10px;
  margin-left: 20px;
}

.identification-container {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  background-color: #fff;
}

.requirements-title {
  margin-top: 20px;
  margin-bottom: 10px;
  width: 35%;
}

.search-bar {
  width: 34%;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 5px 10px;
  background-color: #fff;
  box-sizing: border-box;
}

.search-bar input {
  width: 100%;
  border: none;
  outline: none;
}

.requirements-container {
  width: 34%;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  background-color: #fff;
  max-width: 34%;
}

.requirement-row {
  background-color: #eff2f8;
  color: inherit;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 10px;
  border-radius: 8px;
}

.requirement-row:hover {
  background-color: #d1dbe7;
}

.requirement-row.active {
  background-color: #25465F;
  color: #fff;
}

.requirement-row-content {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex-grow: 1;
}

.delete-button {
  background: none;
  border: none;
  color: inherit;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;
}

.generations-container {
  width: 64%;
  max-height: 520px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  background-color: #fff;
  margin-left: 20px;
  max-width: 64%;
}

.bouchons-container {
  width: 64%;
  max-height: 520px;
  overflow-y: auto;
  padding: 10px;
  background-color: #fff;
  margin-left: 20px;
  max-width: 64%;
}

.generation-detail-container {
  text-align: left;
}

.generation-steps-title {
  margin-top: 10px;
  flex-grow: 3;
  color: #6c757d;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
}

.generation-detail-container .status-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-text {
  display: flex;
  align-items: center;
}

.status-title {
  font-size: 1.2rem;
  font-weight: 500;
}

.status-value {
  margin-left: 5px;
  font-size: 1.2rem;
  font-weight: 500;
}

.status-blue {
  font-family: "Bouygues Read", sans-serif;
  font-size: 22px;
  line-height: 27px;
  margin-bottom: 16px;
  color: #0C7B91;
}

.status-green {
  font-family: "Bouygues Read", sans-serif;
  font-size: 22px;
  line-height: 27px;
  margin-bottom: 16px;
  color: #007B52;
}

.status-red {
  font-family: "Bouygues Read", sans-serif;
  font-size: 22px;
  line-height: 27px;
  margin-bottom: 16px;
  color: #D42D02;
}

.generation-detail-container .status-buttons {
  display: flex;
  gap: 8px;
}

.generation-detail-container .status-button {
  min-width: 90px;
}

.details-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.details-container .top-row {
  display: flex;
  gap: 20px;
}

.details-container .bottom-row {
  display: flex;
  justify-content: center;
}

.filter-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.filter-tabs button {
  font-size: 14px;
  padding: 5px 10px;
}

.tag-container {
  margin-bottom: 10px;
  cursor: pointer;
}

.create-button-modal {
  margin-top: 20px;
}

.tab-context {
  margin-bottom: 10px;
}

.tab-context .tabs>.tab.is-active {
  padding: 0%;
}

.requirements-header {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 20px;
}

.requirement-banner {
  background-color: #eef5f9;
  padding: 10px;
  border-radius: 8px;
  display: flex;
  gap: 30px;
  align-items: last baseline;
  width: 85%;
  margin-left: 20px;
}

.description-exigence {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
}

.requirements-buttons {
  display: flex;
  gap: 15px;
}

.testcases-header {
  display: flex;
  gap: 40px;
  justify-content: flex-start;
  width: 100%;
}

.right-content {
  margin-left: auto;
}

.modal-large .modal-content {
  width: 80% !important;
}

.modal-large-panel .modal-content {
  min-width: 75% !important;
  max-width: 100% !important;
}

.field-header {
  display: flex;
  justify-content: space-between;
}

.dropdown-container {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 110%;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 10;
  min-width: 180px;
}

.dropdown-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
}

.dropdown-button.active {
  color: #25465F;
}

.dropdown-button.inactive {
  color: #333;
}

.dropdown-button.disabled {
  cursor: not-allowed;
  color: #999;
  opacity: 0.7;
}

.dropdown-icon {
  margin-right: 6px;
}

.fab {
  border-radius: 12px !important;
  cursor: pointer !important;
  height: 44px !important;
  color: #fff !important;
  padding: 12px !important;
  display: inline-flex !important;
}

.tag.is-success.is-inverted {
  color: #007b52 !important;
  border: 2px solid #007b52 !important;
  box-shadow: 0 0 4px #1976d233;
  /* color: rgb(0, 123, 82) !important;
  background-color: #fff !important; */
}

.tag.is-error.is-inverted {
  color: #d42d02 !important;
  border: 2px solid #d42d02 !important;
  box-shadow: 0 0 4px #1976d233;
  /* color: #b52222 !important;
  background-color: #fff !important; */
}

.tag.is-secondary.is-inverted {
  color: #0c7b91 !important;
  border: 2px solid #0c7b91 !important;
  box-shadow: 0 0 4px #1976d233;
  /* color: #0c7b91 !important;
  background-color: #fff !important; */
}

.tag.is-error {
  color: #25465f !important;
  background-color: #fff !important;
}

.tag.is-success {
  color: #25465f !important;
  background-color: #fff !important;
}

.tag.is-secondary {
  color: #25465f !important;
  background-color: #fff !important;

}

.is-inverted {
  border: 1px solid !important;

  /* opacity:80%; */
  /* border-color: #0c7b91 !important; */
}
  /* Mutualisation des blocs similaires */
  .box,
  .description-container,
  .generations-container,
  .bouchons-container,
  .requirement-row,
  .description-exigence {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
  }
  

  .box,
  .description-container,
  .description-exigence {
    padding: 20px;
  }

  .generations-container,
  .bouchons-container,
  .requirements-container {
    padding: 10px;
  }

  /* Utilitaires pour flex et alignement */
  .flex-align-center {
    display: flex;
    align-items: center;
  }

  /* Couleurs utilitaires */
  .color-white {
    color: #fff;
  }

  .color-25465f {
    color: #25465f;
  }

  /* Marges et paddings utilitaires */
  .mt-0 {
    margin-top: 0;
  }

  .ml-10 {
    margin-left: 10px;
  }

  .mr-8 {
    margin-right: 8px;
  }

  /* Largeurs et hauteurs utilitaires */
  .max-h-300 {
    max-height: 300px;
    overflow-y: auto;
    margin: 16px 0;
  }

  /* Spécifiques requirements */
  .requirement-row {
    background-color: #eff2f8;
    color: inherit;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 10px;
  }

  .requirement-row.active {
    background-color: #25465F;
    color: #fff;
  }

  .requirement-row-content {
    cursor: pointer;
    flex-grow: 1;
  }

  /* Bannière et titres */
  .banner,
  .requirement-banner {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
  }

  .text-left {
    text-align: left;
  }

  .mb-8 {
    margin-bottom: 8px;
  }

  .p-4-0 {
    padding: 4px 0;
  }