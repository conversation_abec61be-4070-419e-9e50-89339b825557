
import { Generation,Bouchon } from 'services/interfaces/generationInterfaces';
import { Requirement } from 'services/interfaces/requirementInterfaces';
import { CodeBlock,github,a11yDark} from 'react-code-blocks';
import wiremocklogo from '../layout/wiremock-logo.svg';
import { Button,Box,BoxContent,Title ,Icon,Text ,Tag,TagList,Accordion,AccordionItem,AccordionHeader,AccordionBody } from '@bytel/trilogy-react-ts';
interface BouchonsContainerProps {
  bouchons: Bouchon[]; 
  activeRequirement: number | null;

}

const BouchonsContainer: React.FC<BouchonsContainerProps> = ({ 
  bouchons, 
  activeRequirement, 

}) => {
  
  const downloadImplementation = (implementation) => {
    if (!implementation.contenu) return;
    
    const content = JSON.stringify(JSON.parse(implementation.contenu), null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `implementation-${implementation.id}.json`;
    document.body.appendChild(link);
    link.click();
    
    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <>
      {activeRequirement !== null && bouchons.length > 0 && (
        <div className='bouchons-container'>
          {bouchons.map((bouchon) => (
            <div key={bouchon.id} className="bouchon-item">              
              <div>
                <Box>
                  <BoxContent>
                    <Title >
                        {/* Bouchon download :   */}
                        <Button 
                          className="button is-secondary" 
                          style={{ backgroundColor: "rgb(228, 230, 232)" }}
                          onClick={() => {
                            // Check if implementations exist and download the first one
                            if (bouchon.implementations && bouchon.implementations.length > 0) {
                              downloadImplementation(bouchon.implementations[0]);
                            }
                          }}
                        >
                            <span><img src={wiremocklogo} alt="Wiremock Logo"/></span>
                            <span className="icon">
                                 <Icon name="tri-download" size="small" color='blue' style={{ marginLeft: 40 }}/>
                               
                            </span>
                        </Button>
                    </Title>                  
                    
                <Accordion id="accordion-1">
                    {bouchon.usecases.map((uc) => (      
                        <AccordionItem id={uc.id}>
                            <AccordionHeader>
                                <TagList>
                                    <Tag>{uc.http_method}</Tag>
                                    <Tag>{uc.status_code}</Tag>
                                    <Tag>{uc.service_path}</Tag>
                                    <Tag>{uc.description}</Tag>
                                </TagList>                                
                            </AccordionHeader>
                            <AccordionBody data-id={`body-${uc.id}`}>
                                <div class="columns">
                                    <div class="column">
                                        <Text>Appel</Text>
                                        <CodeBlock codeContainerStyle={{ backgroundColor: a11yDark.backgroundColor }} text={uc.appel}  theme={a11yDark} language="javascript" wrapLongLines={true} showLineNumbers={false} />
                                   </div>
                                    <div class="column">
                                       <Text>Reponse</Text>
                                       <CodeBlock codeContainerStyle={{ backgroundColor: a11yDark.backgroundColor }} text={uc.reponse} theme={a11yDark} language="javascript" wrapLongLines={true}  showLineNumbers={false} />
                                    </div>
                                </div>
                            </AccordionBody>
                        </AccordionItem>
                    ))}
                </Accordion>
                    
                </BoxContent>
                </Box>
              </div>
              
            </div>
          ))}
        </div>
      )}

      
    </>
  );
};

export default BouchonsContainer;
