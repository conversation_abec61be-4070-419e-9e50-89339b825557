import uuid
from datetime import datetime

from pydantic import BaseModel
from sqlmodel import Field, SQLModel

# Objet métier


class WorkspaceBase(BaseModel):
    """
    le modèle de données pour les utilisateurs
    """

    nom: str = Field(max_length=120)
    description: str | None = Field(default=None, max_length=1000)
    # public: bool = Field(default=False)


# ce qui est persisté (Data Access Object)


class WorkspaceStorage(WorkspaceBase, SQLModel, table=True):
    """
    le modèle de données de la table utilisateurs
    """

    __tablename__ = "workspaces"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    date_creation: datetime
    owner_id: uuid.UUID = Field(foreign_key="utilisateurs.id")


# ce qui est exposé vers l'extérieur (Data Transfert Objects)


class WorkspaceCreateInput(WorkspaceBase):
    """
    modèle de données pour la création d'un workspace

    Note: owner_id est automatiquement rempli par l'API avec l'ID de l'utilisateur authentifié
    """

    owner_id: uuid.UUID


class WorkspaceUpdateInput(WorkspaceBase):
    """
    modèle de données pour mettre à jour les infos
    """

    # le jour où on voudra permettre de modifier le proprio d'un workspace
    # id: uuid.UUID
    pass


class WorkspaceOutput(WorkspaceBase):
    """
    comment le workspace est vu depuis l'API
    """

    id: uuid.UUID
    date_creation: datetime


class WorkspaceStatusOutput(BaseModel):
    """
    Modèle de réponse pour le statut d'un workspace incluant les informations de la dernière demande d'identification
    """

    is_busy: bool = Field(description="Indique si le workspace est actuellement occupé par une identification en cours")
    last_identification_request: dict | None = Field(None, description="Informations sur la dernière demande d'identification")
