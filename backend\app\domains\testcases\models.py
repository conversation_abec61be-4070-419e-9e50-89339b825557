import uuid
from datetime import datetime
from enum import Enum

from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel

from app.domains.agents.models import FormatTest, TestImplementationIA

# objets métiers


class StatutTestIA(Enum):
    """
    les statuts du cycle de vie d'un test généré via IA
    """

    CREATED = "créé"
    VALIDATED = "validé"
    REJECTED = "rejeté"


class TestImplementation(TestImplementationIA):
    """
    une implémentation d'un test qui a été générée par IA.
    """

    date_generation: datetime


class TestCase(BaseModel):
    """
    le modèle de données pour un test identifié par IA
    tout les typologies plus spécifiques (ex: TestOpenApi) respectent ce modèle
    """

    titre: str
    description: str
    exigence_id: uuid.UUID
    statut: StatutTestIA
    date_generation: datetime = Field(description="date à laquelle le cas de test a été généré via IA")
    implementations: list[TestImplementation] | None = None


# Storage
# Note: pas d'héritage des objets pour éviter des erreurs de validations des schema pydantic sur les listes d'objets


class TestCaseStorage(SQLModel, table=True):
    """
    le modèle de données de la table contenant les cas de tests générés par IA
    """

    __tablename__ = "testcases"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    exigence_id: uuid.UUID = Field(index=True, foreign_key="exigences.id")
    validateur_id: uuid.UUID | None = Field(
        default=None, foreign_key="utilisateurs.id", description="l'utilisateur qui a validé/rejeté le cas de test"
    )
    date_maj_statut: datetime | None = Field(default=None, description="date à laquelle le test a été validé/rejeté")
    date_creation: datetime = Field(default_factory=datetime.now)
    date_generation: datetime = Field(description="date à laquelle le cas de test a été généré via IA")
    statut: StatutTestIA
    titre: str
    description: str

    implementations: list["TestImplementationStorage"] | None = Relationship(back_populates="testcase")


class TestImplementationStorage(SQLModel, table=True):
    """
    le modèle de données de la table contenant les implémentations d'un cas de test
    """

    __tablename__ = "implementations"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    testcase_id: uuid.UUID = Field(index=True, foreign_key="testcases.id")
    date_creation: datetime = Field(default_factory=datetime.now)
    date_generation: datetime
    format: FormatTest
    contenu: str

    testcase: TestCaseStorage | None = Relationship(back_populates="implementations")


# DTO


# Test Implementations


class TestImplementationInput(TestImplementation):
    """
    DTO pour l'ajout/création d'implémentations liées à un testcase
    """

    testcase_id: uuid.UUID


class TestImplementationUpdate(BaseModel):
    """
    DTO pour la modification d'une implementation existante
    """

    id: uuid.UUID
    contenu: str
    date_generation: datetime = Field(default_factory=datetime.now)


class TestImplementationFilter(BaseModel):
    """
    DTO pour la recherche d'implémentations
    """

    format: FormatTest | None = None
    testcase_id: uuid.UUID | None = None


# TestCases


class TestCaseInput(TestCase):
    """
    DTO pour la création d'un testcase généré par IA.
    """

    statut: StatutTestIA = StatutTestIA.CREATED


class TestCaseUpdate(BaseModel):
    """
    DTO pour la mise à jour des testcase (principalement le statut à date)
    """

    id: uuid.UUID
    validateur_id: uuid.UUID
    statut: StatutTestIA | None = None
    date_maj_statut: datetime = Field(default_factory=datetime.now)


class TestCaseOutput(TestCase):
    """
    comment un testcase est vu depuis l'API. à date, on renvoie le modèle complet
    """

    id: uuid.UUID
    date_maj_statut: datetime | None
    date_creation: datetime


class TestCaseFilter(BaseModel):
    """
    filtre de recherche autorisé
    """

    exigences_ids: list[uuid.UUID] | None = None
    statut: StatutTestIA | None = Field(None, description="permet de chercher par statut si indiqué")
    debut: datetime | None = Field(None, description="permet de chercher des demande à partir de cette date si indiquée")
    fin: datetime | None = Field(None, description="permet de chercher des demande jusqu'à cette date si indiquée")
