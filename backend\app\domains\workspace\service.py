import datetime
import logging
import uuid

from app.common.exceptions import InsufficientPermissionsException, NotFoundException
from app.domains.identification.models import DemandeIdentification<PERSON><PERSON>re, StatutsDemande
from app.domains.identification.repository import IdentificationsRepositoryInterface
from app.domains.users.models import UserOutput
from app.domains.users.repository import UsersRepositoryInterface

from .models import WorkspaceCreateInput, WorkspaceOutput, WorkspaceStatusOutput, WorkspaceStorage

# from app.common.exceptions import AlreadyExistsException, InsufficientPermissionsException, NotFoundException
from .repository import WorkspaceRepositoryInterface


class WorkspaceNotFoundException(NotFoundException):
    """Exception levée lorsqu'une exigence n'est pas trouvée."""

    message = "Le Workspace n'existe pas"


class WorkspaceService:
    """
    service de gestion des workspaces
    """

    def __init__(
        self, repository: WorkspaceRepositoryInterface, identifications: IdentificationsRepositoryInterface, users: UsersRepositoryInterface
    ):
        self._repo = repository
        self._repo_identifications = identifications
        self._repo_users = users
        self.log = logging.getLogger(__name__)

    def create_workspace(self, dto: WorkspaceCreateInput) -> WorkspaceOutput:
        dao = WorkspaceStorage(**dto.model_dump(), date_creation=datetime.datetime.now())
        self.log.debug("enregistrement du workspace %s", dao)
        created = self._repo.add(dao)
        return WorkspaceOutput(**created.model_dump())

    def get_user_workspaces(self, owner_id: uuid.UUID) -> list[WorkspaceOutput]:
        found_workspaces = self._repo.find(owner_id)
        self.log.debug("Retrieved workspaces for owner %s", owner_id)
        return [WorkspaceOutput(**workspace.model_dump()) for workspace in found_workspaces]

    def get_workspaces(self, limit: int = None):
        workspaces = self._repo.list(limit)
        self.log.info("Retrieved workspaces with limit %s", limit)
        return [WorkspaceOutput(**workspace.model_dump()) for workspace in workspaces]

    def get_workspace(
        self, id: uuid.UUID, asking_user: UserOutput
    ) -> WorkspaceOutput | WorkspaceNotFoundException | InsufficientPermissionsException:
        workspace = self._repo.get(id)

        if not workspace:
            raise WorkspaceNotFoundException(identifier=str(id))

        if not asking_user.admin and asking_user.id != workspace.id:
            # TODO: à mettre en place par la suite
            print(InsufficientPermissionsException(message="vos droits sont insuffisant pour consulter cet espace de travail"))

        return WorkspaceOutput(**workspace.model_dump())

    def is_workspace_busy(self, id: uuid.UUID) -> bool:
        workspace = self._repo.get(id)
        if not workspace:
            raise WorkspaceNotFoundException(identifier=str(id))
        demandes_en_cours = self._repo_identifications.find(
            DemandeIdentificationFiltre(workspace_id=workspace.id, statuts=[StatutsDemande.EN_COURS, StatutsDemande.CREE])
        )
        if demandes_en_cours:
            return True
        return False

    def get_workspace_status(self, id: uuid.UUID) -> WorkspaceStatusOutput:
        """
        Récupère le statut complet du workspace incluant les informations de la dernière demande d'identification
        """
        workspace = self._repo.get(id)
        if not workspace:
            raise WorkspaceNotFoundException(identifier=str(id))

        # Récupérer toutes les demandes d'identification pour ce workspace, triées par date de création (plus récente en premier)
        all_demandes = self._repo_identifications.find(DemandeIdentificationFiltre(workspace_id=workspace.id, ordre="desc"))

        # Vérifier s'il y a des demandes en cours
        demandes_en_cours = self._repo_identifications.find(
            DemandeIdentificationFiltre(workspace_id=workspace.id, statuts=[StatutsDemande.EN_COURS, StatutsDemande.CREE])
        )
        is_busy = len(demandes_en_cours) > 0

        # Récupérer la dernière demande d'identification (la plus récente)
        last_identification_request = None
        if all_demandes:
            latest_demande = all_demandes[0]  # La première car triée par date desc

            # Récupérer les informations du demandeur
            demandeur_login = "Utilisateur inconnu"
            try:
                demandeur = self._repo_users.get(latest_demande.demandeur_id)
                if demandeur:
                    demandeur_login = demandeur.login
            except Exception as e:
                self.log.warning(f"Impossible de récupérer les informations du demandeur {latest_demande.demandeur_id}: {e}")

            last_identification_request = {
                "demandeur_login": demandeur_login,
                "date_creation": latest_demande.date_creation.isoformat(),
                "date_debut": latest_demande.date_debut.isoformat() if latest_demande.date_debut else None,
                "date_fin": latest_demande.date_fin.isoformat() if latest_demande.date_fin else None,
                "statut": latest_demande.statut.value,
            }

        return WorkspaceStatusOutput(is_busy=is_busy, last_identification_request=last_identification_request)
