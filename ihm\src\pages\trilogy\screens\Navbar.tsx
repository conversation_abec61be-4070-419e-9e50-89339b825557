import React from 'react'
import {
  Section,
  Title,
  TitleLevels,
  Divider,
  Navbar,
  NavbarMenu,
  NavbarStart,
  NavbarLinks,
  NavbarItem,
  NavbarLink,
  NavbarDropdown,
  NavbarDropdownSection,
  NavbarBrand,
  NavbarItemMarkup,
  NavbarExtras,
  InputType,
  NavbarEnd,
  TextMarkup,
  NavbarDivider,
  NavbarAccordionItem,
  Columns,
  ColumnsItem,
  Text,
  Icon,
  IconName,
  IconSize,
  TextIconMarkup,
  IconPosition,
  Input,
} from '@bytel/trilogy-react-ts'

export const NavbarScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={TitleLevels.THREE}>Header (new)</Title>
      <Divider />

      <Navbar newNavbar>
        <NavbarMenu>
          <NavbarStart>
            <NavbarLinks>
              <NavbarItem megaDropdown>
                <NavbarLink href={'#'}>Téléphones / Accessoires</NavbarLink>
                <NavbarDropdown>
                  <NavbarDropdownSection>
                    <Icon
                      className='navbar-category-title'
                      content='Téléphones'
                      name={IconName.MOBILE}
                      size={IconSize.SMALL}
                    />
                    <NavbarLink href={'#'}>Iphone</NavbarLink>
                    <NavbarLink href={'#'}>Samsung</NavbarLink>
                    <NavbarLink href={'#'}>Huawei</NavbarLink>
                    <Icon
                      className='navbar-category-title'
                      content='Bons plans'
                      name={IconName.MOBILE}
                      size={IconSize.SMALL}
                    />
                    <NavbarLink href={'#'} highlighted>
                      Tous les Bons Plans
                    </NavbarLink>
                  </NavbarDropdownSection>
                  <NavbarDropdownSection>
                    <Icon
                      className='navbar-category-title'
                      content='Accessoires pour téléphone'
                      name={IconName.MOBILE}
                      size={IconSize.SMALL}
                    />
                    <NavbarLink href={'#'}>Étuis et protections</NavbarLink>
                    <NavbarLink href={'#'}>Clé 4G et tablette 4G</NavbarLink>
                    <NavbarLink href={'#'} highlighted>
                      Tous les accessoires pour téléphone
                    </NavbarLink>
                  </NavbarDropdownSection>
                  <NavbarDropdownSection>
                    <img alt='Bouygues Telecom' src='https://fakeimg.pl/350x200/?text=Contenu+libre' />
                  </NavbarDropdownSection>
                </NavbarDropdown>
              </NavbarItem>
              <NavbarItem>
                <NavbarLink href={'#'}>Forfaits mobile</NavbarLink>
              </NavbarItem>
              <NavbarItem>
                <NavbarLink href={'#'}>Box internet</NavbarLink>
              </NavbarItem>
              <NavbarItem>
                <NavbarLink href={'#'}>Forfaits mobile + Box</NavbarLink>
              </NavbarItem>
              <NavbarItem>
                <Icon
                  markup={TextIconMarkup.SPAN}
                  className='navbar-category-title navbar-link has-text-primary'
                  content='Promos'
                  name={IconName.V_SIGN}
                  size={IconSize.SMALL}
                  position={IconPosition.RIGHT}
                />
              </NavbarItem>
            </NavbarLinks>
          </NavbarStart>
        </NavbarMenu>
      </Navbar>

      <div style={{ marginBottom: '100px' }} />

      <Title level={TitleLevels.THREE}>Complete header (new)</Title>
      <Divider />

      <div>
        <Navbar newNavbar>
          <NavbarBrand>
            <NavbarItem markup={NavbarItemMarkup.A} hiddenTablet>
              <img alt='Bouygues Telecom' src='https://design.bouyguestelecom.fr/logo-bouygues-telecom.79081152.svg' />
            </NavbarItem>
            <NavbarItem markup={NavbarItemMarkup.A} hiddenMobile>
              <img alt='Bouygues Telecom' src='https://design.bouyguestelecom.fr/logo-bouygues-telecom.79081152.svg' />
            </NavbarItem>
          </NavbarBrand>
          <NavbarMenu hiddenTouch>
            <NavbarStart>
              <NavbarLinks>
                <NavbarItem megaDropdown>
                  <NavbarLink href={'#'}>Téléphones / Accessoires</NavbarLink>
                  <NavbarDropdown>
                    <NavbarDropdownSection>
                      <Icon
                        className='navbar-category-title'
                        content='Téléphones'
                        name={IconName.MOBILE}
                        size={IconSize.SMALL}
                      />
                      <NavbarLink to='/iphone'>Iphone</NavbarLink>
                      <NavbarLink onClick={() => console.log('Navbar link click alert')}>Samsung</NavbarLink>
                      <NavbarLink href='Huawei'>Huawei</NavbarLink>
                      <NavbarLink href={'#'}>Samsung</NavbarLink>
                      <NavbarLink href={'#'}>Huawei</NavbarLink>
                      <Icon
                        className='navbar-category-title'
                        content='Bons plans'
                        name={IconName.MOBILE}
                        size={IconSize.SMALL}
                      />
                      <NavbarLink href={'#'} highlighted>
                        Tous les Bons Plans
                      </NavbarLink>
                    </NavbarDropdownSection>
                    <NavbarDropdownSection>
                      <Icon
                        className='navbar-category-title'
                        content='Accessoires pour téléphone'
                        name={IconName.MOBILE}
                        size={IconSize.SMALL}
                      />
                      <NavbarLink href={'#'}>Étuis et protections</NavbarLink>
                      <NavbarLink href={'#'}>Clé 4G et tablette 4G</NavbarLink>
                      <NavbarLink href={'#'} highlighted>
                        Tous les accessoires pour téléphone
                      </NavbarLink>
                    </NavbarDropdownSection>
                    <NavbarDropdownSection>
                      <img alt='Bouygues Telecom' src='https://fakeimg.pl/350x200/?text=Contenu+libre' />
                    </NavbarDropdownSection>
                  </NavbarDropdown>
                </NavbarItem>
                <NavbarItem>
                  <NavbarLink href={'#'}>Forfaits mobile</NavbarLink>
                </NavbarItem>
                <NavbarItem>
                  <NavbarLink href={'#'}>Box internet</NavbarLink>
                </NavbarItem>
                <NavbarItem>
                  <NavbarLink href={'#'}>Forfaits mobile + Box</NavbarLink>
                </NavbarItem>
                <NavbarItem>
                  <Icon
                    markup={TextIconMarkup.SPAN}
                    className='navbar-category-title navbar-link has-text-primary'
                    content='Promos'
                    name={IconName.V_SIGN}
                    size={IconSize.SMALL}
                    position={IconPosition.RIGHT}
                  />
                </NavbarItem>
              </NavbarLinks>
            </NavbarStart>
          </NavbarMenu>
          <NavbarMenu>
            <NavbarExtras hiddenTouch>
              <Input hasIcon type={InputType.TEXT} placeholder='Forfait, téléphones, fibre…' />
            </NavbarExtras>
            <NavbarEnd>
              <NavbarItem>
                <NavbarLink href={'https://google.fr'}>
                  <Icon
                    markup={TextIconMarkup.SPAN}
                    textClassName='is-hidden-mobile'
                    content='Assistance'
                    name={IconName.HANDS_HELPING}
                    size={IconSize.SMALL}
                    position={IconPosition.LEFT}
                  />
                </NavbarLink>
              </NavbarItem>
              <NavbarItem>
                <NavbarLink href={'https://google.fr'}>
                  <Icon
                    markup={TextIconMarkup.SPAN}
                    textClassName='is-hidden-mobile'
                    content='Nos boutiques'
                    name={IconName.STORE}
                    size={IconSize.SMALL}
                    position={IconPosition.LEFT}
                  />
                </NavbarLink>
              </NavbarItem>
              <NavbarItem>
                <NavbarLink href={'https://google.fr'}>
                  <Icon
                    badgeContent='2'
                    stacked
                    textClassName='is-hidden-mobile'
                    content='Panier'
                    name={IconName.SHOPPING_CART}
                    position={IconPosition.LEFT}
                  />
                </NavbarLink>
              </NavbarItem>
              <NavbarItem alternate>
                <NavbarLink href='https://google.fr'>
                  <Icon name={IconName.USER} />
                  <Text className='is-hidden-mobile' markup={TextMarkup.SPAN}>
                    Se connecter
                  </Text>
                </NavbarLink>
              </NavbarItem>

              <NavbarItem alternate hiddenDesktop>
                <Icon name={IconName.MENU} />
                <NavbarDropdown>
                  <NavbarDropdownSection extras>
                    <Input hasIcon search type={InputType.TEXT} placeholder='Forfait, téléphones, fibre…' />
                  </NavbarDropdownSection>
                  <NavbarDivider />
                  <NavbarDropdownSection className='navbar-extras'>
                    <NavbarAccordionItem headerContent='Téléphones et accessoires'>
                      <NavbarLink>Item</NavbarLink>
                      <NavbarLink>Item</NavbarLink>
                    </NavbarAccordionItem>
                    <NavbarAccordionItem headerContent='Forfaits mobiles'>
                      <NavbarLink>Item</NavbarLink>
                      <NavbarLink>Item</NavbarLink>
                    </NavbarAccordionItem>
                    <NavbarAccordionItem headerContent='Box internet'>
                      <NavbarLink>Item</NavbarLink>
                      <NavbarLink>Item</NavbarLink>
                    </NavbarAccordionItem>
                    <NavbarAccordionItem headerContent='Forfait mobile + Box'>
                      <NavbarLink>Item</NavbarLink>
                      <NavbarLink>Item</NavbarLink>
                    </NavbarAccordionItem>
                    <NavbarAccordionItem headerContent='Promos'>
                      <NavbarLink>Item</NavbarLink>
                      <NavbarLink>Item</NavbarLink>
                    </NavbarAccordionItem>
                  </NavbarDropdownSection>
                  <NavbarDropdownSection>
                    <NavbarLink className='has-ending-arrow'>Accéder à votre espace client</NavbarLink>
                    <NavbarLink className='has-ending-arrow'>Suivre votre commande</NavbarLink>
                    <NavbarLink className='has-ending-arrow'>Activer votre ligne</NavbarLink>
                  </NavbarDropdownSection>
                  <NavbarDivider />
                  <NavbarDropdownSection>
                    <NavbarItem>
                      <NavbarLink href='#'>
                        <Icon name={IconName.HANDS_HELPING} size={IconSize.SMALL} content='Assistance' />
                      </NavbarLink>
                    </NavbarItem>
                    <NavbarItem>
                      <NavbarLink href='#'>
                        <Icon name={IconName.STORE} size={IconSize.SMALL} content='Nos boutique' />
                      </NavbarLink>
                    </NavbarItem>
                    <NavbarItem>
                      <NavbarLink href='#'>
                        <Icon name={IconName.LOGO} size={IconSize.SMALL} content='La marque' />
                      </NavbarLink>
                    </NavbarItem>
                    <NavbarItem>
                      <NavbarLink href='#'>
                        <Icon name={IconName.USER_TIE} size={IconSize.SMALL} content='Professionel' />
                      </NavbarLink>
                    </NavbarItem>
                  </NavbarDropdownSection>
                  <NavbarDropdownSection>
                    <Columns mobile>
                      <ColumnsItem>
                        <a href='/' className='is-flex is-positionned-center'>
                          <img
                            alt='Bouygues Telecom'
                            src='https://design.bouyguestelecom.fr/store-apple.5ab39ea3.svg'
                            className='is-block'
                          />
                        </a>
                      </ColumnsItem>
                      <ColumnsItem>
                        <a href='/' className='is-flex is-positionned-center'>
                          <img
                            alt='Bouygues Telecom'
                            src='https://design.bouyguestelecom.fr/store-google.63dc5064.svg'
                            className='is-block'
                          />
                        </a>
                      </ColumnsItem>
                    </Columns>
                  </NavbarDropdownSection>
                </NavbarDropdown>
              </NavbarItem>
            </NavbarEnd>
          </NavbarMenu>
        </Navbar>
      </div>
    </Section>
  )
}
