import { BQuery } from "@bytel/query";
import { Service } from "./commonService";

export interface FeedbackInput {
  rating: number;
  comment: string;
  exigence_id: string;
  workspace_id: string;
  generation_id: string;
  type: string;
}

export interface FeedbackOutput extends FeedbackInput {
  id: string;
  created_at: string;
  user_id: string;
}

export class FeedbackService extends Service {

    constructor(bquery: BQuery) {
        super(bquery);
    }

    async submitFeedback(feedback: FeedbackInput): Promise<FeedbackOutput> {
        return this.bquery.post<FeedbackOutput>(`/feedbacks`, feedback);
    }

    async listFeedbacks(): Promise<FeedbackOutput[]> {
        return this.bquery.get<FeedbackOutput[]>(`/feedbacks`);
    }
}