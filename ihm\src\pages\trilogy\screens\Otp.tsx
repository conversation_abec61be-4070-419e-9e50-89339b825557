import React from 'react'
import { Section, Title, TitleLevels, Otp, Spacer, SpacerSize } from '@bytel/trilogy-react-ts'

export const OtpScreen = (): JSX.Element => {
  return (
    <Section>
      <Title level={TitleLevels.THREE}>OTP</Title>
      <Otp
        onChange={(code) => console.log('onChange', code)}
        onCompleted={(code) => console.log('onCompleted', code)} label='Saisir votre otp'
        autoFocus
      />
      <Spacer size={SpacerSize.HUGE} />

      <Otp
        disabled
        onChange={(code) => console.log('onChange', code)}
        onCompleted={(code) => console.log('onCompleted', code)}
        label='OTP Disabled'
      />
      <Spacer size={SpacerSize.HUGE} />
      <Otp
        error
        errorMessage={'Ceci est un message  d\'erreur'}
        onChange={(code) => console.log('onChange', code)}
        onCompleted={(code) => console.log('onCompleted', code)}
        label='OTP Error'
      />
      <Spacer size={SpacerSize.HUGE} />

      <Otp
        errorMessage={'Ceci est un message'}
        codeSize={4}
        onChange={(code) => console.log('onChange', code)}
        onCompleted={(code) => console.log('onCompleted', code)}
        label='OTP Error'
      />
    </Section>
  )
}
