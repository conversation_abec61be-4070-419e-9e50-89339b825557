import { Service } from "./commonService";
import { Individu } from "services/interfaces/generated/HELLOWORLD-FORMATION/consulterPersonne.ts";
import { Individu as IndividuRecherche } from "services/interfaces/generated/HELLOWORLD-FORMATION/rechercherPersonnes.ts";

export class PersonneService extends Service {
  async consulterPersonne(idUtilisateur: string): Promise<Individu> {
    // return this.bquery.get<Individu>(`/personnes/${idUtilisateur}`);
    return Promise.resolve(mockUtilisateurs[0]);
  }

  async rechercherPersonnes(value: string): Promise<IndividuRecherche> {
    return Promise.resolve(mockUtilisateurs.filter((u) => u.nom?.toLocaleLowerCase().includes(value.toLocaleLowerCase()) || u.prenom?.toLocaleLowerCase().includes(value.toLocaleLowerCase())))
    // return this.bquery.get(`/personnes`, { params: { nom: value } }).then((p) => p.personne as IndividuRecherche[]);
  }
}

const mockUtilisateurs: IndividuRecherche[] = [
  {
    _type: 'Individu',
    idPersonneUnique: '800000000526',
    nom: 'DIEUDONNE',
    prenom: 'LYNDA',
    civilite: 'MME',
    dateNaissance: '01/01/1970',
    departementNaissance: '75',
  },
  {
    _type: 'Individu',
    idPersonneUnique: '800000000987',
    nom: 'KENT',
    prenom: 'CLARC',
    civilite: 'M',
    dateNaissance: '01/01/1970',
    departementNaissance: '75',
  }
]
