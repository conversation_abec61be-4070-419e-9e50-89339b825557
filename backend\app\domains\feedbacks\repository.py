from abc import ABC, abstractmethod
from datetime import datetime

from sqlalchemy.orm import Session

from app.domains.feedbacks.models import Feedback, FeedbackInput


class FeedbackRepositoryInterface(ABC):
    @abstractmethod
    def add(self, db: Session, feedback: FeedbackInput) -> Feedback: ...

    @abstractmethod
    def list(self, db: Session, skip: int = 0, limit: int = 100): ...


class SqlFeedbackRepository(FeedbackRepositoryInterface):
    def add(self, db: Session, feedback: FeedbackInput) -> Feedback:
        db_feedback = Feedback(
            rating=feedback.rating,
            comment=feedback.comment,
            exigence_id=feedback.exigence_id,
            workspace_id=feedback.workspace_id,
            generation_id=feedback.generation_id,
            type=feedback.type,
            user_id=feedback.user_id,
            created_at=datetime.now(),
        )
        db.add(db_feedback)
        db.commit()
        db.refresh(db_feedback)
        return db_feedback

    def list(self, db: Session, skip: int = 0, limit: int = 100):
        return db.query(Feedback).offset(skip).limit(limit).all()
