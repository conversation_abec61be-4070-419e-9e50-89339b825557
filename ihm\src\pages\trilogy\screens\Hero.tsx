import React from 'react'
import { But<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Con<PERSON>er, Di<PERSON>r, Hero, Section, Text, Title, TitleLevels } from '@bytel/trilogy-react-ts'
import { VariantState } from '@bytel/trilogy-react-ts'

export const HeroScreen = (): JSX.Element => {
  return (
    <Section>
      <Hero backgroundSrc={'https://picsum.photos/id/1/1500/600'}>
        <Container>
          <Text>Message de bienvenue</Text>
          <Title level='ONE'>Bonjour Michel</Title>
          <Title level='TWO'>
            Fugiat velit dolor ad adipisicing id quis enim cupidatat Lorem dolore aute excepteur tempor.
          </Title>
          {/* <button className='button'>Click me !</button> */}
          <Button markup={ButtonMarkup.BUTTON} variant={'PRIMARY'} onClick={() => alert('Click on hero btn')}>
            Click me !
          </Button>
        </Container>
      </Hero>

      <Title level={TitleLevels.THREE}>Hero + Background Color</Title>
      <Divider />

      <Hero variant={VariantState.PRIMARY}>
        <Container>
          <Text>Message de bienvenue</Text>
          <Title level='ONE'>Bonjour Michel</Title>
          <Title level='TWO'>
            Fugiat velit dolor ad adipisicing id quis enim cupidatat Lorem dolore aute excepteur tempor.
          </Title>
          {/* <button className='button'>Click me !</button> */}
          <Button markup={ButtonMarkup.BUTTON} variant={'SECONDARY'} onClick={() => alert('Click on hero btn')}>
            Click me !
          </Button>
        </Container>
      </Hero>
    </Section>
  )
}
