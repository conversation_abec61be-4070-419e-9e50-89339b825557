import logging
import uuid

from fastapi import <PERSON><PERSON>outer, Depends, status
from pydantic import BaseModel

from app.api.security import get_current_user
from app.common.bootstrap import APPLICATION
from app.domains.feedbacks.models import FeedbackCreate, FeedbackInput, FeedbackOutput
from app.domains.users.models import UserOutput

router = APIRouter(tags=["feedbacks"])
logger = logging.getLogger(__name__)


class FeedbackFiltre(BaseModel):
    exigence_id: str | None = None
    workspace_id: str | None = None
    user_id: uuid.UUID | None = None


@router.post(
    "/feedbacks",
    response_model=FeedbackOutput,
    status_code=status.HTTP_201_CREATED,
)
async def create_feedback(feedback: FeedbackInput, current_user: UserOutput = Depends(get_current_user)) -> FeedbackOutput:
    logger.info(f"Received request to create feedback from user={current_user.id}")
    try:
        feedback_with_user = FeedbackCreate(**feedback.model_dump(), user_id=current_user.id)
        print(feedback_with_user)
        result = APPLICATION.services.feedbacks.submit_feedback(feedback_with_user)
        logger.info(f"Successfully created feedback with id={result.id}")
        return result
    except Exception as e:
        logger.error(f"Error creating feedback: {str(e)}", exc_info=True)
        raise


@router.get("/feedbacks", response_model=list[FeedbackOutput])
async def list_feedbacks(skip: int = 0, limit: int = 100, current_user: UserOutput = Depends(get_current_user)) -> list[FeedbackOutput]:
    return APPLICATION.services.feedbacks.list_feedbacks(skip, limit)


@router.get("/feedbacks/{feedback_id}", response_model=FeedbackOutput)
async def get_feedback(feedback_id: uuid.UUID, current_user: UserOutput = Depends(get_current_user)) -> FeedbackOutput:
    return APPLICATION.services.feedbacks.get_feedback(feedback_id)
