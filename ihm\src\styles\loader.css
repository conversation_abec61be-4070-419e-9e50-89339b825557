.custom-loader {
  width: 48px;
  height: 48px;
  border: 5px solid #0c7b91;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

.custom-loader.small {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

.custom-loader.large {
  width: 64px;
  height: 64px;
  border-width: 6px;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}