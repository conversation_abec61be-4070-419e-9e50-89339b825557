import React from 'react'
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Divider,
  Input,
  Link,
  Price,
  PriceLevel,
  PriceVariant,
  PricingPlan,
  PricingPlanExtra,
  PricingPlanFooter,
  PricingPlanHeader,
  PricingPlanItem,
  PricingPlanItems,
  PricingPlanPrice,
  PricingPlanProduct,
  PricingTable,
  PricingTableExtra,
  Section,
  Sticker,
  StickerMarkup,
  Text,
  TextLevels,
  Title,
  TitleLevels,
} from '@bytel/trilogy-react-ts'
import { AlertState, TrilogyColor, TypographyColor } from '@bytel/trilogy-react-ts'

export interface MobileOfferProposedItemProps {
  isMain?: boolean
  isPrimary?: boolean
  onSelect: () => void
  onSeeDetails: () => void
}

export const PricingTableScreen = (): JSX.Element => {
  return (
    <Section>
      <Section>
        <Title level={TitleLevels.THREE}>Tableau offres</Title>
        <Divider />
      </Section>
      <Section>
        <PricingTable>
          <PricingTableExtra>
            <Card horizontal>
              <CardContent>
                <Text>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</Text>
              </CardContent>
            </Card>
          </PricingTableExtra>
          <PricingPlan>
            <PricingPlanExtra>
              <Card horizontal>
                <CardContent>
                  <Text>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</Text>
                </CardContent>
              </Card>
            </PricingPlanExtra>
            <PricingPlanProduct hat>
              <Sticker hat alert={AlertState.INFO}>
                Test
              </Sticker>
              <PricingPlanHeader background={TrilogyColor.WHITE}>
                <Title>50Mo</Title>
                <Text>Engagement 24 mois</Text>
              </PricingPlanHeader>
              <PricingPlanItems background={TrilogyColor.WHITE}>
                <PricingPlanPrice>
                  <Price
                    variant={PriceVariant.SECONDARY}
                    level={PriceLevel.LEVEL3}
                    amount={24.99}
                    mention='(3)'
                    period='mois'
                    showCents
                  />
                </PricingPlanPrice>
                <PricingPlanItem spacing={3}>
                  <Sticker markup={StickerMarkup.P} small alert={AlertState.SUCCESS}>
                    Prix déjà client
                  </Sticker>
                  <Price level={PriceLevel.LEVEL3} amount={24.99} mention='(3)' period='mois' showCents />
                  <Text>12 mois, puis 1€</Text>
                </PricingPlanItem>
                <PricingPlanItem>Forfait exclusivement bloqué</PricingPlanItem>
                <Button variant={'PRIMARY'} fullwidth>
                  Ajouter au panier
                </Button>
              </PricingPlanItems>
              <PricingPlanItems>
                <PricingPlanItem spacing={2}>
                  <Text level={TextLevels.ONE}>Appels / SMS / MMS</Text>
                  <Text>2h appel, SMS / MMS illimités</Text>
                </PricingPlanItem>
                <PricingPlanItem>
                  <Sticker alert={AlertState.INFO} small>
                    Options
                  </Sticker>
                  <Text level={TextLevels.ONE}>50Mo</Text>
                  <Text>Option</Text>
                </PricingPlanItem>
                <PricingPlanFooter>
                  <Link>En savoir plus</Link>
                </PricingPlanFooter>
              </PricingPlanItems>
            </PricingPlanProduct>
          </PricingPlan>
          <PricingPlan>
            <PricingPlanExtra>
              <Box />
            </PricingPlanExtra>
            <PricingPlanProduct>
              <PricingPlanHeader>
                <Title typo={[TypographyColor.TEXT_WHITE]}>50Mo</Title>
                <Text typo={[TypographyColor.TEXT_WHITE]}>Engagement 24 mois</Text>
              </PricingPlanHeader>
              <PricingPlanItems background={TrilogyColor.WHITE}>
                <PricingPlanPrice>
                  <Price level={PriceLevel.LEVEL3} amount={24.99} mention='(3)' period='mois' showCents />
                </PricingPlanPrice>
                <PricingPlanItem spacing={3} />
                <PricingPlanItem spacing={3}>
                  <Input />
                </PricingPlanItem>
                <Button variant={'PRIMARY'} fullwidth>
                  Ajouter au panier
                </Button>
              </PricingPlanItems>
              <PricingPlanItems>
                <PricingPlanItem spacing={2}>
                  <Text level={TextLevels.ONE}>Appels / SMS / MMS</Text>
                  <Text>2h appel, SMS / MMS illimités</Text>
                </PricingPlanItem>
                <PricingPlanItem spacing={2}>
                  <Text level={TextLevels.ONE}>À étranger</Text>
                  <Text>Option</Text>
                </PricingPlanItem>
              </PricingPlanItems>
              <PricingPlanFooter>
                <Link>En savoir plus</Link>
              </PricingPlanFooter>
            </PricingPlanProduct>
          </PricingPlan>
          <PricingPlan>
            <PricingTableExtra>
              <Box />
            </PricingTableExtra>
            <PricingPlanProduct>
              <PricingPlanHeader>
                <Text level={TextLevels.ONE}>50Mo</Text>
                <Text>Engagement 24 mois</Text>
              </PricingPlanHeader>
              <PricingPlanItems background={TrilogyColor.WHITE}>
                <PricingPlanPrice>
                  <Price
                    variant={PriceVariant.SECONDARY}
                    level={PriceLevel.LEVEL3}
                    amount={24.99}
                    mention='(3)'
                    period='mois'
                    showCents
                  />
                </PricingPlanPrice>
                <PricingPlanItem spacing={3}>
                  <Sticker markup={StickerMarkup.P} small alert={AlertState.SUCCESS}>
                    Prix déjà client
                  </Sticker>
                  <Price level={PriceLevel.LEVEL3} amount={24.99} mention='(3)' period='mois' showCents />
                  <Text>12 mois, puis 1€</Text>
                </PricingPlanItem>
                <PricingPlanItem>Forfait exclusivement bloqué</PricingPlanItem>
                <Button variant={'PRIMARY'} fullwidth>
                  Ajouter au panier
                </Button>
              </PricingPlanItems>
              <PricingPlanItems>
                <PricingPlanItem narrow>
                  <Text level={TextLevels.ONE} className='plan-item-title'>
                    Appels / SMS / MMS
                  </Text>
                  <Text>2h appel, SMS / MMS illimités</Text>
                </PricingPlanItem>
                <PricingPlanItem spacing={2}>
                  <Text level={TextLevels.ONE} className='plan-item-title'>
                    Options
                  </Text>
                  <Text>Options</Text>
                </PricingPlanItem>
                <PricingPlanItem spacing={2}>
                  <Text level={TextLevels.ONE} className='plan-item-title'>
                    Options
                  </Text>
                  <Text>Options</Text>
                </PricingPlanItem>
                <PricingPlanFooter>
                  <Link> En savoir plus</Link>
                </PricingPlanFooter>
              </PricingPlanItems>
            </PricingPlanProduct>
          </PricingPlan>
        </PricingTable>
      </Section>
    </Section>
  )
}
