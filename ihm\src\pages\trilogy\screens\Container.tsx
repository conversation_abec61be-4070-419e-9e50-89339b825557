import React from 'react'
import { Box, Section, Title, TitleLevels, Divider, Container, Text, BoxContent } from '@bytel/trilogy-react-ts'

export const ContainerScreen = (): JSX.Element => {
  return (
    <>
      <Section verticalPaddingless>
        <Container>
          <Box>
            <BoxContent>
              <Text>Container</Text>
              <Title level={TitleLevels.THREE}>Conteneur simple</Title>
            </BoxContent>
          </Box>
        </Container>
        <Divider />
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>Conteneur fluid</Title>
        <Divider />
        <Container fluid>
          <Box>
            <BoxContent>
              <Text>Container content</Text>
            </BoxContent>
          </Box>
        </Container>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>Conteneur medium</Title>
        <Divider />
        <Container medium>
          <Box>
            <BoxContent>
              <Text>Fullwidth content</Text>
            </BoxContent>
          </Box>
        </Container>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>fullwidth</Title>
        <Divider />
        <Container fullwidth>
          <Box>
            <BoxContent>
              <Text>Fullwidth content</Text>
            </BoxContent>
          </Box>
        </Container>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>fluide centré</Title>
        <Divider />
        <Container fluid centered>
          <Box>
            <BoxContent>
              <Text>content centered</Text>
            </BoxContent>
          </Box>
        </Container>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>pulled-right</Title>
        <Divider />
        <Container fluid pulledRight>
          <Box>
            <BoxContent>
              <Text>pulledRight</Text>
            </BoxContent>
          </Box>
        </Container>
      </Section>
      <Section>
        <Title level={TitleLevels.THREE}>pulled-left</Title>
        <Divider />
        <Container fluid pulledLeft>
          <Box>
            <BoxContent>
              <Text>pulledLeft</Text>
            </BoxContent>
          </Box>
        </Container>
      </Section>
    </>
  )
}
