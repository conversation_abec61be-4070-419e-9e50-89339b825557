import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from .models import ExigenceFiltre, ExigenceStorage, ExigenceUpdateInput


class ExigenceRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dao: ExigenceStorage) -> ExigenceStorage: ...
    @abstractmethod
    def update(self, dto: ExigenceUpdateInput) -> ExigenceStorage: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> ExigenceStorage | None: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[ExigenceStorage]: ...
    @abstractmethod
    def find(self, filtre: ExigenceFiltre) -> Iterable[ExigenceStorage]: ...

    # @abstractmethod
    # def find_by_user(self, owner_id: uuid.UUID) -> Iterable[ExigenceStorage]: ...
    # @abstractmethod
    # def find_by_workspace(self, ws_id: uuid.UUID) -> Iterable[ExigenceStorage]: ...
