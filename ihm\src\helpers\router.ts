import * as React from "react";
import { matchPath, RouteObject, useLocation, useParams } from "react-router-dom";

export const useTypedParams = <T extends { [K in keyof T]?: string }>() => {
  return useParams<T>() as unknown as T;
};

export const RouteContext = React.createContext<RouteObjectWithMetadata[]>([]);

export const useRouteContext = () => React.useContext(RouteContext);

export function useRouteMetadata(): RouteObjectWithMetadata | undefined {
  const { pathname } = useLocation();
  const routes = useRouteContext();

  return React.useMemo(() => {
    if (!pathname) return;

    const matchingRoute: { data?: RouteObjectWithMetadata } = {
      data: undefined,
    };
    routes.forEach((route) => testMatchingRouteOrChildren(route, pathname, matchingRoute));
    return matchingRoute.data;
  }, [pathname, routes]);
}

export type RouteObjectWithMetadata = Omit<RouteObject, "children"> & {
  titre?: string;
  sousTitre?: string;
  hero?: boolean;
} & ({ index?: false; children?: RouteObjectWithMetadata[] } | { index: true; children?: undefined });

function testMatchingRouteOrChildren(route: RouteObjectWithMetadata, pathname: string, matchingRoute: { data?: RouteObjectWithMetadata }) {
  if (matchingRoute.data != null) return;
  const _route = { ...route, path: route.path || "/" };
  _route.path = _route.path.replace("/*", "");
  if (!_route.path?.endsWith("/")) {
    _route.path += "/";
  }
  if (matchPath(_route, pathname)) {
    matchingRoute.data = route;
    return;
  }
  route.children?.forEach((child) => testMatchingRouteOrChildren({ ...child, path: _route.path + child.path }, pathname, matchingRoute));
}
