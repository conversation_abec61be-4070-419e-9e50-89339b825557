import { screen, waitFor } from "@testing-library/react";
import { customRender } from "tests/custom-renderer";
import { mockAPI, mockSnackbar } from "tests/init-mocks";
import { DetailsPersonnePage } from "../detailsPersonne.tsx";
import { AxiosError } from "axios";

describe("affichage des données de la personne", () => {
  test("affichage basic", async () => {
    // GIVEN
    mockAPI.personneService.consulterPersonne.mockResolvedValue({
      type: "Individu",
      id: "800000000526",
      nom: "Bond",
      prenom: "<PERSON>",
      civilite: "M",
      dateNaissance: "01/01/1970",
      departementNaissance: "44",
    });

    // WHEN
    customRender(<DetailsPersonnePage />);

    // THEN
    await waitFor(() => expect(mockAPI.personneService.consulterPersonne).toHaveBeenCalledTimes(1));
    await waitFor(() => expect(screen.getByTestId("userDetailsTitle")).toBeInTheDocument());
  });
  test("Personne non existante", async () => {
    // GIVEN
    const axiosError = new AxiosError("Personne non existante", "NOT_FOUND");
    mockAPI.personneService.consulterPersonne.mockRejectedValue(axiosError);

    // WHEN
    customRender(<DetailsPersonnePage />);

    // THEN
    await waitFor(() => expect(mockAPI.personneService.consulterPersonne).toHaveBeenCalledTimes(1));
    await waitFor(() =>
      expect(mockSnackbar.showError).toHaveBeenCalledWith(axiosError, {
        message: "Erreur lors de la récupération de la personne",
      }),
    );
  });
});
