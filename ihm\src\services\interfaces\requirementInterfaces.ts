export interface Requirement {
    id: string;
    ws_id: string;
    owner_id: string;
    nom: string;
    description: string;
    type: string;
    data: string;
}


export interface ExtractedRequirement {
    nom: string;
    description: string;
    type: string;
    data: string;
    porteur: string;
    metadonnees: object;
    selected: boolean
}

export interface ExtractionError {
    id: string
    url: string
    message: string
}

export interface ExtractedDocUnique {
    nom: string;
    id: string;
    url: string;
    pages_extraites: number;
    exigences: ExtractedRequirement[]
    erreurs: ExtractionError[]
}
