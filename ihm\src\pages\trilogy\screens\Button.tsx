import {
  Button,
  ButtonColor,
  ButtonMarkup,
  Columns,
  ColumnsItem,
  IconName,
  Spacer,
  View,
  Divider,
  Title,
  TitleLevels,
  ButtonList,
  AutoLayout
} from "@bytel/trilogy-react-ts"
import React from 'react'
import { AlertState, Alignable, Justifiable } from '@bytel/trilogy-react-ts'

const Separator = () => {
  return (
    <>
      <Spacer size={10} />
      <Divider />
      <Spacer size={50} />
    </>
  )
}

export const ButtonScreen = (): JSX.Element => {
  return (
    <AutoLayout>
      {/*  ======== alert ======== */}
      <Title level={TitleLevels.TWO}>alert </Title>
      <Spacer size={10} />
        {Object.values(AlertState).map((alert, index) => {
          return (
            <ButtonList key={index}>
              <Button alert={alert} fullwidth>
                {alert}
              </Button>
            </ButtonList>
          )
        })}
      <Separator />

      {/*  ======== small & alert ======== */}
      <Title level={TitleLevels.TWO}>alert + small </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(AlertState).map((alert, index) => {
          return (
            <ColumnsItem key={index} size={4}>
              <Button alert={alert} fullwidth small>
                {alert}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== loading & alert ======== */}
      <Title level={TitleLevels.TWO}>alert + loading </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(AlertState).map((alert, index) => {
          return (
            <ColumnsItem size={4} key={index}>
              <Button alert={alert} fullwidth loading>
                {alert}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== disabled & alert ======== */}
      <Title level={TitleLevels.TWO}>disabled + alert </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(AlertState).map((alert, index) => {
          return (
            <ColumnsItem size={4} key={index}>
              <Button alert={alert} fullwidth disabled>
                {alert}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== variants ======== */}
      <Title level={TitleLevels.TWO}>variant </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(ButtonColor).map((color, index) => {
          return (
            <ColumnsItem key={index} size={4}>
              <Button variant={color} fullwidth>
                {color}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== variants & small ======== */}
      <Title level={TitleLevels.TWO}>variant & small </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(ButtonColor).map((color, index) => {
          return (
            <ColumnsItem key={index} size={4}>
              <Button variant={color} fullwidth small>
                {color}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== loading & variant ======== */}
      <Title level={TitleLevels.TWO}>loading + variant </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(ButtonColor).map((color, index) => {
          return (
            <ColumnsItem key={index} size={4}>
              <Button variant={color} fullwidth loading>
                {color}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== disabled & variant ======== */}
      <Title level={TitleLevels.TWO}>disabled + variant </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(ButtonColor).map((color, index) => {
          return (
            <ColumnsItem key={index} size={4}>
              <Button variant={color} fullwidth disabled>
                {color}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== fullwidth ======== */}
      <Title level={TitleLevels.TWO}>fullwidth </Title>
      <Spacer size={10} />
      <Button fullwidth variant={'PRIMARY'}>
        Cliquer
      </Button>
      <Separator />

      {/*  ======== inverted & alert ======== */}
      <Title level={TitleLevels.TWO}>alert + inverted </Title>
      <Spacer size={10} />
      <View {...{ style: { backgroundColor: '#25465f' } }}>
        <Columns inlined>
          {Object.values(AlertState).map((alert, index) => {
            return (
              <ColumnsItem size={4} key={index}>
                <Button alert={alert} inverted>
                  {alert}
                </Button>
              </ColumnsItem>
            )
          })}
        </Columns>
        <Spacer size={10} />
      </View>
      <Separator />

      {/*  ======== inverted & variant ======== */}
      <Title level={TitleLevels.TWO}>variant + inverted </Title>
      <Spacer size={10} />
      <View {...{ style: { backgroundColor: '#25465f' } }}>
        <Columns inlined>
          {Object.values(ButtonColor).map((alert, index) => {
            return (
              <ColumnsItem key={index} size={4}>
                <Button variant={alert} inverted>
                  {alert}
                </Button>
              </ColumnsItem>
            )
          })}
        </Columns>
        <Spacer size={10} />
      </View>
      <Separator />

      {/*  ======== justify ======== */}
      <Title level={TitleLevels.TWO}>justify </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(Justifiable).map((justify, index) => {
          return (
            <ColumnsItem key={index} size={12}>
              <Button variant='PRIMARY' justify={justify} fullwidth>
                {justify}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== align ======== */}
      <Title level={TitleLevels.TWO}>align </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(Alignable).map((align, index) => {
          return (
            <ColumnsItem key={index} size={12}>
              <Button variant='PRIMARY' align={align} fullwidth>
                {align}
              </Button>
            </ColumnsItem>
          )
        })}
      </Columns>
      <Separator />

      {/*  ======== icons ======== */}
      <Title level={TitleLevels.TWO}>iconName </Title>
      <Spacer size={10} />
      <Button iconName={IconName.ACCOMODATION} variant={'PRIMARY'}>Icon Primary</Button>
      <Button iconName={IconName.HOME} variant={'SECONDARY'}>Icon Primary</Button>
      <Button iconName={IconName.HOME} variant={'TERTIARY'}>Icon Primary</Button>
      <Button iconName={IconName.HOME} variant={'TERTIARY'}></Button>
      <Separator />

      {/*  ======== markup ======== */}
      <Title level={TitleLevels.TWO}>markup </Title>
      <Spacer size={10} />
      <Columns inlined>
        {Object.values(ButtonMarkup)
          .slice(20, 26)
          .map((mark, index) => {
            return (
              <ColumnsItem key={index} size={12}>
                <Button onClick={() => alert('Click on btn')} markup={mark} variant='PRIMARY' fullwidth to={'/test'}>
                  {mark}
                </Button>
              </ColumnsItem>
            )
          })}
      </Columns>
      <Separator />
    </AutoLayout>
  )
}
