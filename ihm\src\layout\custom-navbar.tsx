import {
  Columns,
  ColumnsItem,
  Image,
  Navbar,
  NavbarBrand,
  NavbarItem,
  NavbarLink,
  NavbarMenu,
  NavbarEnd,
  Title,
  TitleLevels,
} from "@bytel/trilogy-react-ts";
import BytelLogo from './logo-bouygues-telecom-mobile.svg';
import { Link } from "react-router-dom";
import "./style.scss";
import { FaUserCircle } from "react-icons/fa";
import { useAuth } from '../hooks/useAuth';

const CustomNavbar = () => {
  const { user, isAdmin } = useAuth();

  return (
    <Navbar className="single-line-navbar">
      <NavbarMenu>
        <NavbarBrand>
          <NavbarItem>
            <NavbarLink to={"/"} routerLink={Link}>
              <Columns flex verticalCentered>
                <Image
                  src={BytelLogo}
                  height={100}
                  width={100}
                />
                <ColumnsItem>
                  <Title level={TitleLevels.TWO}>CatIA</Title>
                </ColumnsItem>
              </Columns>
            </NavbarLink>
          </NavbarItem>
          <NavbarItem>
            <NavbarLink to={"/workspace"} routerLink={Link}>
              Espaces de travail
            </NavbarLink>
          </NavbarItem>
           {isAdmin && (
            <NavbarItem>
              <NavbarLink to={"/administration"} routerLink={Link}>
                Administration
              </NavbarLink>
            </NavbarItem>
          )}
        </NavbarBrand>
        <NavbarEnd>         
          <NavbarItem>
            <NavbarLink to={"/profile"} routerLink={Link}>
              <Columns flex verticalCentered>
                <FaUserCircle size={20} style={{ marginRight: '8px' }} />
                <span>Mon profil</span>
              </Columns>
            </NavbarLink>
          </NavbarItem>
        </NavbarEnd>
      </NavbarMenu>
    </Navbar>
  );
};

export default CustomNavbar;
