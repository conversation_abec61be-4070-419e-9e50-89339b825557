/**
 * Interface pour la création d'un utilisateur
 */
export interface UserCreateInput {
  login: string;
}

/**
 * Interface pour les données d'un utilisateur retourné par l'API
 */
export interface UserOutput {
  id: string;
  login: string;
  admin: boolean;
  date_inscription: string;
}

/**
 * Interface pour la liste des utilisateurs
 */
export interface UsersOutput {
  users: UserOutput[];
  total: number;
}