import uuid
from abc import ABC, abstractmethod
from collections.abc import Iterable

from .models import (
    TestCaseFilter,
    TestCaseInput,
    TestCaseStorage,
    TestCaseUpdate,
    TestImplementationFilter,
    TestImplementationInput,
    TestImplementationStorage,
)


class TestcasesRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dto: TestCaseInput) -> TestCaseStorage: ...
    @abstractmethod
    def update(self, dto: TestCaseUpdate) -> TestCaseStorage: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[TestCaseStorage]: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> TestCaseStorage | None: ...
    @abstractmethod
    def find(self, dto: TestCaseFilter) -> Iterable[TestCaseStorage] | None: ...


class TestImplementationsRepositoryInterface(ABC):
    @abstractmethod
    def add(self, dto: TestImplementationInput) -> TestImplementationStorage: ...
    @abstractmethod
    def update(self, dto: TestCaseUpdate) -> TestImplementationStorage: ...
    @abstractmethod
    def list(self, limit: int = None) -> Iterable[TestImplementationStorage]: ...
    @abstractmethod
    def get(self, entity_id: uuid.UUID) -> TestImplementationStorage | None: ...
    @abstractmethod
    def find(self, dto: TestImplementationFilter) -> Iterable[TestImplementationStorage] | None: ...
