import React, { useEffect } from 'react'
import {
  Section,
  Title,
  TitleLevels,
  Divider,
  InputType,
  InputStatus,
  AutoLayout,
  IconName,
  Input,
  Link,
} from '@bytel/trilogy-react-ts'

export const InputScreen = (): JSX.Element => {
  const [valueTextInput, setValueTextInput] = React.useState<string | undefined>()
  const [leavingDate, setLeavingDate] = React.useState('')

  useEffect(() => {
    setLeavingDate(leavingDate)
    console.log('leavingDate : ', leavingDate)
  }, [leavingDate])

  function formatMontant(value: string) {
    const tmpValue = value.replace(',', '.').replace(/[^0-9.]/g, '')
    const digits = tmpValue.split('.').filter((_, i) => i <= 1)
    let rightDigits = digits[1] || ''
    if (rightDigits.length > 2) rightDigits = rightDigits.substring(0, 2)
    return digits.join('.')
  }

  return (
    <Section>
      <Input
        onChange={(e) => {
          setValueTextInput(e.inputValue.toUpperCase())
        }}
        value={valueTextInput}
        placeholder={'Text input test'}
      />

      <Input
        type={InputType.TEXT}
        placeholder='Text test'
        name='text'
        value={'value'}
        onChange={(e) => console.log(e)}
      />

      <Input
        type={InputType.DATE}
        placeholder='Date de départ'
        name='leavingDate'
        value={leavingDate}
        onChange={(e) => setLeavingDate(e.inputValue)}
      />

      <Input accessibilityLabel={'input base'} placeholder={'Label input'} />

      <Input
        minLength={10}
        maxLength={12}
        onKeyPress={() => console.log('key')}
        hovered
        hasIcon
        defaultValue='Input, sans placeholder (et sans padding en haut)'
        help="N'affiche pas de padding supérieur quand il n'y a pas de placeholder"
        type={InputType.TEXT}
        onIconClick={() => {
          window.alert("wow ! comment t'as fait ?")
        }}
      />

      <Input
        hovered
        hasIcon
        defaultValue='Input, sans placeholder, et sans search'
        help="N'affiche pas de padding supérieur quand il n'y a pas de placeholder"
        type={InputType.TEXT}
        onIconClick={() => {
          window.alert('lol')
        }}
      />

      <Input
        hovered
        hasIcon
        defaultValue='Input, avec placeholder, et avec search'
        help='this is my help message'
        type={InputType.TEXT}
        onIconClick={() => {
          window.alert('lol')
        }}
        customIconLeft={IconName.STAR}
        placeholder='Placeholder avec search actif'
      />

      <Input
        hovered
        hasIcon
        defaultValue='Input, avec search ET customIcon'
        help="Le customIcon prévaut sur l'affichage du search afin de ne pas affiché 2 icône l'un au-dessus de l'autre"
        type={InputType.TEXT}
        onIconClick={() => {
          window.alert('lol')
        }}
        customIcon={IconName.STAR}
        placeholder='Placeholder avec search actif'
      />

      <Input
        hovered
        hasIcon
        defaultValue='My default input value'
        help='this is my help message'
        type={InputType.TEXT}
        status={InputStatus.SUCCESS}
        customIcon={IconName.STAR}
        onIconClick={() => {
          window.alert('lol')
        }}
        placeholder='This is my placeholder'
      />

      <Input
        hovered
        hasIcon
        defaultValue='My default input value'
        help='this is my help message'
        type={InputType.TEXT}
        patternValidator={new RegExp(/^hello/, 'i')}
        customIcon={IconName.STAR}
        onIconClick={() => {
          window.alert('lol')
        }}
        placeholder='Pattern start by hello'
      />

      <Input
        hovered
        hasIcon
        defaultValue='My default input value'
        help='this is my help message'
        type={InputType.TEXT}
        customValidator={(value) => (value === 'machin' ? InputStatus.SUCCESS : InputStatus.WARNING)}
        customIcon={IconName.STAR}
        onIconClick={() => {
          window.alert('lol')
        }}
        placeholder='Custom validator value="machin"'
      />

      <Input
        hovered
        hasIcon
        forceControl
        defaultValue='12'
        value={valueTextInput}
        status={InputStatus.SUCCESS}
        customIcon={IconName.EUROS}
        onIconClick={() => {
          window.alert('lol')
        }}
        placeholder='Forced control formatted normal input'
        onChange={(e) => {
          setValueTextInput(formatMontant(e.inputValue))
        }}
      />

      <AutoLayout>
        <Title level={TitleLevels.THREE}>Champs</Title>
        <Divider />

        <Input
          hovered
          hasIcon
          forceControl
          defaultValue='Input Success'
          value={valueTextInput}
          status={InputStatus.SUCCESS}
          customIcon={IconName.CHECK_CIRCLE}
          onIconClick={() => {
            window.alert('lol')
          }}
          placeholder='Placeholder in success input type'
          onChange={(e) => {
            setValueTextInput(formatMontant(e.inputValue))
          }}
        />

        <Input type={InputType.TEXT} placeholder='Input type texte' />
        <Input type={InputType.NUMBER} placeholder='Input type number' />
        <Input type={InputType.SEARCH} placeholder='Input type search' />
        <Input type={InputType.PASSWORD} placeholder='Input type password' />
        <Input type={InputType.DATE} placeholder='Input type date' />
        <Input type={InputType.EMAIL} placeholder='Input type mail' />

        <Input
          hasIcon
          help='Ce mot de passe ne respecte pas les règles de sécurité'
          type={InputType.TEXT}
          status={InputStatus.ERROR}
          customIcon={IconName.EXCLAMATION_CIRCLE}
          placeholder='This is an error message'
        />

        <Input
          hasIcon
          help='this is my help message'
          type={InputType.TEXT}
          placeholder='This is my placeholder'
        />

        <Input
          hasIcon
          help='this is my help message'
          type={InputType.TEXT}
          placeholder='This is my placeholder'
          customIconLeft={IconName.ADVISOR}
        />

        <Input
          type='password'
          hasIcon
          customIconLeft={IconName.LOCK}
          securityGauge
          help={<Link>1ère connexion / Mot de passe oublié ?</Link>}
          placeholder='this is my placeholder'
          minLength={8}
          maxLength={15}
          dataVerifies={{
            lowercase: true,
            uppercase: true,
            number: true,
            specialChars: true,
            length: { max: 4, min: 2 },
          }}
        />

        <Input type='password' help='test' placeholder='this is my placeholder' />
        <Input
          defaultValue='My 2nd default input value'
          help='this is my help message'
          type={InputType.TEXT}
          status={InputStatus.WARNING}
          customIcon={IconName.STAR}
          placeholder='This is my placeholder'
        />
      </AutoLayout>
    </Section>
  )
}
